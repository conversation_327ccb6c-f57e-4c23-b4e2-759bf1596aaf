"use client"

import type React from "react"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { ChevronLeft, Truck, Check } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// Mock cart data - in a real app, this would come from a cart context/store
const cartItems = [
  {
    id: "cantu-shea-butter",
    title: "Cantu Shea Butter Tea Tree Oil Leave-In Conditioner",
    price: 140000,
    discount: 15,
    quantity: 2,
    image: "/placeholder.svg?height=300&width=300",
  },
  {
    id: "suave-essentials",
    title: "Suave Essentials Gentle Body Wash Ocean Breeze",
    price: 145000,
    discount: 20,
    quantity: 1,
    image: "/placeholder.svg?height=300&width=300",
  },
  {
    id: "oral-b-electric",
    title: "Oral-B Pro 1000 Electric Toothbrush",
    price: 350000,
    discount: 10,
    quantity: 1,
    image: "/placeholder.svg?height=300&width=300",
  },
]

// Calculate cart totals
const subtotal = cartItems.reduce((total, item) => {
  const itemPrice = item.discount > 0 ? item.price - (item.price * item.discount) / 100 : item.price
  return total + itemPrice * item.quantity
}, 0)

const shipping = subtotal > 100000 ? 0 : 15000
const discount = 0 // No coupon applied in checkout
const total = subtotal + shipping - discount

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-US").format(amount)
}

export default function CheckoutPage() {
  const [activeStep, setActiveStep] = useState<"shipping" | "payment" | "confirmation">("shipping")
  const [shippingInfo, setShippingInfo] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    country: "Uganda",
    postalCode: "",
    saveInfo: false,
  })
  const [shippingMethod, setShippingMethod] = useState("standard")
  const [paymentMethod, setPaymentMethod] = useState("credit-card")

  const handleShippingSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setActiveStep("payment")
  }

  const handlePaymentSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setActiveStep("confirmation")
  }

  return (
    <div className="flex flex-col min-h-screen">
      <main className="flex-1">
        <div className="container py-8">
          <div className="flex items-center mb-8">
            <Link href="/cart" className="text-muted-foreground hover:text-foreground flex items-center">
              <ChevronLeft className="h-4 w-4 mr-1" />
              Back to Cart
            </Link>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <div className="mb-8">
                <div className="flex items-center justify-between mb-6">
                  <h1 className="text-2xl font-bold tracking-tight">Checkout</h1>
                  <div className="flex items-center">
                    <div
                      className={`flex items-center ${activeStep === "shipping" || activeStep === "payment" || activeStep === "confirmation" ? "text-primary" : "text-muted-foreground"}`}
                    >
                      <div className="w-8 h-8 rounded-full border-2 border-current flex items-center justify-center mr-2">
                        <span className="text-sm font-medium">1</span>
                      </div>
                      <span className="text-sm font-medium hidden sm:inline">Shipping</span>
                    </div>
                    <div
                      className={`w-8 h-0.5 mx-1 ${activeStep === "payment" || activeStep === "confirmation" ? "bg-primary" : "bg-muted"}`}
                    />
                    <div
                      className={`flex items-center ${activeStep === "payment" || activeStep === "confirmation" ? "text-primary" : "text-muted-foreground"}`}
                    >
                      <div className="w-8 h-8 rounded-full border-2 border-current flex items-center justify-center mr-2">
                        <span className="text-sm font-medium">2</span>
                      </div>
                      <span className="text-sm font-medium hidden sm:inline">Payment</span>
                    </div>
                    <div className={`w-8 h-0.5 mx-1 ${activeStep === "confirmation" ? "bg-primary" : "bg-muted"}`} />
                    <div
                      className={`flex items-center ${activeStep === "confirmation" ? "text-primary" : "text-muted-foreground"}`}
                    >
                      <div className="w-8 h-8 rounded-full border-2 border-current flex items-center justify-center mr-2">
                        <span className="text-sm font-medium">3</span>
                      </div>
                      <span className="text-sm font-medium hidden sm:inline">Confirmation</span>
                    </div>
                  </div>
                </div>

                {activeStep === "shipping" && (
                  <Card>
                    <CardContent className="p-6">
                      <form onSubmit={handleShippingSubmit}>
                        <h2 className="text-xl font-semibold mb-4">Shipping Information</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                          <div className="space-y-2">
                            <Label htmlFor="firstName">
                              First Name <span className="text-destructive">*</span>
                            </Label>
                            <Input
                              id="firstName"
                              value={shippingInfo.firstName}
                              onChange={(e) => setShippingInfo({ ...shippingInfo, firstName: e.target.value })}
                              required
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="lastName">
                              Last Name <span className="text-destructive">*</span>
                            </Label>
                            <Input
                              id="lastName"
                              value={shippingInfo.lastName}
                              onChange={(e) => setShippingInfo({ ...shippingInfo, lastName: e.target.value })}
                              required
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="email">
                              Email Address <span className="text-destructive">*</span>
                            </Label>
                            <Input
                              id="email"
                              type="email"
                              value={shippingInfo.email}
                              onChange={(e) => setShippingInfo({ ...shippingInfo, email: e.target.value })}
                              required
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="phone">
                              Phone Number <span className="text-destructive">*</span>
                            </Label>
                            <Input
                              id="phone"
                              value={shippingInfo.phone}
                              onChange={(e) => setShippingInfo({ ...shippingInfo, phone: e.target.value })}
                              required
                            />
                          </div>
                          <div className="space-y-2 md:col-span-2">
                            <Label htmlFor="address">
                              Address <span className="text-destructive">*</span>
                            </Label>
                            <Textarea
                              id="address"
                              value={shippingInfo.address}
                              onChange={(e) => setShippingInfo({ ...shippingInfo, address: e.target.value })}
                              required
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="city">
                              City <span className="text-destructive">*</span>
                            </Label>
                            <Input
                              id="city"
                              value={shippingInfo.city}
                              onChange={(e) => setShippingInfo({ ...shippingInfo, city: e.target.value })}
                              required
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="postalCode">Postal Code</Label>
                            <Input
                              id="postalCode"
                              value={shippingInfo.postalCode}
                              onChange={(e) => setShippingInfo({ ...shippingInfo, postalCode: e.target.value })}
                            />
                          </div>
                        </div>

                        <Separator className="my-6" />

                        <h2 className="text-xl font-semibold mb-4">Shipping Method</h2>
                        <RadioGroup value={shippingMethod} onValueChange={setShippingMethod} className="space-y-4">
                          <div className="flex items-center space-x-2 border rounded-lg p-4 cursor-pointer hover:border-primary transition-colors">
                            <RadioGroupItem value="standard" id="standard" />
                            <Label htmlFor="standard" className="flex-1 cursor-pointer">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <Truck className="h-5 w-5 text-muted-foreground" />
                                  <div>
                                    <p className="font-medium">Standard Shipping</p>
                                    <p className="text-sm text-muted-foreground">Delivery in 3-5 business days</p>
                                  </div>
                                </div>
                                <div className="font-medium">
                                  {shipping === 0 ? (
                                    <span className="text-green-600">Free</span>
                                  ) : (
                                    `UGX ${formatCurrency(shipping)}`
                                  )}
                                </div>
                              </div>
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2 border rounded-lg p-4 cursor-pointer hover:border-primary transition-colors">
                            <RadioGroupItem value="express" id="express" />
                            <Label htmlFor="express" className="flex-1 cursor-pointer">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <Truck className="h-5 w-5 text-muted-foreground" />
                                  <div>
                                    <p className="font-medium">Express Shipping</p>
                                    <p className="text-sm text-muted-foreground">Delivery in 1-2 business days</p>
                                  </div>
                                </div>
                                <div className="font-medium">UGX {formatCurrency(25000)}</div>
                              </div>
                            </Label>
                          </div>
                        </RadioGroup>

                        <div className="mt-8 flex justify-end">
                          <Button type="submit" size="lg">
                            Continue to Payment
                          </Button>
                        </div>
                      </form>
                    </CardContent>
                  </Card>
                )}

                {activeStep === "payment" && (
                  <Card>
                    <CardContent className="p-6">
                      <form onSubmit={handlePaymentSubmit}>
                        <h2 className="text-xl font-semibold mb-4">Payment Method</h2>
                        <Tabs defaultValue="card" className="w-full">
                          <TabsList className="grid grid-cols-3 mb-6">
                            <TabsTrigger value="card" onClick={() => setPaymentMethod("credit-card")}>
                              Credit Card
                            </TabsTrigger>
                            <TabsTrigger value="mobile" onClick={() => setPaymentMethod("mobile-money")}>
                              Mobile Money
                            </TabsTrigger>
                            <TabsTrigger value="paypal" onClick={() => setPaymentMethod("paypal")}>
                              PayPal
                            </TabsTrigger>
                          </TabsList>
                          <TabsContent value="card" className="space-y-4">
                            <div className="space-y-2">
                              <Label htmlFor="cardName">
                                Name on Card <span className="text-destructive">*</span>
                              </Label>
                              <Input id="cardName" placeholder="John Doe" required />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="cardNumber">
                                Card Number <span className="text-destructive">*</span>
                              </Label>
                              <div className="relative">
                                <Input id="cardNumber" placeholder="1234 5678 9012 3456" required />
                                <div className="absolute right-3 top-1/2 -translate-y-1/2 flex gap-2">
                                  <Image src="/placeholder.svg?height=20&width=30" alt="Visa" width={30} height={20} />
                                  <Image
                                    src="/placeholder.svg?height=20&width=30"
                                    alt="Mastercard"
                                    width={30}
                                    height={20}
                                  />
                                </div>
                              </div>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label htmlFor="expiry">
                                  Expiry Date <span className="text-destructive">*</span>
                                </Label>
                                <Input id="expiry" placeholder="MM/YY" required />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="cvv">
                                  CVV <span className="text-destructive">*</span>
                                </Label>
                                <Input id="cvv" placeholder="123" required />
                              </div>
                            </div>
                          </TabsContent>
                          <TabsContent value="mobile" className="space-y-4">
                            <div className="space-y-2">
                              <Label htmlFor="mobileNumber">
                                Mobile Money Number <span className="text-destructive">*</span>
                              </Label>
                              <Input id="mobileNumber" placeholder="+256 700 000 000" required />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="mobileProvider">
                                Provider <span className="text-destructive">*</span>
                              </Label>
                              <select
                                id="mobileProvider"
                                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                required
                              >
                                <option value="">Select Provider</option>
                                <option value="mtn">MTN Mobile Money</option>
                                <option value="airtel">Airtel Money</option>
                              </select>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              You will receive a prompt on your phone to confirm the payment.
                            </p>
                          </TabsContent>
                          <TabsContent value="paypal" className="space-y-4">
                            <div className="text-center py-6">
                              <Image
                                src="/placeholder.svg?height=60&width=120&text=PayPal"
                                alt="PayPal"
                                width={120}
                                height={60}
                                className="mx-auto mb-4"
                              />
                              <p className="text-sm text-muted-foreground mb-4">
                                You will be redirected to PayPal to complete your payment.
                              </p>
                              <Button type="button" variant="outline" className="mx-auto">
                                <span className="mr-2">Pay with</span>
                                <span className="font-bold">PayPal</span>
                              </Button>
                            </div>
                          </TabsContent>
                        </Tabs>

                        <Separator className="my-6" />

                        <h2 className="text-xl font-semibold mb-4">Billing Address</h2>
                        <div className="flex items-center space-x-2 mb-4">
                          <input
                            type="checkbox"
                            id="sameAsShipping"
                            className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                            defaultChecked
                          />
                          <Label htmlFor="sameAsShipping">Same as shipping address</Label>
                        </div>

                        <div className="mt-8 flex justify-between">
                          <Button type="button" variant="outline" onClick={() => setActiveStep("shipping")}>
                            Back to Shipping
                          </Button>
                          <Button type="submit" size="lg">
                            Complete Order
                          </Button>
                        </div>
                      </form>
                    </CardContent>
                  </Card>
                )}

                {activeStep === "confirmation" && (
                  <Card>
                    <CardContent className="p-6 text-center">
                      <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Check className="h-8 w-8 text-primary" />
                      </div>
                      <h2 className="text-2xl font-bold mb-2">Thank You for Your Order!</h2>
                      <p className="text-muted-foreground mb-6">
                        Your order has been placed and is being processed. You will receive an email confirmation
                        shortly.
                      </p>
                      <div className="bg-muted p-4 rounded-lg inline-block mb-6">
                        <p className="font-medium">
                          Order Number: <span className="text-primary">#STX12345678</span>
                        </p>
                      </div>
                      <div className="space-y-4 text-left mb-8">
                        <h3 className="font-semibold">Order Details:</h3>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <p className="text-muted-foreground">Shipping Address:</p>
                            <p>
                              {shippingInfo.firstName} {shippingInfo.lastName}
                            </p>
                            <p>{shippingInfo.address}</p>
                            <p>
                              {shippingInfo.city}, {shippingInfo.country} {shippingInfo.postalCode}
                            </p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Payment Method:</p>
                            <p>
                              {paymentMethod === "credit-card"
                                ? "Credit Card"
                                : paymentMethod === "mobile-money"
                                  ? "Mobile Money"
                                  : "PayPal"}
                            </p>
                            <p className="text-muted-foreground mt-2">Shipping Method:</p>
                            <p>{shippingMethod === "standard" ? "Standard Shipping" : "Express Shipping"}</p>
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <Button asChild>
                          <Link href="/">Continue Shopping</Link>
                        </Button>
                        <Button variant="outline" asChild>
                          <Link href="/account/orders">View Order Status</Link>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>

            <div>
              <Card>
                <CardContent className="p-6">
                  <h2 className="text-xl font-bold mb-4">Order Summary</h2>
                  <div className="space-y-4 mb-6">
                    {cartItems.map((item) => {
                      const itemPrice = item.discount > 0 ? item.price - (item.price * item.discount) / 100 : item.price

                      return (
                        <div key={item.id} className="flex gap-3">
                          <div className="relative w-16 h-16 rounded-md overflow-hidden bg-muted/20 flex-shrink-0">
                            <Image
                              src={item.image || "/placeholder.svg"}
                              alt={item.title}
                              fill
                              className="object-cover"
                            />
                            <div className="absolute top-0 right-0 bg-background rounded-full w-5 h-5 flex items-center justify-center text-xs -mt-1 -mr-1 border">
                              {item.quantity}
                            </div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="text-sm font-medium line-clamp-2">{item.title}</h3>
                            <p className="text-sm text-muted-foreground">
                              UGX {formatCurrency(itemPrice)}{" "}
                              {item.discount > 0 && (
                                <span className="text-xs line-through">UGX {formatCurrency(item.price)}</span>
                              )}
                            </p>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                  <Separator className="mb-4" />
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Subtotal</span>
                      <span>UGX {formatCurrency(subtotal)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Shipping</span>
                      <span>
                        {shipping === 0 ? (
                          <span className="text-green-600">Free</span>
                        ) : (
                          `UGX ${formatCurrency(shipping)}`
                        )}
                      </span>
                    </div>
                    {discount > 0 && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Discount</span>
                        <span className="text-destructive">-UGX {formatCurrency(discount)}</span>
                      </div>
                    )}
                    <Separator />
                    <div className="flex justify-between font-bold text-lg">
                      <span>Total</span>
                      <span>UGX {formatCurrency(total)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}

