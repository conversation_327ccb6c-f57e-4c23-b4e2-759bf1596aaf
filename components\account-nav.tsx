"use client"
import {
  User,
  Package,
  Heart,
  CreditCard,
  MapPin,
  Bell,
  LogOut,
  Home,
  ShoppingCart,
  Gift,
  Headphones,
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"

interface AccountNavProps {
  activeTab: string
  setActiveTab: (tab: string) => void
}

export function AccountNav({ activeTab, setActiveTab }: AccountNavProps) {
  const navItems = [
    { id: "dashboard", label: "Dashboard", icon: <Home className="h-5 w-5" /> },
    { id: "orders", label: "Orders", icon: <Package className="h-5 w-5" />, badge: 2 },
    { id: "wishlist", label: "Wishlist", icon: <Heart className="h-5 w-5" />, badge: 12 },
    { id: "profile", label: "Profile", icon: <User className="h-5 w-5" /> },
    { id: "addresses", label: "Addresses", icon: <MapPin className="h-5 w-5" /> },
    { id: "payments", label: "Payment Methods", icon: <CreditCard className="h-5 w-5" /> },
    { id: "notifications", label: "Notifications", icon: <Bell className="h-5 w-5" />, badge: 3 },
    { id: "returns", label: "Returns & Refunds", icon: <ShoppingCart className="h-5 w-5" /> },
    { id: "gift-cards", label: "Gift Cards", icon: <Gift className="h-5 w-5" /> },
    { id: "support", label: "Support", icon: <Headphones className="h-5 w-5" /> },
  ]

  return (
    <Card className="overflow-hidden shadow-sm border">
      <div className="p-6 bg-primary/5 border-b">
        <div className="flex items-center gap-3">
          <Avatar className="h-12 w-12 border-2 border-primary/20">
            <AvatarImage src="/placeholder.svg?height=48&width=48" alt="John Doe" />
            <AvatarFallback>JD</AvatarFallback>
          </Avatar>
          <div>
            <h2 className="font-bold text-lg">John Doe</h2>
            <p className="text-sm text-muted-foreground"><EMAIL></p>
          </div>
        </div>
      </div>
      <nav className="p-2">
        <ul className="space-y-1">
          {navItems.map((item) => (
            <li key={item.id}>
              <button
                onClick={() => setActiveTab(item.id)}
                className={`w-full flex items-center justify-between px-4 py-2.5 rounded-md text-sm transition-colors ${
                  activeTab === item.id
                    ? "bg-primary text-primary-foreground font-medium"
                    : "hover:bg-muted text-muted-foreground hover:text-foreground"
                }`}
              >
                <span className="flex items-center gap-3">
                  {item.icon}
                  <span>{item.label}</span>
                </span>
                {item.badge && (
                  <Badge
                    variant={activeTab === item.id ? "outline" : "secondary"}
                    className={activeTab === item.id ? "bg-primary-foreground text-primary" : ""}
                  >
                    {item.badge}
                  </Badge>
                )}
              </button>
            </li>
          ))}
        </ul>
        <div className="pt-4 mt-4 border-t">
          <Button
            variant="ghost"
            className="w-full justify-start text-destructive hover:text-destructive hover:bg-destructive/10"
          >
            <LogOut className="h-5 w-5 mr-3" />
            Logout
          </Button>
        </div>
      </nav>
    </Card>
  )
}

