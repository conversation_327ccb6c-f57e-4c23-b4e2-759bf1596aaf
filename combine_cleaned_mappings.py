import pandas as pd

# File paths
CLEANED_01 = "image_mapping_01_cleaned.csv"
CLEANED_02 = "image_mapping_02_cleaned.csv"
COMBINED = "image_mapping_combined.csv"

# Read both cleaned mapping files
df1 = pd.read_csv(CLEANED_01)
df2 = pd.read_csv(CLEANED_02)

# Combine and reset index
combined = pd.concat([df1, df2], ignore_index=True)
combined.to_csv(COMBINED, index=False)

print(f"Combined mapping saved to {COMBINED}. {len(combined)} total valid images.")
