"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { SearchIcon, X } from "lucide-react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import Link from "next/link"

import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"

// Mock product data for search - in a real app, this would come from an API
const products = [
  {
    id: "cantu-shea-butter",
    title: "Cantu Shea Butter Tea Tree Oil Leave-In Conditioner",
    price: 140000,
    image: "/placeholder.svg?height=300&width=300",
    category: "Beauty & Health",
  },
  {
    id: "suave-essentials",
    title: "Suave Essentials Gentle Body Wash Ocean Breeze",
    price: 145000,
    image: "/placeholder.svg?height=300&width=300",
    category: "Beauty & Health",
  },
  {
    id: "aveeno-pure",
    title: "Aveeno Pure Flaming Body Wash with Oatmeal",
    price: 135000,
    image: "/placeholder.svg?height=300&width=300",
    category: "Beauty & Health",
  },
  {
    id: "oral-b-electric",
    title: "Oral-B Pro 1000 Electric Toothbrush",
    price: 350000,
    image: "/placeholder.svg?height=300&width=300",
    category: "Oral Hygiene",
  },
  {
    id: "colgate-total",
    title: "Colgate Total Whitening Toothpaste",
    price: 25000,
    image: "/placeholder.svg?height=300&width=300",
    category: "Oral Hygiene",
  },
  {
    id: "egyptian-pillow",
    title: "Egyptian Cotton King Size Pillow",
    price: 185000,
    image: "/placeholder.svg?height=300&width=300",
    category: "Beddings",
  },
  {
    id: "bamboo-duvet",
    title: "Bamboo Duvet Cover Queen Size",
    price: 450000,
    image: "/placeholder.svg?height=300&width=300",
    category: "Beddings",
  },
]

export function Search() {
  const [searchQuery, setSearchQuery] = useState("")
  const [searchResults, setSearchResults] = useState<typeof products>([])
  const [isSearching, setIsSearching] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const searchRef = useRef<HTMLDivElement>(null)
  const router = useRouter()

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US").format(amount)
  }

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    setSearchQuery(query)

    if (query.length > 1) {
      setIsSearching(true)
      // Simulate API search delay
      setTimeout(() => {
        const filtered = products.filter(
          (product) =>
            product.title.toLowerCase().includes(query.toLowerCase()) ||
            product.category.toLowerCase().includes(query.toLowerCase()),
        )
        setSearchResults(filtered)
        setIsSearching(false)
        setShowResults(true)
      }, 300)
    } else {
      setSearchResults([])
      setShowResults(false)
    }
  }

  // Handle search form submission
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      setShowResults(false)
      router.push(`/search?q=${encodeURIComponent(searchQuery)}`)
    }
  }

  // Handle click outside to close search results
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  return (
    <div className="relative" ref={searchRef}>
      <form onSubmit={handleSearchSubmit} className="relative">
        <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search products..."
          className="w-[200px] lg:w-[300px] pl-8 pr-8"
          value={searchQuery}
          onChange={handleSearchChange}
        />
        {searchQuery && (
          <Button
            type="button"
            variant="ghost"
            size="icon"
            className="absolute right-0 top-0 h-9 w-9"
            onClick={() => {
              setSearchQuery("")
              setSearchResults([])
              setShowResults(false)
            }}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </form>

      {showResults && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-background border rounded-md shadow-lg z-50 max-h-[400px] overflow-y-auto">
          {isSearching ? (
            <div className="p-4 text-center text-muted-foreground">
              <div className="animate-spin h-5 w-5 border-2 border-primary border-t-transparent rounded-full mx-auto mb-2"></div>
              Searching...
            </div>
          ) : searchResults.length > 0 ? (
            <div>
              <div className="p-2 border-b">
                <p className="text-xs text-muted-foreground">
                  {searchResults.length} result{searchResults.length !== 1 ? "s" : ""} for "{searchQuery}"
                </p>
              </div>
              <ul>
                {searchResults.map((product) => (
                  <li key={product.id} className="border-b last:border-b-0">
                    <Link
                      href={`/products/${product.id}`}
                      className="flex items-center p-2 hover:bg-muted transition-colors"
                      onClick={() => setShowResults(false)}
                    >
                      <div className="w-10 h-10 rounded overflow-hidden bg-muted/20 flex-shrink-0">
                        <Image
                          src={product.image || "/placeholder.svg"}
                          alt={product.title}
                          width={40}
                          height={40}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="ml-3 flex-1 min-w-0">
                        <p className="text-sm font-medium line-clamp-1">{product.title}</p>
                        <p className="text-xs text-muted-foreground">{product.category}</p>
                      </div>
                      <div className="ml-2 text-sm font-medium">UGX {formatCurrency(product.price)}</div>
                    </Link>
                  </li>
                ))}
              </ul>
              <div className="p-2 border-t">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full text-primary"
                  onClick={() => {
                    setShowResults(false)
                    router.push(`/search?q=${encodeURIComponent(searchQuery)}`)
                  }}
                >
                  View all results
                </Button>
              </div>
            </div>
          ) : (
            <div className="p-4 text-center text-muted-foreground">No products found for "{searchQuery}"</div>
          )}
        </div>
      )}
    </div>
  )
}

