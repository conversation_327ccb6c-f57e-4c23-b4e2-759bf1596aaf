import { ProductCard } from "@/components/product-card"

export function ProductGrid() {
  const products = [
    {
      id: 1,
      title: "Dove Body Wash with Deep Moisture",
      price: 110000,
      currency: "UGX",
      image: "/placeholder.svg?height=300&width=300",
      discount: 10,
      rating: 4.8,
      href: "/products/dove-body-wash",
      badge: "Best Seller",
    },
    {
      id: 2,
      title: "Suave Essentials Gentle Body Wash Ocean Breeze",
      price: 145000,
      currency: "UGX",
      image: "/placeholder.svg?height=300&width=300",
      discount: 0,
      rating: 4.2,
      href: "/products/suave-essentials",
    },
    {
      id: 3,
      title: "Cantu Shea Butter Tea Tree Oil Leave-In Conditioner",
      price: 140000,
      currency: "UGX",
      image: "/placeholder.svg?height=300&width=300",
      discount: 15,
      rating: 4.5,
      href: "/products/cantu-body-wash",
    },
    {
      id: 4,
      title: "Wild Cherry Blossom Body Wash with Vitamin E",
      price: 160000,
      currency: "UGX",
      image: "/placeholder.svg?height=300&width=300",
      discount: 0,
      rating: 4.7,
      href: "/products/wild-cherry",
      badge: "New",
    },
    {
      id: 5,
      title: "Olay Moisture Body Wash with Shea Butter",
      price: 155000,
      currency: "UGX",
      image: "/placeholder.svg?height=300&width=300",
      discount: 5,
      rating: 4.6,
      href: "/products/olay-moisture",
    },
    {
      id: 6,
      title: "Oral-B Pro 1000 Electric Toothbrush",
      price: 350000,
      currency: "UGX",
      image: "/placeholder.svg?height=300&width=300",
      discount: 10,
      rating: 4.9,
      href: "/products/oral-b-electric",
    },
    {
      id: 7,
      title: "Colgate Total Whitening Toothpaste",
      price: 25000,
      currency: "UGX",
      image: "/placeholder.svg?height=300&width=300",
      discount: 0,
      rating: 4.7,
      href: "/products/colgate-total",
    },
    {
      id: 8,
      title: "Sensodyne Pronamel Gentle Whitening Toothpaste",
      price: 30000,
      currency: "UGX",
      image: "/placeholder.svg?height=300&width=300",
      discount: 5,
      rating: 4.8,
      href: "/products/sensodyne",
    },
    {
      id: 9,
      title: "Oral-B Glide Pro-Health Comfort Plus Dental Floss",
      price: 28000,
      currency: "UGX",
      image: "/placeholder.svg?height=300&width=300",
      discount: 0,
      rating: 4.6,
      href: "/products/oral-b-paste",
    },
    {
      id: 10,
      title: "Listerine Cool Mint Antiseptic Mouthwash",
      price: 45000,
      currency: "UGX",
      image: "/placeholder.svg?height=300&width=300",
      discount: 15,
      rating: 4.5,
      href: "/products/listerine",
      badge: "Hot",
    },
    {
      id: 11,
      title: "Egyptian Cotton King Size Pillow",
      price: 185000,
      currency: "UGX",
      image: "/placeholder.svg?height=300&width=300",
      discount: 0,
      rating: 4.8,
      href: "/products/egyptian-pillow",
    },
    {
      id: 12,
      title: "Bamboo Duvet Cover Queen Size",
      price: 450000,
      currency: "UGX",
      image: "/placeholder.svg?height=300&width=300",
      discount: 10,
      rating: 4.9,
      href: "/products/bamboo-duvet",
    },
  ]

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {products.map((product) => (
        <ProductCard
          key={product.id}
          title={product.title}
          price={product.price}
          currency={product.currency}
          image={product.image}
          discount={product.discount}
          rating={product.rating}
          href={product.href}
          badge={product.badge}
        />
      ))}
    </div>
  )
}

