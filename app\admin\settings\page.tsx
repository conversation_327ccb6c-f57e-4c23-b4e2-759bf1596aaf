import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { AdminSettingsGeneral } from "@/components/admin/admin-settings-general"
import { AdminSettingsAppearance } from "@/components/admin/admin-settings-appearance"
import { AdminSettingsUsers } from "@/components/admin/admin-settings-users"
import { AdminSettingsNotifications } from "@/components/admin/admin-settings-notifications"
import { AdminSettingsSecurity } from "@/components/admin/admin-settings-security"

export default function SettingsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground mt-2">Manage your store settings and preferences.</p>
      </div>

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="bg-muted/50 p-1">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="appearance">Appearance</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <AdminSettingsGeneral />
        </TabsContent>

        <TabsContent value="appearance" className="space-y-6">
          <AdminSettingsAppearance />
        </TabsContent>

        <TabsContent value="users" className="space-y-6">
          <AdminSettingsUsers />
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <AdminSettingsNotifications />
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <AdminSettingsSecurity />
        </TabsContent>
      </Tabs>
    </div>
  )
}

