import Image from "next/image"
import { <PERSON>, <PERSON> } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function AboutPage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-muted">
        <div className="absolute inset-0 z-0">
          <Image
            src="/placeholder.svg?height=400&width=1600"
            alt="About Us Banner"
            fill
            className="object-cover opacity-20"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-background via-background/80 to-background/20" />
        </div>
        <div className="container relative z-10 py-16 md:py-24">
          <div className="max-w-2xl space-y-5">
            <div className="inline-block rounded-full bg-primary/10 px-3 py-1 text-sm text-primary">About STXpress</div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight">
              Your Trusted Partner for Quality Products
            </h1>
            <p className="text-lg text-muted-foreground">
              We're dedicated to bringing you the best personal care, beauty, and home essentials at affordable prices.
            </p>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="container py-16 md:py-24">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-3xl font-bold tracking-tight mb-6">Our Story</h2>
            <div className="space-y-4 text-muted-foreground">
              <p>
                Founded in 2015, STXpress began with a simple mission: to provide high-quality personal care and home
                products to customers in Uganda at affordable prices.
              </p>
              <p>
                What started as a small family business has grown into one of the leading retailers in the region,
                serving thousands of satisfied customers every month.
              </p>
              <p>
                Our journey has been driven by our commitment to quality, customer satisfaction, and community
                engagement. We believe that everyone deserves access to premium products that enhance their daily lives.
              </p>
            </div>
            <div className="mt-8 grid grid-cols-2 gap-4">
              <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                <span className="text-3xl font-bold text-primary">10+</span>
                <span className="text-sm text-muted-foreground">Years of Experience</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                <span className="text-3xl font-bold text-primary">50k+</span>
                <span className="text-sm text-muted-foreground">Happy Customers</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                <span className="text-3xl font-bold text-primary">5k+</span>
                <span className="text-sm text-muted-foreground">Products</span>
              </div>
              <div className="flex flex-col items-center p-4 bg-muted rounded-lg">
                <span className="text-3xl font-bold text-primary">24/7</span>
                <span className="text-sm text-muted-foreground">Customer Support</span>
              </div>
            </div>
          </div>
          <div className="relative">
            <div className="absolute -top-4 -left-4 w-24 h-24 bg-primary/10 rounded-full z-0" />
            <div className="absolute -bottom-4 -right-4 w-32 h-32 bg-primary/10 rounded-full z-0" />
            <div className="relative z-10 rounded-lg overflow-hidden">
              <Image
                src="/placeholder.svg?height=600&width=800"
                alt="Our Story"
                width={800}
                height={600}
                className="w-full h-auto"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Values Section */}
      <section className="bg-muted/40 py-16 md:py-24">
        <div className="container">
          <div className="text-center max-w-3xl mx-auto mb-12">
            <h2 className="text-3xl font-bold tracking-tight mb-4">Our Mission & Values</h2>
            <p className="text-muted-foreground">
              At STXpress, we're guided by a set of core values that define who we are and how we operate.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="bg-background border-primary/10">
              <CardContent className="pt-6">
                <div className="rounded-full bg-primary/10 p-3 w-12 h-12 flex items-center justify-center mb-4">
                  <Check className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-bold mb-2">Quality First</h3>
                <p className="text-muted-foreground">
                  We never compromise on quality. Every product in our inventory undergoes rigorous quality checks to
                  ensure it meets our high standards.
                </p>
              </CardContent>
            </Card>
            <Card className="bg-background border-primary/10">
              <CardContent className="pt-6">
                <div className="rounded-full bg-primary/10 p-3 w-12 h-12 flex items-center justify-center mb-4">
                  <Check className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-bold mb-2">Customer Satisfaction</h3>
                <p className="text-muted-foreground">
                  Our customers are at the heart of everything we do. We strive to exceed expectations and provide an
                  exceptional shopping experience.
                </p>
              </CardContent>
            </Card>
            <Card className="bg-background border-primary/10">
              <CardContent className="pt-6">
                <div className="rounded-full bg-primary/10 p-3 w-12 h-12 flex items-center justify-center mb-4">
                  <Check className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-bold mb-2">Community Impact</h3>
                <p className="text-muted-foreground">
                  We believe in giving back to the communities we serve. Through various initiatives, we support local
                  causes and promote sustainable practices.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="container py-16 md:py-24">
        <div className="text-center max-w-3xl mx-auto mb-12">
          <h2 className="text-3xl font-bold tracking-tight mb-4">Meet Our Team</h2>
          <p className="text-muted-foreground">
            The passionate individuals behind STXpress who work tirelessly to bring you the best products and services.
          </p>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((member) => (
            <div key={member} className="text-center">
              <div className="rounded-full overflow-hidden w-32 h-32 mx-auto mb-4">
                <Image
                  src={`/placeholder.svg?height=200&width=200&text=Team Member ${member}`}
                  alt={`Team Member ${member}`}
                  width={200}
                  height={200}
                  className="object-cover w-full h-full"
                />
              </div>
              <h3 className="font-bold">John Doe</h3>
              <p className="text-sm text-muted-foreground">Co-Founder & CEO</p>
            </div>
          ))}
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="bg-muted/40 py-16 md:py-24">
        <div className="container">
          <div className="text-center max-w-3xl mx-auto mb-12">
            <h2 className="text-3xl font-bold tracking-tight mb-4">What Our Customers Say</h2>
            <p className="text-muted-foreground">
              Don't just take our word for it. Here's what our customers have to say about their experience with
              STXpress.
            </p>
          </div>
          <Tabs defaultValue="testimonial1" className="w-full">
            <TabsList className="grid grid-cols-3 max-w-md mx-auto">
              <TabsTrigger value="testimonial1">Sarah M.</TabsTrigger>
              <TabsTrigger value="testimonial2">David K.</TabsTrigger>
              <TabsTrigger value="testimonial3">Lisa T.</TabsTrigger>
            </TabsList>
            <TabsContent value="testimonial1" className="mt-8">
              <div className="max-w-2xl mx-auto text-center">
                <div className="flex justify-center mb-4">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star key={star} className="h-5 w-5 fill-primary text-primary" />
                  ))}
                </div>
                <blockquote className="text-xl italic mb-4">
                  "I've been shopping with STXpress for over 3 years now, and I'm always impressed by their product
                  quality and customer service. The delivery is always on time, and their range of products is amazing!"
                </blockquote>
                <p className="font-semibold">Sarah M.</p>
                <p className="text-sm text-muted-foreground">Loyal Customer since 2020</p>
              </div>
            </TabsContent>
            <TabsContent value="testimonial2" className="mt-8">
              <div className="max-w-2xl mx-auto text-center">
                <div className="flex justify-center mb-4">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star key={star} className="h-5 w-5 fill-primary text-primary" />
                  ))}
                </div>
                <blockquote className="text-xl italic mb-4">
                  "The variety of products available at STXpress is unmatched. I can find everything I need for my home
                  and personal care in one place. Their prices are competitive, and the quality is top-notch."
                </blockquote>
                <p className="font-semibold">David K.</p>
                <p className="text-sm text-muted-foreground">Regular Shopper</p>
              </div>
            </TabsContent>
            <TabsContent value="testimonial3" className="mt-8">
              <div className="max-w-2xl mx-auto text-center">
                <div className="flex justify-center mb-4">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star key={star} className="h-5 w-5 fill-primary text-primary" />
                  ))}
                </div>
                <blockquote className="text-xl italic mb-4">
                  "What sets STXpress apart is their attention to detail and commitment to customer satisfaction. When I
                  had an issue with an order, their support team resolved it promptly and professionally."
                </blockquote>
                <p className="font-semibold">Lisa T.</p>
                <p className="text-sm text-muted-foreground">Happy Customer</p>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container py-16 md:py-24">
        <div className="bg-primary/10 rounded-2xl p-8 md:p-12">
          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div>
              <h2 className="text-3xl font-bold tracking-tight mb-4">Ready to Experience STXpress?</h2>
              <p className="text-muted-foreground mb-6">
                Join thousands of satisfied customers and discover our wide range of high-quality products at
                competitive prices.
              </p>
              <div className="flex flex-wrap gap-4">
                <Button size="lg">Shop Now</Button>
                <Button variant="outline" size="lg">
                  Contact Us
                </Button>
              </div>
            </div>
            <div className="relative">
              <Image
                src="/placeholder.svg?height=400&width=600"
                alt="Shopping Experience"
                width={600}
                height={400}
                className="rounded-lg"
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

