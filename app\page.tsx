import Image from "next/image"
import Link from "next/link"
import { ChevronR<PERSON>, <PERSON>R<PERSON> } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { ProductCard } from "@/components/product-card"
import { CategoryCard } from "@/components/category-card"
import { CountdownTimer } from "@/components/countdown-timer"
import { Newsletter } from "@/components/newsletter"
import { FeaturedBrands } from "@/components/featured-brands"
import { HeroCarousel } from "@/components/hero-carousel"

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      <main className="flex-1">
        <section className="py-6">
          <HeroCarousel />
        </section>

        <section className="container py-8 md:py-12">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-8">
            <div className="flex flex-col items-center gap-2 p-4 text-center rounded-lg border bg-card">
              <div className="rounded-full bg-primary/10 p-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-6 w-6 text-primary"
                >
                  <path d="M5 12.55a11 11 0 0 1 14.08 0" />
                  <path d="M1.42 9a16 16 0 0 1 21.16 0" />
                  <path d="M8.53 16.11a6 6 0 0 1 6.95 0" />
                  <line x1="12" x2="12.01" y1="20" y2="20" />
                </svg>
              </div>
              <h3 className="font-medium">Free Delivery</h3>
              <p className="text-xs text-muted-foreground">For all orders over $50</p>
            </div>
            <div className="flex flex-col items-center gap-2 p-4 text-center rounded-lg border bg-card">
              <div className="rounded-full bg-primary/10 p-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-6 w-6 text-primary"
                >
                  <path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8" />
                  <path d="M3 3v5h5" />
                  <path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16" />
                  <path d="M16 16h5v5" />
                </svg>
              </div>
              <h3 className="font-medium">60 Days Return</h3>
              <p className="text-xs text-muted-foreground">If goods have problems</p>
            </div>
            <div className="flex flex-col items-center gap-2 p-4 text-center rounded-lg border bg-card">
              <div className="rounded-full bg-primary/10 p-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-6 w-6 text-primary"
                >
                  <rect width="18" height="11" x="3" y="11" rx="2" ry="2" />
                  <path d="M7 11V7a5 5 0 0 1 10 0v4" />
                </svg>
              </div>
              <h3 className="font-medium">Secure Payment</h3>
              <p className="text-xs text-muted-foreground">100% secure payment</p>
            </div>
            <div className="flex flex-col items-center gap-2 p-4 text-center rounded-lg border bg-card">
              <div className="rounded-full bg-primary/10 p-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-6 w-6 text-primary"
                >
                  <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z" />
                </svg>
              </div>
              <h3 className="font-medium">24/7 Support</h3>
              <p className="text-xs text-muted-foreground">Dedicated support</p>
            </div>
          </div>
        </section>

        <section className="container py-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold tracking-tight">Featured Categories</h2>
            <Link href="/categories" className="flex items-center text-sm font-medium text-primary">
              View All Categories
              <ChevronRight className="ml-1 h-4 w-4" />
            </Link>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <CategoryCard
              title="Beauty & Health"
              image="/placeholder.svg?height=200&width=200"
              href="/categories/beauty-health"
              count={120}
            />
            <CategoryCard
              title="Oral Hygiene"
              image="/placeholder.svg?height=200&width=200"
              href="/categories/oral-hygiene"
              count={45}
            />
            <CategoryCard
              title="Beddings"
              image="/placeholder.svg?height=200&width=200"
              href="/categories/beddings"
              count={78}
            />
            <CategoryCard
              title="Footwear"
              image="/placeholder.svg?height=200&width=200"
              href="/categories/footwear"
              count={96}
            />
            <CategoryCard
              title="Perfumes"
              image="/placeholder.svg?height=200&width=200"
              href="/categories/perfumes"
              count={64}
            />
            <CategoryCard
              title="Clothing"
              image="/placeholder.svg?height=200&width=200"
              href="/categories/clothing"
              count={150}
            />
          </div>
        </section>

        <section className="container py-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <h2 className="text-2xl font-bold tracking-tight">Deals of The Day</h2>
              <CountdownTimer targetDate="2025-04-30T00:00:00" />
            </div>
            <Link href="/deals" className="flex items-center text-sm font-medium text-primary">
              View All Deals
              <ChevronRight className="ml-1 h-4 w-4" />
            </Link>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            <ProductCard
              title="Cantu Shea Butter Tea Tree Oil Leave-In Conditioner"
              price={140000}
              currency="UGX"
              image="/placeholder.svg?height=300&width=300"
              discount={15}
              rating={4.5}
              href="/products/cantu-shea-butter"
              id="cantu-shea-butter"
            />
            <ProductCard
              title="Suave Essentials Gentle Body Wash Ocean Breeze"
              price={145000}
              currency="UGX"
              image="/placeholder.svg?height=300&width=300"
              discount={20}
              rating={4.2}
              href="/products/suave-essentials"
              id="suave-essentials"
            />
            <ProductCard
              title="Aveeno Pure Flaming Body Wash with Oatmeal"
              price={135000}
              currency="UGX"
              image="/placeholder.svg?height=300&width=300"
              discount={10}
              rating={4.7}
              href="/products/aveeno-pure"
              id="aveeno-pure"
            />
            <ProductCard
              title="Herbal Essentials Hydrating Shampoo with Aloe Vera"
              price={150000}
              currency="UGX"
              image="/placeholder.svg?height=300&width=300"
              discount={25}
              rating={4.3}
              href="/products/herbal-essentials"
              id="herbal-essentials"
            />
            <ProductCard
              title="Neutrogena Revitalizing Body Wash with Vitamin E"
              price={125000}
              currency="UGX"
              image="/placeholder.svg?height=300&width=300"
              discount={15}
              rating={4.6}
              href="/products/neutrogena-revitalizing"
              id="neutrogena-revitalizing"
            />
          </div>
        </section>

        <section className="container py-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="relative overflow-hidden rounded-lg">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/80 to-primary/40 z-10"></div>
              <Image
                src="/placeholder.svg?height=400&width=600"
                alt="Running shoes promotion"
                width={600}
                height={400}
                className="w-full h-[300px] object-cover"
              />
              <div className="absolute top-0 left-0 p-8 z-20">
                <p className="text-white text-sm font-medium mb-2">Limited Time Offer</p>
                <h3 className="text-white text-3xl font-bold mb-2">Running Shoes</h3>
                <p className="text-white/90 mb-4">Up to 30% off on selected items</p>
                <Button variant="secondary" size="sm">
                  Shop Now
                </Button>
              </div>
            </div>
            <div className="relative overflow-hidden rounded-lg">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/80 to-primary/40 z-10"></div>
              <Image
                src="/placeholder.svg?height=400&width=600"
                alt="Perfumes promotion"
                width={600}
                height={400}
                className="w-full h-[300px] object-cover"
              />
              <div className="absolute top-0 left-0 p-8 z-20">
                <p className="text-white text-sm font-medium mb-2">New Collection</p>
                <h3 className="text-white text-3xl font-bold mb-2">Top Perfumes</h3>
                <p className="text-white/90 mb-4">Discover luxury fragrances</p>
                <Button variant="secondary" size="sm">
                  Shop Now
                </Button>
              </div>
            </div>
          </div>
        </section>

        <section className="container py-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold tracking-tight">Popular Categories</h2>
          </div>
          <Tabs defaultValue="beauty" className="w-full">
            <TabsList className="mb-6">
              <TabsTrigger value="beauty">Beauty & Health</TabsTrigger>
              <TabsTrigger value="oral">Oral Hygiene</TabsTrigger>
              <TabsTrigger value="bedding">Beddings</TabsTrigger>
            </TabsList>
            <TabsContent value="beauty" className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4">
                <ProductCard
                  title="Dove Body Wash"
                  price={110000}
                  currency="UGX"
                  image="/placeholder.svg?height=300&width=300"
                  discount={10}
                  rating={4.8}
                  href="/products/dove-body-wash"
                  id="dove-body-wash"
                />
                <ProductCard
                  title="Suave Essentials Gentle Body Wash"
                  price={145000}
                  currency="UGX"
                  image="/placeholder.svg?height=300&width=300"
                  discount={0}
                  rating={4.2}
                  href="/products/suave-essentials"
                  id="suave-essentials-2"
                />
                <ProductCard
                  title="Cantu Body Wash"
                  price={140000}
                  currency="UGX"
                  image="/placeholder.svg?height=300&width=300"
                  discount={15}
                  rating={4.5}
                  href="/products/cantu-body-wash"
                  id="cantu-body-wash"
                />
                <ProductCard
                  title="Wild Cherry Blossom Body Wash"
                  price={160000}
                  currency="UGX"
                  image="/placeholder.svg?height=300&width=300"
                  discount={0}
                  rating={4.7}
                  href="/products/wild-cherry"
                  id="wild-cherry"
                />
                <ProductCard
                  title="Olay Moisture Body Wash"
                  price={155000}
                  currency="UGX"
                  image="/placeholder.svg?height=300&width=300"
                  discount={5}
                  rating={4.6}
                  href="/products/olay-moisture"
                  id="olay-moisture"
                />
              </div>
              <div className="flex justify-center mt-8">
                <Button variant="outline" className="gap-2">
                  View All Products
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </div>
            </TabsContent>
            <TabsContent value="oral" className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4">
                <ProductCard
                  title="Oral-B Electric Toothbrush"
                  price={350000}
                  currency="UGX"
                  image="/placeholder.svg?height=300&width=300"
                  discount={10}
                  rating={4.9}
                  href="/products/oral-b-electric"
                  id="oral-b-electric"
                />
                <ProductCard
                  title="Colgate Total Toothpaste"
                  price={25000}
                  currency="UGX"
                  image="/placeholder.svg?height=300&width=300"
                  discount={0}
                  rating={4.7}
                  href="/products/colgate-total"
                  id="colgate-total"
                />
                <ProductCard
                  title="Sensodyne Toothpaste"
                  price={30000}
                  currency="UGX"
                  image="/placeholder.svg?height=300&width=300"
                  discount={5}
                  rating={4.8}
                  href="/products/sensodyne"
                  id="sensodyne"
                />
                <ProductCard
                  title="Oral-B Toothpaste"
                  price={28000}
                  currency="UGX"
                  image="/placeholder.svg?height=300&width=300"
                  discount={0}
                  rating={4.6}
                  href="/products/oral-b-paste"
                  id="oral-b-paste"
                />
                <ProductCard
                  title="Listerine Mouthwash"
                  price={45000}
                  currency="UGX"
                  image="/placeholder.svg?height=300&width=300"
                  discount={15}
                  rating={4.5}
                  href="/products/listerine"
                  id="listerine"
                />
              </div>
              <div className="flex justify-center mt-8">
                <Button variant="outline" className="gap-2">
                  View All Products
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </div>
            </TabsContent>
            <TabsContent value="bedding" className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4">
                <ProductCard
                  title="Egyptian Cotton Pillow"
                  price={185000}
                  currency="UGX"
                  image="/placeholder.svg?height=300&width=300"
                  discount={0}
                  rating={4.8}
                  href="/products/egyptian-pillow"
                  id="egyptian-pillow"
                />
                <ProductCard
                  title="Bamboo Duvet Cover"
                  price={450000}
                  currency="UGX"
                  image="/placeholder.svg?height=300&width=300"
                  discount={10}
                  rating={4.9}
                  href="/products/bamboo-duvet"
                  id="bamboo-duvet"
                />
                <ProductCard
                  title="Luxury King Size Sheet Set"
                  price={350000}
                  currency="UGX"
                  image="/placeholder.svg?height=300&width=300"
                  discount={15}
                  rating={4.7}
                  href="/products/king-sheet-set"
                  id="king-sheet-set"
                />
                <ProductCard
                  title="Comfy Sheets Pink King"
                  price={450000}
                  currency="UGX"
                  image="/placeholder.svg?height=300&width=300"
                  discount={0}
                  rating={4.6}
                  href="/products/comfy-sheets"
                  id="comfy-sheets"
                />
                <ProductCard
                  title="Memory Foam Pillow"
                  price={120000}
                  currency="UGX"
                  image="/placeholder.svg?height=300&width=300"
                  discount={5}
                  rating={4.8}
                  href="/products/memory-foam-pillow"
                  id="memory-foam-pillow"
                />
              </div>
              <div className="flex justify-center mt-8">
                <Button variant="outline" className="gap-2">
                  View All Products
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </section>

        <section className="bg-muted py-12">
          <div className="container">
            <FeaturedBrands />
          </div>
        </section>

        <section className="container py-12">
          <Newsletter />
        </section>
      </main>
    </div>
  )
}

