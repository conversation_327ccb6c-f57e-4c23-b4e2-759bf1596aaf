import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON><PERSON>, ExternalLink } from "lucide-react"

import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"

// Mock data for top products
const topProducts = [
  {
    id: "cantu-shea-butter",
    name: "Cantu Shea Butter Tea Tree Oil Leave-In Conditioner",
    image: "/placeholder.svg?height=40&width=40",
    sales: 245,
    revenue: 34300000,
    stock: 45,
  },
  {
    id: "oral-b-electric",
    name: "Oral-B Pro 1000 Electric Toothbrush",
    image: "/placeholder.svg?height=40&width=40",
    sales: 189,
    revenue: 66150000,
    stock: 32,
  },
  {
    id: "bamboo-duvet",
    name: "Bamboo Duvet Cover Queen Size",
    image: "/placeholder.svg?height=40&width=40",
    sales: 156,
    revenue: 70200000,
    stock: 18,
  },
  {
    id: "suave-essentials",
    name: "Suave Essentials Gentle Body Wash Ocean Breeze",
    image: "/placeholder.svg?height=40&width=40",
    sales: 134,
    revenue: 19430000,
    stock: 67,
  },
  {
    id: "egyptian-pillow",
    name: "Egyptian Cotton King Size Pillow",
    image: "/placeholder.svg?height=40&width=40",
    sales: 112,
    revenue: 20720000,
    stock: 24,
  },
]

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "UGX",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

export function AdminTopProducts() {
  // Find the highest sales value for progress calculation
  const maxSales = Math.max(...topProducts.map((product) => product.sales))

  return (
    <div className="space-y-6">
      {topProducts.map((product) => (
        <div key={product.id} className="flex items-start gap-4">
          <div className="w-10 h-10 rounded overflow-hidden bg-muted/20 flex-shrink-0">
            <Image
              src={product.image || "/placeholder.svg"}
              alt={product.name}
              width={40}
              height={40}
              className="w-full h-full object-cover"
            />
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex justify-between items-start">
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-sm line-clamp-1">{product.name}</h4>
                <div className="flex items-center gap-4 mt-1">
                  <span className="text-xs text-muted-foreground">{product.sales} sold</span>
                  <span className="text-xs text-muted-foreground">{formatCurrency(product.revenue)}</span>
                </div>
              </div>
              <Button variant="ghost" size="icon" className="h-8 w-8" asChild>
                <Link href={`/admin/products/${product.id}`}>
                  <ExternalLink className="h-4 w-4" />
                </Link>
              </Button>
            </div>
            <div className="mt-2 flex items-center gap-2">
              <Progress value={(product.sales / maxSales) * 100} className="h-2" />
              <span className="text-xs font-medium w-12 text-right">
                {Math.round((product.sales / maxSales) * 100)}%
              </span>
            </div>
            <div className="mt-1 flex justify-between items-center">
              <span className="text-xs text-muted-foreground">Stock: {product.stock} units</span>
              <Button variant="ghost" size="sm" className="h-6 text-xs gap-1" asChild>
                <Link href={`/admin/analytics/products/${product.id}`}>
                  <BarChart className="h-3 w-3" />
                  <span>Stats</span>
                </Link>
              </Button>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

