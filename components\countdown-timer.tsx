"use client"

import { useEffect, useState } from "react"

interface CountdownTimerProps {
  targetDate: string
}

export function CountdownTimer({ targetDate }: CountdownTimerProps) {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  })

  useEffect(() => {
    const target = new Date(targetDate).getTime()

    const interval = setInterval(() => {
      const now = new Date().getTime()
      const difference = target - now

      if (difference <= 0) {
        clearInterval(interval)
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 })
        return
      }

      const days = Math.floor(difference / (1000 * 60 * 60 * 24))
      const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((difference % (1000 * 60)) / 1000)

      setTimeLeft({ days, hours, minutes, seconds })
    }, 1000)

    return () => clearInterval(interval)
  }, [targetDate])

  return (
    <div className="flex items-center gap-1.5">
      <div className="bg-primary/10 rounded px-2 py-1 text-xs font-medium">
        {String(timeLeft.days).padStart(2, "0")}d
      </div>
      <div className="bg-primary/10 rounded px-2 py-1 text-xs font-medium">
        {String(timeLeft.hours).padStart(2, "0")}h
      </div>
      <div className="bg-primary/10 rounded px-2 py-1 text-xs font-medium">
        {String(timeLeft.minutes).padStart(2, "0")}m
      </div>
      <div className="bg-primary/10 rounded px-2 py-1 text-xs font-medium">
        {String(timeLeft.seconds).padStart(2, "0")}s
      </div>
    </div>
  )
}

