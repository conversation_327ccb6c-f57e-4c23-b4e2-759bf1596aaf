"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { ChevronLeft, ChevronRight } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"

const slides = [
  {
    image: "/placeholder.svg?height=600&width=1200",
    title: "Get great Sport sneakers",
    subtitle: "New styles, new comfort",
    discount: "15% OFF",
    buttonText: "Shop Now",
    buttonLink: "/shop/sneakers",
  },
  {
    image: "/placeholder.svg?height=600&width=1200",
    title: "Premium Beauty Products",
    subtitle: "For your daily routine",
    discount: "20% OFF",
    buttonText: "Explore",
    buttonLink: "/shop/beauty",
  },
  {
    image: "/placeholder.svg?height=600&width=1200",
    title: "Luxury Bedding Collection",
    subtitle: "Sleep in comfort",
    discount: "10% OFF",
    buttonText: "Discover",
    buttonLink: "/shop/bedding",
  },
]

export function HeroCarousel() {
  const [currentSlide, setCurrentSlide] = useState(0)

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1))
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev === 0 ? slides.length - 1 : prev - 1))
  }

  useEffect(() => {
    const interval = setInterval(() => {
      nextSlide()
    }, 5000)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="relative overflow-hidden">
      <div
        className="flex transition-transform duration-500 ease-out h-[400px] md:h-[500px]"
        style={{ transform: `translateX(-${currentSlide * 100}%)` }}
      >
        {slides.map((slide, index) => (
          <div key={index} className="min-w-full relative">
            <Image
              src={slide.image || "/placeholder.svg"}
              alt={slide.title}
              fill
              className="object-cover"
              priority={index === 0}
            />
            <div className="absolute inset-0 bg-gradient-to-r from-background/80 to-background/20" />
            <div className="absolute inset-0 flex flex-col justify-center container">
              <div className="max-w-lg space-y-4">
                <p className="text-sm font-medium text-primary">{slide.subtitle}</p>
                <h2 className="text-4xl md:text-5xl font-bold tracking-tight">{slide.title}</h2>
                <p className="text-xl md:text-2xl font-semibold">{slide.discount}</p>
                <Button size="lg" asChild>
                  <a href={slide.buttonLink}>{slide.buttonText}</a>
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>
      <Button
        variant="outline"
        size="icon"
        className="absolute left-4 top-1/2 -translate-y-1/2 h-10 w-10 rounded-full bg-background/80 backdrop-blur-sm"
        onClick={prevSlide}
      >
        <ChevronLeft className="h-5 w-5" />
      </Button>
      <Button
        variant="outline"
        size="icon"
        className="absolute right-4 top-1/2 -translate-y-1/2 h-10 w-10 rounded-full bg-background/80 backdrop-blur-sm"
        onClick={nextSlide}
      >
        <ChevronRight className="h-5 w-5" />
      </Button>
      <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
        {slides.map((_, index) => (
          <button
            key={index}
            className={cn(
              "w-2.5 h-2.5 rounded-full transition-colors",
              currentSlide === index ? "bg-primary" : "bg-primary/30",
            )}
            onClick={() => setCurrentSlide(index)}
          />
        ))}
      </div>
    </div>
  )
}

