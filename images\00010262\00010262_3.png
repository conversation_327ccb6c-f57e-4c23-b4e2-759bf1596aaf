<html> <head> <meta charset='UTF-8'> <meta name='viewport' content='width=device-width, initial-scale=1.0'> <meta http-equiv='X-UA-Compatible' content='ie=edge'> <link href='https://fonts.googleapis.com/css?family=Montserrat:400,700' rel='stylesheet' type='text/css'> <link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/bootstrap@4.5.3/dist/css/bootstrap.min.css' integrity='sha384-TX8t27EcRE3e/ihU7zmQxVncDAy5uIKz4rEkgIXeMed4M0jlfIDPvg6uqKI2xXr2' crossorigin='anonymous'> <title>This site is under mantenance</title> <style>html,body{height: 100%;}html{font-size: 62.5%;}body{margin: 0; padding: 0; font-size: 1.6rem; color: #353535; background: linear-gradient(to bottom, #ccc, #fff 30%); -ms-flex-align: center; -ms-flex-pack: center; -webkit-box-align: center; align-items: center; -webkit-box-pack: center; justify-content: center; padding-top: 40px; padding-bottom: 40px; background-color: #f5f5f5;}.form-signin{width: 100%; max-width: 330px; padding: 15px; margin: 0 auto;}.form-signin .checkbox{font-weight: 400;}.form-signin .form-control{position: relative; box-sizing: border-box; height: auto; padding: 10px; font-size: 16px;}.form-signin .form-control:focus{z-index: 2;}.form-signin input[type='email']{margin-bottom: -1px; border-bottom-right-radius: 0; border-bottom-left-radius: 0;}.form-signin input[type='password']{margin-bottom: 10px; border-top-left-radius: 0; border-top-right-radius: 0;}h1,h3{font-family: 'Montserrat', sans-serif; text-align: center; margin: 0; padding: 0; text-transform: uppercase;}h1{margin: 20rem 0 1rem; font-size: 3rem;}h3{font-weight: normal; font-size: 1.4rem;}</style> </head> <body>
<h1>This Site is offline</h1>
<h3>&nbsp;</h3>
<p><br/>&nbsp;</p>
<form class="form-signin" method="post">
<h2 class="h3 mb-3 font-weight-normal">Please sign in <label class="sr-only" for="inputPassword">Password</label> <input id="pass" class="form-control" name="pass" required="" type="password" placeholder="Password"/> <button class="btn btn-lg btn-primary btn-block" type="submit">Sign in</button></h2>
</form>
</body></html>