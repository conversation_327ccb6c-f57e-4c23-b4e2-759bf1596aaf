import Image from "next/image"

export function FeaturedBrands() {
  const brands = [
    { name: "Brand 1", logo: "/placeholder.svg?height=60&width=120" },
    { name: "Brand 2", logo: "/placeholder.svg?height=60&width=120" },
    { name: "Brand 3", logo: "/placeholder.svg?height=60&width=120" },
    { name: "Brand 4", logo: "/placeholder.svg?height=60&width=120" },
    { name: "Brand 5", logo: "/placeholder.svg?height=60&width=120" },
    { name: "Brand 6", logo: "/placeholder.svg?height=60&width=120" },
  ]

  return (
    <div className="py-8">
      <h2 className="text-2xl font-bold tracking-tight text-center mb-8">Featured Brands</h2>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
        {brands.map((brand, index) => (
          <div key={index} className="flex items-center justify-center p-4 bg-background rounded-lg border">
            <Image src={brand.logo || "/placeholder.svg"} alt={brand.name} width={120} height={60} />
          </div>
        ))}
      </div>
    </div>
  )
}

