import Link from "next/link"
import Image from "next/image"
import { Search, ShoppingCart, Heart, User } from "lucide-react"

import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

export function MobileNav() {
  return (
    <div className="flex flex-col h-full">
      <div className="flex items-center justify-center py-4">
        <Link href="/" className="flex items-center space-x-2">
          <Image src="/logo.svg" alt="STXpress Logo" width={120} height={30} />
        </Link>
      </div>
      <Separator className="mb-4" />
      <div className="relative mb-4">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input type="search" placeholder="Search products..." className="pl-8" />
      </div>
      <div className="flex justify-around mb-6">
        <Link
          href="/wishlist"
          className="flex flex-col items-center text-muted-foreground hover:text-foreground transition-colors"
        >
          <Heart className="h-6 w-6" />
          <span className="text-xs mt-1">Wishlist</span>
        </Link>
        <Link
          href="/cart"
          className="flex flex-col items-center text-muted-foreground hover:text-foreground transition-colors"
        >
          <div className="relative">
            <ShoppingCart className="h-6 w-6" />
            <Badge className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs">2</Badge>
          </div>
          <span className="text-xs mt-1">Cart</span>
        </Link>
        <Link
          href="/account"
          className="flex flex-col items-center text-muted-foreground hover:text-foreground transition-colors"
        >
          <User className="h-6 w-6" />
          <span className="text-xs mt-1">Account</span>
        </Link>
      </div>
      <Separator className="mb-4" />
      <nav className="flex flex-col space-y-4">
        <Link href="/shop" className="text-base font-medium hover:text-primary transition-colors">
          Shop
        </Link>
        <Link href="/categories" className="text-base font-medium hover:text-primary transition-colors">
          Categories
        </Link>
        <Link href="/deals" className="text-base font-medium hover:text-primary transition-colors">
          Deals
        </Link>
        <Link href="/about" className="text-base font-medium hover:text-primary transition-colors">
          About
        </Link>
        <Link href="/contact" className="text-base font-medium hover:text-primary transition-colors">
          Contact
        </Link>
      </nav>
      <div className="mt-auto pt-6">
        <Separator className="mb-4" />
        <div className="flex flex-col space-y-2">
          <Link href="/shipping" className="text-sm text-muted-foreground hover:text-primary transition-colors">
            Shipping Policy
          </Link>
          <Link href="/returns" className="text-sm text-muted-foreground hover:text-primary transition-colors">
            Returns & Exchanges
          </Link>
          <Link href="/faq" className="text-sm text-muted-foreground hover:text-primary transition-colors">
            FAQs
          </Link>
        </div>
      </div>
    </div>
  )
}

