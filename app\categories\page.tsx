import Image from "next/image"
import Link from "next/link"
import { ChevronRight } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

// Mock categories data - in a real app, this would come from an API or database
const categories = [
  {
    id: "beauty-health",
    name: "Beauty & Health",
    description: "Skincare, haircare, and personal care products for your daily routine.",
    image: "/placeholder.svg?height=400&width=600",
    count: 120,
    featured: true,
    subcategories: [
      { id: "skincare", name: "Skincare", count: 45 },
      { id: "haircare", name: "Haircare", count: 38 },
      { id: "body-care", name: "Body Care", count: 37 },
    ],
  },
  {
    id: "oral-hygiene",
    name: "Oral Hygiene",
    description: "Toothpastes, toothbrushes, mouthwashes, and more for your dental care.",
    image: "/placeholder.svg?height=400&width=600",
    count: 45,
    featured: true,
    subcategories: [
      { id: "toothpaste", name: "Toothpaste", count: 15 },
      { id: "toothbrushes", name: "Toothbrushes", count: 12 },
      { id: "mouthwash", name: "Mouthwash", count: 8 },
      { id: "dental-floss", name: "Dental Floss", count: 10 },
    ],
  },
  {
    id: "beddings",
    name: "Beddings",
    description: "Sheets, pillows, duvets, and more for a comfortable night's sleep.",
    image: "/placeholder.svg?height=400&width=600",
    count: 78,
    featured: true,
    subcategories: [
      { id: "bed-sheets", name: "Bed Sheets", count: 25 },
      { id: "pillows", name: "Pillows", count: 18 },
      { id: "duvets", name: "Duvets", count: 15 },
      { id: "mattress-protectors", name: "Mattress Protectors", count: 20 },
    ],
  },
  {
    id: "footwear",
    name: "Footwear",
    description: "Shoes, sandals, slippers, and more for men, women, and children.",
    image: "/placeholder.svg?height=400&width=600",
    count: 96,
    featured: true,
    subcategories: [
      { id: "mens-shoes", name: "Men's Shoes", count: 30 },
      { id: "womens-shoes", name: "Women's Shoes", count: 35 },
      { id: "kids-shoes", name: "Kids' Shoes", count: 20 },
      { id: "slippers", name: "Slippers", count: 11 },
    ],
  },
  {
    id: "perfumes",
    name: "Perfumes",
    description: "Fragrances for men and women from top brands around the world.",
    image: "/placeholder.svg?height=400&width=600",
    count: 64,
    featured: true,
    subcategories: [
      { id: "mens-perfumes", name: "Men's Perfumes", count: 28 },
      { id: "womens-perfumes", name: "Women's Perfumes", count: 36 },
    ],
  },
  {
    id: "clothing",
    name: "Clothing",
    description: "Apparel for men, women, and children for all occasions.",
    image: "/placeholder.svg?height=400&width=600",
    count: 150,
    featured: true,
    subcategories: [
      { id: "mens-clothing", name: "Men's Clothing", count: 50 },
      { id: "womens-clothing", name: "Women's Clothing", count: 65 },
      { id: "kids-clothing", name: "Kids' Clothing", count: 35 },
    ],
  },
  {
    id: "home-decor",
    name: "Home Decor",
    description: "Decorative items to beautify your living space.",
    image: "/placeholder.svg?height=400&width=600",
    count: 85,
    featured: false,
    subcategories: [
      { id: "wall-art", name: "Wall Art", count: 30 },
      { id: "vases", name: "Vases", count: 15 },
      { id: "candles", name: "Candles", count: 20 },
      { id: "throw-pillows", name: "Throw Pillows", count: 20 },
    ],
  },
  {
    id: "kitchen-dining",
    name: "Kitchen & Dining",
    description: "Cookware, utensils, and dining essentials for your kitchen.",
    image: "/placeholder.svg?height=400&width=600",
    count: 110,
    featured: false,
    subcategories: [
      { id: "cookware", name: "Cookware", count: 35 },
      { id: "utensils", name: "Utensils", count: 25 },
      { id: "dinnerware", name: "Dinnerware", count: 30 },
      { id: "glassware", name: "Glassware", count: 20 },
    ],
  },
]

export default function CategoriesPage() {
  const featuredCategories = categories.filter((category) => category.featured)
  const otherCategories = categories.filter((category) => !category.featured)

  return (
    <div className="flex flex-col min-h-screen">
      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative overflow-hidden bg-muted">
          <div className="absolute inset-0 z-0">
            <Image
              src="/placeholder.svg?height=400&width=1600"
              alt="Categories Banner"
              fill
              className="object-cover opacity-20"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-r from-background via-background/80 to-background/20" />
          </div>
          <div className="container relative z-10 py-16 md:py-24">
            <div className="max-w-2xl space-y-5">
              <div className="inline-block rounded-full bg-primary/10 px-3 py-1 text-sm text-primary">
                Browse Categories
              </div>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight">
                Shop By <span className="text-primary">Category</span>
              </h1>
              <p className="text-lg text-muted-foreground">
                Explore our wide range of products organized by categories to find exactly what you're looking for.
              </p>
            </div>
          </div>
        </section>

        {/* Featured Categories */}
        <section className="container py-12">
          <h2 className="text-2xl font-bold tracking-tight mb-8">Featured Categories</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {featuredCategories.map((category) => (
              <Link key={category.id} href={`/categories/${category.id}`}>
                <Card className="overflow-hidden h-full transition-all duration-300 hover:shadow-md group">
                  <CardContent className="p-0">
                    <div className="relative">
                      <div className="aspect-[16/9] overflow-hidden">
                        <Image
                          src={category.image || "/placeholder.svg"}
                          alt={category.name}
                          width={600}
                          height={400}
                          className="object-cover w-full h-full transition-transform duration-500 group-hover:scale-105"
                        />
                      </div>
                      <div className="absolute inset-0 bg-gradient-to-t from-background/90 via-background/50 to-transparent flex flex-col justify-end p-6">
                        <h3 className="text-xl font-bold mb-1">{category.name}</h3>
                        <p className="text-sm text-muted-foreground mb-2">{category.count} products</p>
                        <p className="text-sm mb-4 line-clamp-2">{category.description}</p>
                        <div className="flex items-center text-primary text-sm font-medium">
                          Shop Now
                          <ChevronRight className="ml-1 h-4 w-4" />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </section>

        {/* All Categories */}
        <section className="container py-12">
          <h2 className="text-2xl font-bold tracking-tight mb-8">All Categories</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {categories.map((category) => (
              <Card key={category.id} className="overflow-hidden transition-all duration-300 hover:shadow-md">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-bold">{category.name}</h3>
                    <span className="text-sm text-muted-foreground">{category.count} items</span>
                  </div>
                  <ul className="space-y-2 mb-4">
                    {category.subcategories.map((subcategory) => (
                      <li key={subcategory.id}>
                        <Link
                          href={`/categories/${category.id}/${subcategory.id}`}
                          className="text-sm hover:text-primary transition-colors flex items-center justify-between"
                        >
                          <span>{subcategory.name}</span>
                          <span className="text-xs text-muted-foreground">({subcategory.count})</span>
                        </Link>
                      </li>
                    ))}
                  </ul>
                  <Link
                    href={`/categories/${category.id}`}
                    className="text-sm text-primary font-medium flex items-center"
                  >
                    View All
                    <ChevronRight className="ml-1 h-4 w-4" />
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* CTA Section */}
        <section className="container py-12">
          <div className="bg-primary/10 rounded-2xl p-8 md:p-12">
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div>
                <h2 className="text-3xl font-bold tracking-tight mb-4">Can't Find What You're Looking For?</h2>
                <p className="text-muted-foreground mb-6">
                  Our customer support team is here to help you find the perfect product for your needs.
                </p>
                <div className="flex flex-wrap gap-4">
                  <Button size="lg">Contact Us</Button>
                  <Button variant="outline" size="lg">
                    View New Arrivals
                  </Button>
                </div>
              </div>
              <div className="relative hidden md:block">
                <div className="absolute -top-4 -left-4 w-16 h-16 bg-primary/20 rounded-full" />
                <div className="absolute -bottom-4 -right-4 w-24 h-24 bg-primary/20 rounded-full" />
                <Image
                  src="/placeholder.svg?height=300&width=500"
                  alt="Customer Support"
                  width={500}
                  height={300}
                  className="rounded-lg relative z-10"
                />
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  )
}

