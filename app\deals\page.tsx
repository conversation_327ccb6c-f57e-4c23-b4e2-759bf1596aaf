import Image from "next/image"
import Link from "next/link"
import { ArrowRight } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ProductCard } from "@/components/product-card"
import { CountdownTimer } from "@/components/countdown-timer"

export default function DealsPage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-muted">
        <div className="absolute inset-0 z-0">
          <Image
            src="/placeholder.svg?height=400&width=1600"
            alt="Deals Banner"
            fill
            className="object-cover opacity-20"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-r from-background via-background/80 to-background/20" />
        </div>
        <div className="container relative z-10 py-16 md:py-24">
          <div className="max-w-2xl space-y-5">
            <div className="inline-block rounded-full bg-primary/10 px-3 py-1 text-sm text-primary">
              Limited Time Offers
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight">
              Exclusive <span className="text-primary">Deals</span> & Promotions
            </h1>
            <p className="text-lg text-muted-foreground">
              Discover amazing discounts on your favorite products. Don't miss out on these special offers!
            </p>
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium">Deals End In:</span>
              <CountdownTimer targetDate="2025-04-30T00:00:00" />
            </div>
          </div>
        </div>
      </section>

      {/* Flash Deals Section */}
      <section className="container py-16">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <h2 className="text-2xl md:text-3xl font-bold tracking-tight">Flash Deals</h2>
            <CountdownTimer targetDate="2025-04-15T00:00:00" />
          </div>
          <Link href="/flash-deals" className="text-primary flex items-center gap-1 font-medium">
            View All <ArrowRight className="h-4 w-4" />
          </Link>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          <ProductCard
            title="Dove Nourishing Body Wash with Deep Moisture"
            price={110000}
            currency="UGX"
            image="/placeholder.svg?height=300&width=300"
            discount={25}
            rating={4.8}
            href="/products/dove-body-wash"
            badge="Flash Deal"
          />
          <ProductCard
            title="Cantu Shea Butter Tea Tree Oil Leave-In Conditioner"
            price={140000}
            currency="UGX"
            image="/placeholder.svg?height=300&width=300"
            discount={30}
            rating={4.5}
            href="/products/cantu-body-wash"
            badge="Flash Deal"
          />
          <ProductCard
            title="Oral-B Pro 1000 Electric Toothbrush"
            price={350000}
            currency="UGX"
            image="/placeholder.svg?height=300&width=300"
            discount={20}
            rating={4.9}
            href="/products/oral-b-electric"
            badge="Flash Deal"
          />
          <ProductCard
            title="Listerine Cool Mint Antiseptic Mouthwash"
            price={45000}
            currency="UGX"
            image="/placeholder.svg?height=300&width=300"
            discount={35}
            rating={4.5}
            href="/products/listerine"
            badge="Flash Deal"
          />
        </div>
      </section>

      {/* Featured Deals Banner */}
      <section className="container py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="relative overflow-hidden rounded-lg">
            <div className="absolute inset-0 bg-gradient-to-r from-primary/80 to-primary/40 z-10"></div>
            <Image
              src="/placeholder.svg?height=400&width=600"
              alt="Beauty products promotion"
              width={600}
              height={400}
              className="w-full h-[300px] object-cover"
            />
            <div className="absolute top-0 left-0 p-8 z-20">
              <p className="text-white text-sm font-medium mb-2">Limited Time Offer</p>
              <h3 className="text-white text-3xl font-bold mb-2">Beauty Products</h3>
              <p className="text-white/90 mb-4">Up to 40% off on selected items</p>
              <Button variant="secondary" size="sm">
                Shop Now
              </Button>
            </div>
          </div>
          <div className="relative overflow-hidden rounded-lg">
            <div className="absolute inset-0 bg-gradient-to-r from-primary/80 to-primary/40 z-10"></div>
            <Image
              src="/placeholder.svg?height=400&width=600"
              alt="Home essentials promotion"
              width={600}
              height={400}
              className="w-full h-[300px] object-cover"
            />
            <div className="absolute top-0 left-0 p-8 z-20">
              <p className="text-white text-sm font-medium mb-2">New Collection</p>
              <h3 className="text-white text-3xl font-bold mb-2">Home Essentials</h3>
              <p className="text-white/90 mb-4">Starting at just UGX 50,000</p>
              <Button variant="secondary" size="sm">
                Shop Now
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Deal Categories */}
      <section className="container py-16">
        <h2 className="text-2xl md:text-3xl font-bold tracking-tight mb-8">Shop Deals By Category</h2>
        <Tabs defaultValue="beauty" className="w-full">
          <TabsList className="mb-8">
            <TabsTrigger value="beauty">Beauty & Health</TabsTrigger>
            <TabsTrigger value="oral">Oral Hygiene</TabsTrigger>
            <TabsTrigger value="bedding">Beddings</TabsTrigger>
            <TabsTrigger value="footwear">Footwear</TabsTrigger>
          </TabsList>
          <TabsContent value="beauty" className="space-y-8">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              <ProductCard
                title="Dove Body Wash"
                price={110000}
                currency="UGX"
                image="/placeholder.svg?height=300&width=300"
                discount={15}
                rating={4.8}
                href="/products/dove-body-wash"
              />
              <ProductCard
                title="Suave Essentials Gentle Body Wash"
                price={145000}
                currency="UGX"
                image="/placeholder.svg?height=300&width=300"
                discount={10}
                rating={4.2}
                href="/products/suave-essentials"
              />
              <ProductCard
                title="Cantu Body Wash"
                price={140000}
                currency="UGX"
                image="/placeholder.svg?height=300&width=300"
                discount={20}
                rating={4.5}
                href="/products/cantu-body-wash"
              />
              <ProductCard
                title="Wild Cherry Blossom Body Wash"
                price={160000}
                currency="UGX"
                image="/placeholder.svg?height=300&width=300"
                discount={25}
                rating={4.7}
                href="/products/wild-cherry"
              />
            </div>
            <div className="flex justify-center">
              <Button variant="outline" className="gap-2">
                View All Beauty Deals
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </TabsContent>
          <TabsContent value="oral" className="space-y-8">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              <ProductCard
                title="Oral-B Electric Toothbrush"
                price={350000}
                currency="UGX"
                image="/placeholder.svg?height=300&width=300"
                discount={20}
                rating={4.9}
                href="/products/oral-b-electric"
              />
              <ProductCard
                title="Colgate Total Toothpaste"
                price={25000}
                currency="UGX"
                image="/placeholder.svg?height=300&width=300"
                discount={15}
                rating={4.7}
                href="/products/colgate-total"
              />
              <ProductCard
                title="Sensodyne Toothpaste"
                price={30000}
                currency="UGX"
                image="/placeholder.svg?height=300&width=300"
                discount={10}
                rating={4.8}
                href="/products/sensodyne"
              />
              <ProductCard
                title="Listerine Mouthwash"
                price={45000}
                currency="UGX"
                image="/placeholder.svg?height=300&width=300"
                discount={30}
                rating={4.5}
                href="/products/listerine"
              />
            </div>
            <div className="flex justify-center">
              <Button variant="outline" className="gap-2">
                View All Oral Hygiene Deals
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </TabsContent>
          <TabsContent value="bedding" className="space-y-8">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              <ProductCard
                title="Egyptian Cotton Pillow"
                price={185000}
                currency="UGX"
                image="/placeholder.svg?height=300&width=300"
                discount={15}
                rating={4.8}
                href="/products/egyptian-pillow"
              />
              <ProductCard
                title="Bamboo Duvet Cover"
                price={450000}
                currency="UGX"
                image="/placeholder.svg?height=300&width=300"
                discount={20}
                rating={4.9}
                href="/products/bamboo-duvet"
              />
              <ProductCard
                title="Luxury King Size Sheet Set"
                price={350000}
                currency="UGX"
                image="/placeholder.svg?height=300&width=300"
                discount={25}
                rating={4.7}
                href="/products/king-sheet-set"
              />
              <ProductCard
                title="Memory Foam Pillow"
                price={120000}
                currency="UGX"
                image="/placeholder.svg?height=300&width=300"
                discount={10}
                rating={4.8}
                href="/products/memory-foam-pillow"
              />
            </div>
            <div className="flex justify-center">
              <Button variant="outline" className="gap-2">
                View All Bedding Deals
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </TabsContent>
          <TabsContent value="footwear" className="space-y-8">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              <ProductCard
                title="Men's Running Shoes"
                price={250000}
                currency="UGX"
                image="/placeholder.svg?height=300&width=300"
                discount={30}
                rating={4.6}
                href="/products/mens-running-shoes"
              />
              <ProductCard
                title="Women's Casual Sneakers"
                price={220000}
                currency="UGX"
                image="/placeholder.svg?height=300&width=300"
                discount={25}
                rating={4.7}
                href="/products/womens-sneakers"
              />
              <ProductCard
                title="Kids Sport Shoes"
                price={180000}
                currency="UGX"
                image="/placeholder.svg?height=300&width=300"
                discount={20}
                rating={4.5}
                href="/products/kids-sport-shoes"
              />
              <ProductCard
                title="Unisex Slippers"
                price={85000}
                currency="UGX"
                image="/placeholder.svg?height=300&width=300"
                discount={15}
                rating={4.4}
                href="/products/unisex-slippers"
              />
            </div>
            <div className="flex justify-center">
              <Button variant="outline" className="gap-2">
                View All Footwear Deals
                <ArrowRight className="h-4 w-4" />
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </section>

      {/* Bundle Deals */}
      <section className="bg-muted/40 py-16">
        <div className="container">
          <h2 className="text-2xl md:text-3xl font-bold tracking-tight mb-8">Bundle Deals & Save More</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-background rounded-lg border p-6 flex flex-col md:flex-row gap-6">
              <div className="md:w-1/3">
                <Image
                  src="/placeholder.svg?height=300&width=300"
                  alt="Oral Care Bundle"
                  width={300}
                  height={300}
                  className="rounded-lg"
                />
              </div>
              <div className="md:w-2/3">
                <div className="inline-block rounded-full bg-primary/10 px-3 py-1 text-xs text-primary mb-2">
                  Save 30%
                </div>
                <h3 className="text-xl font-bold mb-2">Complete Oral Care Bundle</h3>
                <p className="text-muted-foreground mb-4">
                  Get an Oral-B Electric Toothbrush, Colgate Toothpaste, and Listerine Mouthwash at a special bundle
                  price.
                </p>
                <div className="flex items-center gap-2 mb-4">
                  <p className="font-semibold text-lg">UGX 350,000</p>
                  <p className="text-sm text-muted-foreground line-through">UGX 500,000</p>
                </div>
                <Button>Add Bundle to Cart</Button>
              </div>
            </div>
            <div className="bg-background rounded-lg border p-6 flex flex-col md:flex-row gap-6">
              <div className="md:w-1/3">
                <Image
                  src="/placeholder.svg?height=300&width=300"
                  alt="Skincare Bundle"
                  width={300}
                  height={300}
                  className="rounded-lg"
                />
              </div>
              <div className="md:w-2/3">
                <div className="inline-block rounded-full bg-primary/10 px-3 py-1 text-xs text-primary mb-2">
                  Save 25%
                </div>
                <h3 className="text-xl font-bold mb-2">Luxury Skincare Bundle</h3>
                <p className="text-muted-foreground mb-4">
                  Complete skincare routine with cleanser, toner, moisturizer, and serum from top brands.
                </p>
                <div className="flex items-center gap-2 mb-4">
                  <p className="font-semibold text-lg">UGX 280,000</p>
                  <p className="text-sm text-muted-foreground line-through">UGX 375,000</p>
                </div>
                <Button>Add Bundle to Cart</Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter with Deal Alert */}
      <section className="container py-16">
        <div className="bg-primary/10 rounded-2xl p-8 md:p-12">
          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div>
              <h2 className="text-3xl font-bold tracking-tight mb-4">Never Miss a Deal!</h2>
              <p className="text-muted-foreground mb-6">
                Subscribe to our newsletter and be the first to know about exclusive deals, new arrivals, and special
                promotions.
              </p>
              <div className="flex flex-col sm:flex-row gap-3">
                <input type="email" placeholder="Enter your email" className="px-4 py-2 rounded-md border flex-1" />
                <Button>Subscribe</Button>
              </div>
            </div>
            <div className="relative hidden md:block">
              <div className="absolute -top-4 -left-4 w-16 h-16 bg-primary/20 rounded-full" />
              <div className="absolute -bottom-4 -right-4 w-24 h-24 bg-primary/20 rounded-full" />
              <Image
                src="/placeholder.svg?height=300&width=500"
                alt="Newsletter"
                width={500}
                height={300}
                className="rounded-lg relative z-10"
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

