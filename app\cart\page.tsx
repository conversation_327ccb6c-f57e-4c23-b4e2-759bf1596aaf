"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { Trash2, Plus, Minus, ShoppingBag, ArrowRight, Truck, ShieldCheck, RefreshCw } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

// Mock cart data - in a real app, this would come from a cart context/store
const initialCartItems = [
  {
    id: "cantu-shea-butter",
    title: "Cantu Shea Butter Tea Tree Oil Leave-In Conditioner",
    price: 140000,
    discount: 15,
    quantity: 2,
    image: "/placeholder.svg?height=300&width=300",
    stock: 10,
  },
  {
    id: "suave-essentials",
    title: "Suave Essentials Gentle Body Wash Ocean Breeze",
    price: 145000,
    discount: 20,
    quantity: 1,
    image: "/placeholder.svg?height=300&width=300",
    stock: 5,
  },
  {
    id: "oral-b-electric",
    title: "Oral-B Pro 1000 Electric Toothbrush",
    price: 350000,
    discount: 10,
    quantity: 1,
    image: "/placeholder.svg?height=300&width=300",
    stock: 3,
  },
]

// Mock recommended products
const recommendedProducts = [
  {
    id: "dove-body-wash",
    title: "Dove Deep Moisture Body Wash",
    price: 120000,
    discount: 5,
    image: "/placeholder.svg?height=200&width=200",
  },
  {
    id: "nivea-lotion",
    title: "Nivea Nourishing Body Lotion",
    price: 95000,
    discount: 0,
    image: "/placeholder.svg?height=200&width=200",
  },
  {
    id: "colgate-toothpaste",
    title: "Colgate Total Whitening Toothpaste",
    price: 75000,
    discount: 10,
    image: "/placeholder.svg?height=200&width=200",
  },
]

export default function CartPage() {
  const [cartItems, setCartItems] = useState(initialCartItems)
  const [couponCode, setCouponCode] = useState("")
  const [couponApplied, setCouponApplied] = useState(false)
  const [couponError, setCouponError] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [freeShippingProgress, setFreeShippingProgress] = useState(0)

  // Calculate cart totals
  const subtotal = cartItems.reduce((total, item) => {
    const itemPrice = item.discount > 0 ? item.price - (item.price * item.discount) / 100 : item.price
    return total + itemPrice * item.quantity
  }, 0)

  const freeShippingThreshold = 500000 // 500,000 UGX
  const shipping = subtotal > freeShippingThreshold ? 0 : 15000
  const discount = couponApplied ? subtotal * 0.1 : 0 // 10% discount if coupon applied
  const total = subtotal + shipping - discount

  // Calculate free shipping progress
  useEffect(() => {
    const progress = Math.min((subtotal / freeShippingThreshold) * 100, 100)
    setFreeShippingProgress(progress)
  }, [subtotal])

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US").format(amount)
  }

  // Update item quantity
  const updateQuantity = (id: string, newQuantity: number) => {
    if (newQuantity < 1) return

    const item = cartItems.find((item) => item.id === id)
    if (item && newQuantity > item.stock) {
      // Show stock limit notification
      return
    }

    setCartItems(cartItems.map((item) => (item.id === id ? { ...item, quantity: newQuantity } : item)))
  }

  // Remove item from cart
  const removeItem = (id: string) => {
    setCartItems(cartItems.filter((item) => item.id !== id))
  }

  // Apply coupon code
  const applyCoupon = () => {
    setIsLoading(true)
    setCouponError("")

    // Simulate API call
    setTimeout(() => {
      if (couponCode.toLowerCase() === "discount10") {
        setCouponApplied(true)
        setCouponError("")
      } else {
        setCouponApplied(false)
        setCouponError("Invalid coupon code. Please try again.")
      }
      setIsLoading(false)
    }, 800)
  }

  // Add recommended product to cart
  const addToCart = (product: any) => {
    const existingItem = cartItems.find((item) => item.id === product.id)

    if (existingItem) {
      updateQuantity(product.id, existingItem.quantity + 1)
    } else {
      setCartItems([...cartItems, { ...product, quantity: 1, stock: 10 }])
    }
  }

  // Calculate estimated delivery date (5 days from now)
  const getEstimatedDelivery = () => {
    const date = new Date()
    date.setDate(date.getDate() + 5)
    return date.toLocaleDateString("en-US", { weekday: "long", month: "long", day: "numeric" })
  }

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      <main className="flex-1 py-10">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col gap-2 mb-8">
            <h1 className="text-3xl font-bold tracking-tight">Your Shopping Cart</h1>
            <p className="text-muted-foreground">
              {cartItems.length === 0
                ? "Your cart is empty"
                : `You have ${cartItems.length} item${cartItems.length > 1 ? "s" : ""} in your cart`}
            </p>
          </div>

          {cartItems.length === 0 ? (
            <div className="text-center py-16 bg-white rounded-lg shadow-sm border">
              <div className="mx-auto w-24 h-24 rounded-full bg-gray-100 flex items-center justify-center mb-6">
                <ShoppingBag className="h-12 w-12 text-muted-foreground" />
              </div>
              <h2 className="text-xl font-semibold mb-2">Your cart is empty</h2>
              <p className="text-muted-foreground mb-8 max-w-md mx-auto">
                Looks like you haven't added any products to your cart yet. Browse our shop to find products you'll
                love.
              </p>
              <Button size="lg" asChild>
                <Link href="/shop">Browse Products</Link>
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2 space-y-6">
                {/* Free shipping progress */}
                <Card className="overflow-hidden">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3 mb-2">
                      <Truck className="h-5 w-5 text-primary" />
                      {subtotal >= freeShippingThreshold ? (
                        <p className="text-sm font-medium text-green-600">Your order qualifies for FREE shipping!</p>
                      ) : (
                        <p className="text-sm">
                          Add{" "}
                          <span className="font-medium">UGX {formatCurrency(freeShippingThreshold - subtotal)}</span>{" "}
                          more to qualify for FREE shipping
                        </p>
                      )}
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                      <div
                        className="bg-primary h-2.5 rounded-full transition-all duration-500 ease-out"
                        style={{ width: `${freeShippingProgress}%` }}
                      ></div>
                    </div>
                  </CardContent>
                </Card>

                {/* Cart items */}
                <Card>
                  <CardContent className="p-6">
                    <div className="hidden md:grid grid-cols-12 gap-4 mb-4 text-sm font-medium text-muted-foreground">
                      <div className="col-span-6">Product</div>
                      <div className="col-span-2 text-center">Price</div>
                      <div className="col-span-2 text-center">Quantity</div>
                      <div className="col-span-2 text-right">Total</div>
                    </div>
                    <Separator className="mb-6" />

                    <AnimatePresence>
                      {cartItems.map((item) => {
                        const itemPrice =
                          item.discount > 0 ? item.price - (item.price * item.discount) / 100 : item.price
                        const itemTotal = itemPrice * item.quantity

                        return (
                          <motion.div
                            key={item.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, height: 0, marginBottom: 0 }}
                            transition={{ duration: 0.3 }}
                            className="mb-6 last:mb-0"
                          >
                            <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center bg-white p-4 rounded-lg hover:bg-gray-50 transition-colors">
                              <div className="col-span-6 flex items-center gap-4">
                                <div className="w-20 h-20 rounded-md overflow-hidden bg-gray-100 flex-shrink-0 border">
                                  <Image
                                    src={item.image || "/placeholder.svg"}
                                    alt={item.title}
                                    width={80}
                                    height={80}
                                    className="w-full h-full object-cover"
                                  />
                                </div>
                                <div>
                                  <Link
                                    href={`/products/${item.id}`}
                                    className="font-medium hover:text-primary transition-colors line-clamp-2"
                                  >
                                    {item.title}
                                  </Link>
                                  {item.discount > 0 && (
                                    <Badge variant="outline" className="mt-1 text-primary border-primary">
                                      {item.discount}% OFF
                                    </Badge>
                                  )}
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 p-0 text-muted-foreground hover:text-destructive mt-1"
                                    onClick={() => removeItem(item.id)}
                                  >
                                    <Trash2 className="h-4 w-4 mr-1" />
                                    <span className="text-xs">Remove</span>
                                  </Button>
                                </div>
                              </div>
                              <div className="col-span-2 text-center">
                                <div className="font-medium">UGX {formatCurrency(itemPrice)}</div>
                                {item.discount > 0 && (
                                  <div className="text-xs text-muted-foreground line-through">
                                    UGX {formatCurrency(item.price)}
                                  </div>
                                )}
                              </div>
                              <div className="col-span-2 flex justify-center">
                                <div className="flex items-center border rounded-md shadow-sm">
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <Button
                                          variant="ghost"
                                          size="icon"
                                          className="h-8 w-8 rounded-none"
                                          onClick={() => updateQuantity(item.id, item.quantity - 1)}
                                          disabled={item.quantity <= 1}
                                        >
                                          <Minus className="h-3 w-3" />
                                        </Button>
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <p>Decrease quantity</p>
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>

                                  <span className="w-10 text-center text-sm font-medium">{item.quantity}</span>

                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <Button
                                          variant="ghost"
                                          size="icon"
                                          className="h-8 w-8 rounded-none"
                                          onClick={() => updateQuantity(item.id, item.quantity + 1)}
                                          disabled={item.quantity >= item.stock}
                                        >
                                          <Plus className="h-3 w-3" />
                                        </Button>
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        <p>Increase quantity</p>
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                </div>
                              </div>
                              <div className="col-span-2 text-right font-medium">UGX {formatCurrency(itemTotal)}</div>
                            </div>
                            {item.quantity >= item.stock && (
                              <p className="text-xs text-amber-600 mt-1 text-right">Maximum available quantity</p>
                            )}
                          </motion.div>
                        )
                      })}
                    </AnimatePresence>
                  </CardContent>
                </Card>

                {/* Coupon and continue shopping */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <h3 className="text-base font-medium mb-2">Have a coupon?</h3>
                      <div className="flex">
                        <Input
                          placeholder="Enter coupon code"
                          value={couponCode}
                          onChange={(e) => setCouponCode(e.target.value)}
                          className="rounded-r-none"
                          disabled={isLoading || couponApplied}
                        />
                        <Button
                          onClick={applyCoupon}
                          disabled={!couponCode || isLoading || couponApplied}
                          className="rounded-l-none"
                        >
                          {isLoading ? (
                            <>
                              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                              Applying...
                            </>
                          ) : couponApplied ? (
                            "Applied"
                          ) : (
                            "Apply"
                          )}
                        </Button>
                      </div>
                      {couponError && <p className="text-sm text-destructive mt-1">{couponError}</p>}
                      {couponApplied && (
                        <p className="text-sm text-green-600 mt-1">Coupon applied successfully! 10% discount</p>
                      )}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4 flex items-center justify-between">
                      <div>
                        <h3 className="text-base font-medium">Not ready to checkout?</h3>
                        <p className="text-sm text-muted-foreground">Continue shopping</p>
                      </div>
                      <Button variant="outline" asChild>
                        <Link href="/shop" className="gap-2">
                          <ArrowRight className="h-4 w-4" />
                          Shop
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                </div>

                {/* Recommended products */}
                <div>
                  <h3 className="text-lg font-medium mb-4">You might also like</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    {recommendedProducts.map((product) => (
                      <Card key={product.id} className="overflow-hidden">
                        <div className="aspect-square relative">
                          <Image
                            src={product.image || "/placeholder.svg"}
                            alt={product.title}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <CardContent className="p-4">
                          <h4 className="font-medium line-clamp-1">{product.title}</h4>
                          <div className="flex items-center justify-between mt-2">
                            <div>
                              <div className="font-medium">
                                UGX{" "}
                                {formatCurrency(
                                  product.discount > 0
                                    ? product.price - (product.price * product.discount) / 100
                                    : product.price,
                                )}
                              </div>
                              {product.discount > 0 && (
                                <div className="text-xs text-muted-foreground line-through">
                                  UGX {formatCurrency(product.price)}
                                </div>
                              )}
                            </div>
                            <Button size="sm" variant="outline" onClick={() => addToCart(product)}>
                              Add
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              </div>

              {/* Order summary */}
              <div className="lg:sticky lg:top-20 self-start">
                <Card className="shadow-md border-primary/10">
                  <CardContent className="p-6">
                    <h2 className="text-xl font-bold mb-4">Order Summary</h2>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">
                          Subtotal ({cartItems.reduce((acc, item) => acc + item.quantity, 0)} items)
                        </span>
                        <span>UGX {formatCurrency(subtotal)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Shipping</span>
                        <span>
                          {shipping === 0 ? (
                            <span className="text-green-600">Free</span>
                          ) : (
                            `UGX ${formatCurrency(shipping)}`
                          )}
                        </span>
                      </div>
                      {couponApplied && (
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Discount (10%)</span>
                          <span className="text-destructive">-UGX {formatCurrency(discount)}</span>
                        </div>
                      )}
                      <Separator />
                      <div className="flex justify-between font-bold text-lg">
                        <span>Total</span>
                        <span>UGX {formatCurrency(total)}</span>
                      </div>

                      {/* Estimated delivery */}
                      <div className="bg-gray-50 p-3 rounded-md mt-4">
                        <div className="flex items-center gap-2 text-sm">
                          <Truck className="h-4 w-4 text-muted-foreground" />
                          <span>
                            Estimated delivery by <span className="font-medium">{getEstimatedDelivery()}</span>
                          </span>
                        </div>
                      </div>
                    </div>

                    <Button className="w-full mt-6" size="lg" asChild>
                      <Link href="/checkout">Proceed to Checkout</Link>
                    </Button>

                    {/* Trust badges */}
                    <div className="mt-6 space-y-4">
                      <div className="flex items-center justify-between text-sm text-muted-foreground">
                        <div className="flex items-center gap-1.5">
                          <ShieldCheck className="h-4 w-4" />
                          <span>Secure Checkout</span>
                        </div>
                        <div className="flex items-center gap-1.5">
                          <RefreshCw className="h-4 w-4" />
                          <span>30-Day Returns</span>
                        </div>
                      </div>

                      <div>
                        <h3 className="font-medium text-sm mb-2">We Accept</h3>
                        <div className="flex gap-2">
                          <div className="w-12 h-8 bg-gray-100 rounded flex items-center justify-center">
                            <Image src="/placeholder.svg?height=30&width=40" alt="Visa" width={40} height={30} />
                          </div>
                          <div className="w-12 h-8 bg-gray-100 rounded flex items-center justify-center">
                            <Image src="/placeholder.svg?height=30&width=40" alt="Mastercard" width={40} height={30} />
                          </div>
                          <div className="w-12 h-8 bg-gray-100 rounded flex items-center justify-center">
                            <Image
                              src="/placeholder.svg?height=30&width=40"
                              alt="Mobile Money"
                              width={40}
                              height={30}
                            />
                          </div>
                          <div className="w-12 h-8 bg-gray-100 rounded flex items-center justify-center">
                            <Image src="/placeholder.svg?height=30&width=40" alt="PayPal" width={40} height={30} />
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}

