import type { Metada<PERSON> } from "next"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import AdminContentBanners from "@/components/admin/admin-content-banners"
import AdminContentPages from "@/components/admin/admin-content-pages"

export const metadata: Metadata = {
  title: "Content Management | Admin Dashboard",
  description: "Content management for STXpress ecommerce platform",
}

export default function ContentPage() {
  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Content Management</h2>
      </div>

      <Tabs defaultValue="banners" className="space-y-4">
        <TabsList>
          <TabsTrigger value="banners">Banners</TabsTrigger>
          <TabsTrigger value="pages">Pages</TabsTrigger>
          <TabsTrigger value="blog">Blog</TabsTrigger>
          <TabsTrigger value="media">Media Library</TabsTrigger>
        </TabsList>

        <TabsContent value="banners" className="space-y-4">
          <AdminContentBanners />
        </TabsContent>

        <TabsContent value="pages" className="space-y-4">
          <AdminContentPages />
        </TabsContent>

        <TabsContent value="blog" className="space-y-4">
          <div className="rounded-md border p-4">
            <h3 className="text-lg font-medium">Blog Management</h3>
            <p className="text-sm text-muted-foreground mt-2">
              Blog management functionality will be implemented here.
            </p>
          </div>
        </TabsContent>

        <TabsContent value="media" className="space-y-4">
          <div className="rounded-md border p-4">
            <h3 className="text-lg font-medium">Media Library</h3>
            <p className="text-sm text-muted-foreground mt-2">
              Media library management functionality will be implemented here.
            </p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

