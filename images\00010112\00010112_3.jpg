



        
        
        
          





  








    
  











































    














    










    
  



    
  



<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <meta name="ahot-aplus" content="1">
  <meta name="aplus-rate-ahot" content="0.5">
  <title>阿里云万网虚机过期页面</title>
  <link rel="icon" href="https://img.alicdn.com/tfs/TB1_ZXuNcfpK1RjSZFOXXa6nFXa-32-32.ico" type="image/x-icon">
  <meta name="data-spm" content="5176" />
  <meta name="keywords" content="">
  <meta name="description" content="">
  <meta property="og:title" content="阿里云万网虚机过期页面" />
  <meta property="og:description" content="" />
  <meta property="og:image" content="https://img.alicdn.com/tfs/TB1qNVdXlGw3KVjSZFDXXXWEpXa-620-620.png" />
  
  
    
  
  
  <style>
    body {
      min-height:100vh;
      margin:0;
      padding:0;
      
        background:;
      ;
    }
    .seo-html-content{
      display:none!important;
    }
    @media screen and (max-width: 768px) {
      body {
        overflow-x: hidden;
        font-size: 12px;
      }
    }
    
      div.small-icon .cart-name{
        display: none;
      }
    
  </style>

  
  
    <script type="text/javascript" src="//www.aliyun.com/rgn/aliyun_assets?renderer=js"></script>
<script src="//www.aliyun.com/assets/responsive_assets/index.js"></script>
<script type="text/javascript" src="//g.alicdn.com/kissy/k/1.4.4/seed-min.js" charset="utf-8"
  data-config="{combine:true}"></script>

  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  

  
  
  
  
  
  
  
    
  
  
  
  
  
  
  
    
  
  
  
  
  
  
  
    
  
  
  
  
  
  
  
    
  
  
  



  

  

  

  

  

  

  

  

  
    
  






  
  <script>
    window.$PAGE_CONFIG = {
      id:'105028',
      env: 'production',
      previewEnv: '',
      
      PLUGINS: {"plugin_theme":{"title":"阿里云官网白橙主题","mode":"light","scene":"channel","c1":"#ff6a00","l1":"#fff0e5","l2":"#ffe1cc","l3":"#ffb580","l4":"#ff8833","d1":"#cc5500","d2":"#994000","d3":"#662a00","d4":"#4d2000"}},
      renderEngine:1,
      SOLUTION_HEADER_FOOTER_CONFG:{"headerSection":"完整头部","footerSection":"展示"}
    };
  </script>
  
  
    
  
  <script>
    if (window.innerWidth <= 768) {
      var meta = document.querySelector('meta[name="viewport"]');
      meta.content = 'width=375px,user-scalable=no,viewport-fit=cover';
      meta.setAttribute('homon','true');
    }
  </script>
  
  
  

  
</head>

<body data-spm="8048696"><script  type="text/javascript">
(function  (d)  {
var t=d.createElement("script");t.type="text/javascript";t.async=true;t.id="tb-beacon-aplus";
t.setAttribute("exparams","category=&userid=&aplus&yunid=&yunpk=&current_pk=&channel=&cps=");
t.src="//g.alicdn.com/alilog/mlog/aplus_v2.js";
d.getElementsByTagName("head")[0].appendChild(t);
})(document);
</script>


    <script src="https://www.aliyun.com/assets/publish/ace-base-assets"></script>
    
    <link rel="stylesheet" href="https://g.alicdn.com/homon/page-render/3.0.17-fix/v1/index-pc.css" />
  
    
    <link rel="stylesheet" href="https://g.alicdn.com/??hmod/ace-website-general-banner/0.0.48/index.css,hmod/ace-standard-title/0.0.16/index.css,hmod/ace-dpl-step-solution-step/0.0.18/index.css,hmod/ace-dpl-deversion-corset-2/0.0.4/index.css" />
    
    

  
  <div id="J_fetch">
  <textarea style="display: none">
    {}
  </textarea>
 </div>
  <textarea id="J_data" style="display: none;">
    [{"settings":{},"componentId":34317,"hidden":"false","fullName":"@ali/hmod-ace-website-general-banner","uuid":"1566867500","limitConfig":{},"version":"0.0.48","componentVersionId":358994,"name":"hmod-ace-website-general-banner"},{"settings":{},"componentId":33306,"hidden":"false","fullName":"@ali/hmod-ace-standard-title","uuid":"5395834640","limitConfig":{},"version":"0.0.16","componentVersionId":320793,"name":"hmod-ace-standard-title"},{"settings":{},"componentId":35984,"hidden":"false","fullName":"@ali/hmod-ace-dpl-step-solution-step","uuid":"7109661920","limitConfig":{},"version":"0.0.18","componentVersionId":350081,"name":"hmod-ace-dpl-step-solution-step"},{"settings":{},"componentId":37893,"hidden":"false","fullName":"@ali/hmod-ace-dpl-deversion-corset-2","uuid":"**********","limitConfig":{},"version":"0.0.4","componentVersionId":297743,"name":"hmod-ace-dpl-deversion-corset-2"}]
  </textarea>


  
  
  
  

  <script src="//www.aliyun.com/assets/publish/ace-topbar"></script>


  












  






  
  
  
  
  
  
  
  
            
              
              
              
              



<div id="J_1566867500"></div>


<div data-homon-module class="aliyun-module" data-section-id="" data-id="1566867500" data-spm="J_1566867500" data-module-id="1566867500"
  data-module-name="@ali/hmod-ace-website-general-banner" data-version="0.0.48" data-hidden="false"
  data-block-render=""
  
  >
  
</div>
<div class="aliyun-module-config">
  <textarea class="aliyun-module-data" data-target="1566867500" style="display:none;">
          {"operatSetting":{"operatTitle":{"mainTitle":"该网站因主机过期暂时无法访问","subTitle":"开通方法：续费后网站即可开通","mainTitleH5":"该网站因主机过期暂时无法访问","subTitleH5":"开通方法：续费后网站即可开通"},"actionArray":[{"actionScenario":"default","actionRuleType":"vice","diyAction":{"diyBack":"","diyColor":"","diyHover":""},"actionText":"续费流程帮助","actionLink":"https://help.aliyun.com/knowledge_detail/36202.html"}]},"visualSetting":{"textStyle":"dark","backSetting":{"backImage":"https://img.alicdn.com/tfs/TB1FkZgbjMZ7e4jSZFOXXX7epXa-1798-404.png","backImageH5":"","openAutoHeight":false},"titleSetting":{"titleLocation":"left","titleImage":""}}}
        </textarea>
  <textarea class="aliyun-module-settings" data-target="1566867500" style="display:none;">
          {"recommend":{"visibleMode":"default"},"anchor":{"visibleMode":"all"},"pc":{"background":"","horizontal":"默认","verticalNumber":0,"marginTop":"0","marginBottom":"0","contentBackground":"","paddingHoz":"0","paddingTop":"0","paddingBottom":"0","backgroundImage":"","autoHeight":false},"h5":{"background":"","horizontal":"默认","verticalNumber":0,"marginTop":"0","marginBottom":"0","contentBackground":"","paddingHoz":"0","paddingTop":"0","paddingBottom":"0","backgroundImage":"","autoHeight":false}}
        </textarea>
</div>

              
            
  


  
  
  
  
  
            
              
              
              
              



<div id="J_5395834640"></div>


<div data-homon-module class="aliyun-module" data-section-id="" data-id="5395834640" data-spm="J_5395834640" data-module-id="5395834640"
  data-module-name="@ali/hmod-ace-standard-title" data-version="0.0.16" data-hidden="false"
  data-block-render=""
  
  >
  
</div>
<div class="aliyun-module-config">
  <textarea class="aliyun-module-data" data-target="5395834640" style="display:none;">
          {"textConfig":{"title":"续费流程","subtitle":"","actions":[]},"uiConfig":{"layoutMode":"center"}}
        </textarea>
  <textarea class="aliyun-module-settings" data-target="5395834640" style="display:none;">
          {"recommend":{"visibleMode":"default"},"anchor":{"visibleMode":"all"},"pc":{"background":"","horizontal":"默认","verticalNumber":0,"marginTop":"0","marginBottom":"0","contentBackground":"","paddingHoz":"0","paddingTop":"0","paddingBottom":"0","backgroundImage":"","autoHeight":false},"h5":{"background":"","horizontal":"默认","verticalNumber":0,"marginTop":"0","marginBottom":"0","contentBackground":"","paddingHoz":"0","paddingTop":"0","paddingBottom":"0","backgroundImage":"","autoHeight":false}}
        </textarea>
</div>

              
            
  


  
  
  
  
  
            
              
              
              
              



<div id="J_7109661920"></div>


<div data-homon-module class="aliyun-module" data-section-id="" data-id="7109661920" data-spm="J_7109661920" data-module-id="7109661920"
  data-module-name="@ali/hmod-ace-dpl-step-solution-step" data-version="0.0.18" data-hidden="false"
  data-block-render=""
  
  >
  
</div>
<div class="aliyun-module-config">
  <textarea class="aliyun-module-data" data-target="7109661920" style="display:none;">
          {"textConfig":{"simplifiedDisplay":"concise","switchTheme":"grey","dataSource":[{"mainTitle":"登陆会员中心"},{"mainTitle":"万网主机管理"},{"mainTitle":"继续续费主机"},{"mainTitle":"选择要续费的主机"},{"mainTitle":"提交续费订单并结算"}]}}
        </textarea>
  <textarea class="aliyun-module-settings" data-target="7109661920" style="display:none;">
          {"recommend":{"visibleMode":"default"},"anchor":{"visibleMode":"all"},"pc":{"background":"","horizontal":"默认","verticalNumber":0,"marginTop":"0","marginBottom":"0","contentBackground":"","paddingHoz":"0","paddingTop":"0","paddingBottom":"0","backgroundImage":"","autoHeight":false},"h5":{"background":"","horizontal":"默认","verticalNumber":0,"marginTop":"0","marginBottom":"0","contentBackground":"","paddingHoz":"0","paddingTop":"0","paddingBottom":"0","backgroundImage":"","autoHeight":false}}
        </textarea>
</div>

              
            
  


  
  
  
  
  
            
              
              
              
              



<div id="J_**********"></div>


<div data-homon-module class="aliyun-module" data-section-id="" data-id="**********" data-spm="J_**********" data-module-id="**********"
  data-module-name="@ali/hmod-ace-dpl-deversion-corset-2" data-version="0.0.4" data-hidden="false"
  data-block-render=""
  
  >
  
</div>
<div class="aliyun-module-config">
  <textarea class="aliyun-module-data" data-target="**********" style="display:none;">
          {"textConfig":{"follow":"right","corsetList":[{"title":"特别提示","descList":[{"title":"","content":"超过15天不续费，数据会被清空。如果需要备份数据，请在15天内通过FTP及时备份","titleH5":"","contentH5":""}],"link":"https://help.aliyun.com/knowledge_detail/36135.html","linktext":"备份帮助"},{"descList":[{"content":"过期续费后如果仍然打不开站点请尝试重启虚拟主机，问题如未解决请提交工单"}],"linktext":"提交工单","link":"https://account.aliyun.com/login/login.htm?oauth_callback=https%3A//ticket.console.aliyun.com/%23/ticket/add%3FproductCode%3Dhost_xuni%26commonQuestionId%3D269%26isSmart%3Dtrue%26iatraceid%3D1594368822092-2fbe36f6cc2a38d6ede9b4%26channel%3Dselfservice"},{"title":"","descList":[{"content":"或者您可以先逛逛这里：虚拟主机帮助文档"}],"linktext":"帮助文档","link":"https://help.aliyun.com/product/35465.html"}]},"uiConfig":{"BgColorlist":[{"bgimg":"","bgimgH5":""},{"bgimg":"","bgimgH5":""},{"bgimg":"","bgimgH5":""}]}}
        </textarea>
  <textarea class="aliyun-module-settings" data-target="**********" style="display:none;">
          {"recommend":{"visibleMode":"default"},"anchor":{"visibleMode":"all"},"pc":{"background":"","horizontal":"默认","verticalNumber":0,"marginTop":"0","marginBottom":"0","contentBackground":"","paddingHoz":"0","paddingTop":"0","paddingBottom":"0","backgroundImage":"","autoHeight":false},"h5":{"background":"","horizontal":"默认","verticalNumber":0,"marginTop":"0","marginBottom":"0","contentBackground":"","paddingHoz":"0","paddingTop":"0","paddingBottom":"0","backgroundImage":"","autoHeight":false}}
        </textarea>
</div>

              
            
  


  


  
    <script>
  // 遍历所有tms模块
  $('[data-tms-module]').each(function (index, dom) {
    $(dom).find("textarea").each(function (index, dom) {
      var value = $(dom).val();
      if (value == undefined) {
        console.log(dom, '没有发现数据');
      }
      try {
        var newValue = JSON.parse(value) || {};
        newValue = newValue.$context;
        if (newValue) {
          $(dom).val(JSON.stringify(newValue));
        }
      } catch (error) {
        console.log(dom, '解析出现问题');
      }
    });
  });

</script>

  
  

  
  <script src="https://g.alicdn.com/??hmod/ace-website-general-banner/0.0.48/index.js,hmod/ace-website-general-banner/0.0.48/services.js,hmod/ace-standard-title/0.0.16/index.js,hmod/ace-standard-title/0.0.16/services.js,hmod/ace-dpl-step-solution-step/0.0.18/index.js,hmod/ace-dpl-step-solution-step/0.0.18/services.js,hmod/ace-dpl-deversion-corset-2/0.0.4/index.js,hmod/ace-dpl-deversion-corset-2/0.0.4/services.js"></script>
  



  
  <script
    src="https://g.alicdn.com/homon/page-render/3.0.17-fix/v1/index-pc.js">
  </script>


  
  
  
  <script src="//www.aliyun.com/assets/publish/ace-footer"></script>
  
</body>

</html>






          
            
          
        
      