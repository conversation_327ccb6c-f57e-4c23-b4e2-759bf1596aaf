"use client"

import { ChevronDown } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

export function ProductSort() {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="w-full md:w-auto justify-between">
          <span>Sort By: Featured</span>
          <ChevronDown className="ml-2 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[200px]">
        <DropdownMenuItem className="cursor-pointer">Featured</DropdownMenuItem>
        <DropdownMenuItem className="cursor-pointer">Price: Low to High</DropdownMenuItem>
        <DropdownMenuItem className="cursor-pointer">Price: High to Low</DropdownMenuItem>
        <DropdownMenuItem className="cursor-pointer">Newest Arrivals</DropdownMenuItem>
        <DropdownMenuItem className="cursor-pointer">Best Selling</DropdownMenuItem>
        <DropdownMenuItem className="cursor-pointer">Customer Rating</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

