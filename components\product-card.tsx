"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { Heart, ShoppingCart, Star, Eye } from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"

interface ProductCardProps {
  title: string
  price: number
  currency?: string
  image: string
  discount?: number
  rating?: number
  href: string
  badge?: string
  id: string
}

export function ProductCard({
  title,
  price,
  currency = "UGX",
  image,
  discount = 0,
  rating = 0,
  href,
  badge,
  id,
}: ProductCardProps) {
  const [isWishlisted, setIsWishlisted] = useState(false)
  const [isHovered, setIsHovered] = useState(false)

  const formattedPrice = new Intl.NumberFormat("en-US").format(price)
  const discountedPrice = discount > 0 ? price - (price * discount) / 100 : price
  const formattedDiscountedPrice = new Intl.NumberFormat("en-US").format(discountedPrice)

  return (
    <Card
      className="overflow-hidden group transition-all duration-300 hover:shadow-md border-muted"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="relative">
        <Link href={`/products/${id}`} className="block">
          <div className="aspect-square overflow-hidden bg-muted/20">
            <Image
              src={image || "/placeholder.svg"}
              alt={title}
              width={300}
              height={300}
              className="object-cover w-full h-full transition-transform duration-500 group-hover:scale-105"
            />
          </div>
        </Link>
        {discount > 0 && (
          <Badge className="absolute top-2 left-2 bg-destructive hover:bg-destructive">-{discount}%</Badge>
        )}
        {badge && <Badge className="absolute top-2 left-2 bg-primary hover:bg-primary">{badge}</Badge>}
        <div className="absolute top-2 right-2 flex flex-col gap-2">
          <Button
            variant="secondary"
            size="icon"
            className="h-8 w-8 rounded-full opacity-0 group-hover:opacity-100 transition-opacity shadow-md"
            onClick={() => setIsWishlisted(!isWishlisted)}
          >
            <Heart className={cn("h-4 w-4", isWishlisted ? "fill-destructive text-destructive" : "")} />
          </Button>
          <Link href={`/products/${id}`}>
            <Button
              variant="secondary"
              size="icon"
              className="h-8 w-8 rounded-full opacity-0 group-hover:opacity-100 transition-opacity shadow-md"
            >
              <Eye className="h-4 w-4" />
            </Button>
          </Link>
        </div>
        <div
          className={cn(
            "absolute left-0 right-0 bottom-0 bg-background p-2 transform transition-transform duration-300",
            isHovered ? "translate-y-0" : "translate-y-full",
          )}
        >
          <Button
            variant="default"
            size="sm"
            className="w-full gap-2"
            onClick={() => {
              // In a real app, this would dispatch to a cart context/store
              console.log(`Added ${title} to cart`)
            }}
          >
            <ShoppingCart className="h-4 w-4" />
            Add to Cart
          </Button>
        </div>
      </div>
      <CardContent className="p-4">
        <Link href={`/products/${id}`} className="block">
          <h3 className="font-medium text-sm line-clamp-2 mb-2 hover:text-primary transition-colors">{title}</h3>
        </Link>
        <div className="flex items-center gap-2 mb-2">
          {rating > 0 && (
            <div className="flex items-center">
              <Star className="h-3.5 w-3.5 fill-primary text-primary" />
              <span className="text-xs ml-1 text-muted-foreground">{rating.toFixed(1)}</span>
            </div>
          )}
        </div>
        <div className="flex items-center gap-2">
          <p className="font-semibold">
            {currency} {formattedDiscountedPrice}
          </p>
          {discount > 0 && (
            <p className="text-sm text-muted-foreground line-through">
              {currency} {formattedPrice}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

