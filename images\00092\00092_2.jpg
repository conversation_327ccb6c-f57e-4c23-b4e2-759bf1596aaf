<!DOCTYPE html>
<html lang="en">
  <head>
    <style data-vite-theme="" data-inject-first="">:root {
      --background: 262 77% 82%;
--foreground: 273 100% 12%;
--muted: 264 73% 73%;
--muted-foreground: 273 100% 12%;
--popover: 260 100% 99%;
--popover-foreground: 273 100% 12%;
--card: 260 100% 99%;
--card-foreground: 273 100% 12%;
--border: 264 73% 73%;
--input: 264 73% 73%;
--primary: 265 100% 75%;
--primary-foreground: 259 100% 99%;
--secondary: 262 100% 89%;
--secondary-foreground: 273 100% 12%;
--accent: 262 100% 89%;
--accent-foreground: 273 100% 12%;
--destructive: 0 84.2% 60.2%;
--destructive-foreground: 60 9.1% 97.8%;
--ring: 265 100% 75%;
--radius: 0.5rem;
  }
  .dark {
      --background: 258 100% 1%;
--foreground: 260 100% 97%;
--muted: 268 52% 36%;
--muted-foreground: 260 100% 97%;
--popover: 268 71% 12%;
--popover-foreground: 260 100% 97%;
--card: 268 71% 12%;
--card-foreground: 260 100% 97%;
--border: 268 52% 36%;
--input: 268 52% 36%;
--primary: 266 70% 62%;
--primary-foreground: 259 85% 99%;
--secondary: 268 52% 36%;
--secondary-foreground: 260 100% 97%;
--accent: 268 52% 36%;
--accent-foreground: 260 100% 97%;
--destructive: 0 62.8% 30.6%;
--destructive-foreground: 0 0% 98%;
--ring: 266 70% 62%;
--radius: 0.5rem;
  }</style>

    <script type="module">
import { createHotContext } from "/@vite/client";
const hot = createHotContext("/__dummy__runtime-error-plugin");

function sendError(error) {
  if (!(error instanceof Error)) {
    error = new Error("(unknown runtime error)");
  }
  const serialized = {
    message: error.message,
    stack: error.stack,
  };
  hot.send("runtime-error-plugin:error", serialized);
}

window.addEventListener("error", (evt) => {
  sendError(evt.error);
});

window.addEventListener("unhandledrejection", (evt) => {
  sendError(evt.reason);
});
</script>

    <script type="module">
import RefreshRuntime from "/@react-refresh"
RefreshRuntime.injectIntoGlobalHook(window)
window.$RefreshReg$ = () => {}
window.$RefreshSig$ = () => (type) => type
window.__vite_plugin_react_preamble_installed__ = true
</script>

    <script type="module" src="/@vite/client"></script>

    <meta charset="UTF-8" />
    <link rel="icon" type="image/png" href="/images/logo.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Advaana Inc. specializes in Google Cloud solutions with AI integration at every step. As a trusted Google Cloud partner, we transform businesses through cloud innovation." />
    <title>Advaana Inc. - Google Cloud Solutions & AI Specialists</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.remixicon.com/releases/v3.5.0/remixicon.css" rel="stylesheet">
    <script type="module">"use strict";(()=>{var F="0.0.11";var T={HIGHLIGHT_COLOR:"#0079F2",HIGHLIGHT_BG:"#0079F210",ALLOWED_DOMAIN:".replit.dev"},I={highlighter:{position:"absolute",zIndex:Number.MAX_SAFE_INTEGER-3,boxSizing:"border-box",pointerEvents:"none",border:`2px solid ${T.HIGHLIGHT_COLOR}`,borderRadius:"4px",background:T.HIGHLIGHT_BG,transition:"opacity 0.2s",willChange:"opacity",opacity:"0"},label:{position:"absolute",backgroundColor:T.HIGHLIGHT_COLOR,color:"#FFFFFF",padding:"2px 6px",borderRadius:"4px",fontSize:"12px",fontFamily:"monospace",transform:"translateY(-100%)",marginTop:"-4px",zIndex:Number.MAX_SAFE_INTEGER-2,pointerEvents:"none",opacity:"0"}};function Le(e,n){return e[13]=1,e[14]=n>>8,e[15]=n&255,e[16]=n>>8,e[17]=n&255,e}var ne=112,re=72,oe=89,ie=115,W;function Ne(){let e=new Int32Array(256);for(let n=0;n<256;n++){let t=n;for(let r=0;r<8;r++)t=t&1?3988292384^t>>>1:t>>>1;e[n]=t}return e}function Ie(e){let n=-1;W||(W=Ne());for(let t=0;t<e.length;t++)n=W[(n^e[t])&255]^n>>>8;return n^-1}function xe(e){let n=e.length-1;for(let t=n;t>=4;t--)if(e[t-4]===9&&e[t-3]===ne&&e[t-2]===re&&e[t-1]===oe&&e[t]===ie)return t-3;return 0}function De(e,n,t=!1){let r=new Uint8Array(13);n*=39.3701,r[0]=ne,r[1]=re,r[2]=oe,r[3]=ie,r[4]=n>>>24,r[5]=n>>>16,r[6]=n>>>8,r[7]=n&255,r[8]=r[4],r[9]=r[5],r[10]=r[6],r[11]=r[7],r[12]=1;let s=Ie(r),i=new Uint8Array(4);if(i[0]=s>>>24,i[1]=s>>>16,i[2]=s>>>8,i[3]=s&255,t){let a=xe(e);return e.set(r,a),e.set(i,a+13),e}else{let a=new Uint8Array(4);a[0]=0,a[1]=0,a[2]=0,a[3]=9;let o=new Uint8Array(54);return o.set(e,0),o.set(a,33),o.set(r,37),o.set(i,50),o}}var se="[modern-screenshot]",A=typeof window<"u",_e=A&&"Worker"in window,Me=A&&"atob"in window,Wt=A&&"btoa"in window,j=A?window.navigator?.userAgent:"",ae=j.includes("Chrome"),O=j.includes("AppleWebKit")&&!ae,V=j.includes("Firefox"),He=e=>e&&"__CONTEXT__"in e,Re=e=>e.constructor.name==="CSSFontFaceRule",Pe=e=>e.constructor.name==="CSSImportRule",v=e=>e.nodeType===1,M=e=>typeof e.className=="object",le=e=>e.tagName==="image",Fe=e=>e.tagName==="use",x=e=>v(e)&&typeof e.style<"u"&&!M(e),Oe=e=>e.nodeType===8,ke=e=>e.nodeType===3,N=e=>e.tagName==="IMG",k=e=>e.tagName==="VIDEO",Be=e=>e.tagName==="CANVAS",Ue=e=>e.tagName==="TEXTAREA",$e=e=>e.tagName==="INPUT",We=e=>e.tagName==="STYLE",Ge=e=>e.tagName==="SCRIPT",je=e=>e.tagName==="SELECT",Ve=e=>e.tagName==="SLOT",ze=e=>e.tagName==="IFRAME",qe=(...e)=>console.warn(se,...e);function Xe(e){let n=e?.createElement?.("canvas");return n&&(n.height=n.width=1),!!n&&"toDataURL"in n&&!!n.toDataURL("image/webp").includes("image/webp")}var G=e=>e.startsWith("data:");function ce(e,n){if(e.match(/^[a-z]+:\/\//i))return e;if(A&&e.match(/^\/\//))return window.location.protocol+e;if(e.match(/^[a-z]+:/i)||!A)return e;let t=B().implementation.createHTMLDocument(),r=t.createElement("base"),s=t.createElement("a");return t.head.appendChild(r),t.body.appendChild(s),n&&(r.href=n),s.href=e,s.href}function B(e){return(e&&v(e)?e?.ownerDocument:e)??window.document}var U="http://www.w3.org/2000/svg";function Ye(e,n,t){let r=B(t).createElementNS(U,"svg");return r.setAttributeNS(null,"width",e.toString()),r.setAttributeNS(null,"height",n.toString()),r.setAttributeNS(null,"viewBox",`0 0 ${e} ${n}`),r}function Je(e,n){let t=new XMLSerializer().serializeToString(e);return n&&(t=t.replace(/[\u0000-\u0008\v\f\u000E-\u001F\uD800-\uDFFF\uFFFE\uFFFF]/gu,"")),`data:image/svg+xml;charset=utf-8,${encodeURIComponent(t)}`}async function Ke(e,n="image/png",t=1){try{return await new Promise((r,s)=>{e.toBlob(i=>{i?r(i):s(new Error("Blob is null"))},n,t)})}catch(r){if(Me)return Qe(e.toDataURL(n,t));throw r}}function Qe(e){let[n,t]=e.split(","),r=n.match(/data:(.+);/)?.[1]??void 0,s=window.atob(t),i=s.length,a=new Uint8Array(i);for(let o=0;o<i;o+=1)a[o]=s.charCodeAt(o);return new Blob([a],{type:r})}function ue(e,n){return new Promise((t,r)=>{let s=new FileReader;s.onload=()=>t(s.result),s.onerror=()=>r(s.error),s.onabort=()=>r(new Error(`Failed read blob to ${n}`)),n==="dataUrl"?s.readAsDataURL(e):n==="arrayBuffer"&&s.readAsArrayBuffer(e)})}var Ze=e=>ue(e,"dataUrl"),et=e=>ue(e,"arrayBuffer");function L(e,n){let t=B(n).createElement("img");return t.decoding="sync",t.loading="eager",t.src=e,t}function D(e,n){return new Promise(t=>{let{timeout:r,ownerDocument:s,onError:i,onWarn:a}=n??{},o=typeof e=="string"?L(e,B(s)):e,c=null,d=null;function l(){t(o),c&&clearTimeout(c),d?.()}if(r&&(c=setTimeout(l,r)),k(o)){let u=o.currentSrc||o.src;if(!u)return o.poster?D(o.poster,n).then(t):l();if(o.readyState>=2)return l();let h=l,f=g=>{a?.("Failed video load",u,g),i?.(g),l()};d=()=>{o.removeEventListener("loadeddata",h),o.removeEventListener("error",f)},o.addEventListener("loadeddata",h,{once:!0}),o.addEventListener("error",f,{once:!0})}else{let u=le(o)?o.href.baseVal:o.currentSrc||o.src;if(!u)return l();let h=async()=>{if(N(o)&&"decode"in o)try{await o.decode()}catch(g){a?.("Failed to decode image, trying to render anyway",o.dataset.originalSrc||u,g)}l()},f=g=>{a?.("Failed image load",o.dataset.originalSrc||u,g),l()};if(N(o)&&o.complete)return h();d=()=>{o.removeEventListener("load",h),o.removeEventListener("error",f)},o.addEventListener("load",h,{once:!0}),o.addEventListener("error",f,{once:!0})}})}async function tt(e,n){x(e)&&(N(e)||k(e)?await D(e,n):await Promise.all(["img","video"].flatMap(t=>Array.from(e.querySelectorAll(t)).map(r=>D(r,n)))))}var de=function(){let n=0,t=()=>`0000${(Math.random()*36**4<<0).toString(36)}`.slice(-4);return()=>(n+=1,`u${t()}${n}`)}();function he(e){return e?.split(",").map(n=>n.trim().replace(/"|'/g,"").toLowerCase()).filter(Boolean)}var J=0;function nt(e){let n=`${se}[#${J}]`;return J++,{time:t=>e&&console.time(`${n} ${t}`),timeEnd:t=>e&&console.timeEnd(`${n} ${t}`),warn:(...t)=>e&&qe(...t)}}function rt(e){return{cache:e?"no-cache":"force-cache"}}async function z(e,n){return He(e)?e:ot(e,{...n,autoDestruct:!0})}async function ot(e,n){let{scale:t=1,workerUrl:r,workerNumber:s=1}=n||{},i=!!n?.debug,a=n?.features??!0,o=e.ownerDocument??(A?window.document:void 0),c=e.ownerDocument?.defaultView??(A?window:void 0),d=new Map,l={width:0,height:0,quality:1,type:"image/png",scale:t,backgroundColor:null,style:null,filter:null,maximumCanvasSize:0,timeout:3e4,progress:null,debug:i,fetch:{requestInit:rt(n?.fetch?.bypassingCache),placeholderImage:"data:image/png;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",bypassingCache:!1,...n?.fetch},fetchFn:null,font:{},drawImageInterval:100,workerUrl:null,workerNumber:s,onCloneNode:null,onEmbedNode:null,onCreateForeignObjectSvg:null,includeStyleProperties:null,autoDestruct:!1,...n,__CONTEXT__:!0,log:nt(i),node:e,ownerDocument:o,ownerWindow:c,dpi:t===1?null:96*t,svgStyleElement:ge(o),svgDefsElement:o?.createElementNS(U,"defs"),svgStyles:new Map,defaultComputedStyles:new Map,workers:[...Array.from({length:_e&&r&&s?s:0})].map(()=>{try{let f=new Worker(r);return f.onmessage=async g=>{let{url:m,result:p}=g.data;p?d.get(m)?.resolve?.(p):d.get(m)?.reject?.(new Error(`Error receiving message from worker: ${m}`))},f.onmessageerror=g=>{let{url:m}=g.data;d.get(m)?.reject?.(new Error(`Error receiving message from worker: ${m}`))},f}catch(f){return l.log.warn("Failed to new Worker",f),null}}).filter(Boolean),fontFamilies:new Map,fontCssTexts:new Map,acceptOfImage:`${[Xe(o)&&"image/webp","image/svg+xml","image/*","*/*"].filter(Boolean).join(",")};q=0.8`,requests:d,drawImageCount:0,tasks:[],features:a,isEnable:f=>f==="restoreScrollPosition"?typeof a=="boolean"?!1:a[f]??!1:typeof a=="boolean"?a:a[f]??!0};l.log.time("wait until load"),await tt(e,{timeout:l.timeout,onWarn:l.log.warn}),l.log.timeEnd("wait until load");let{width:u,height:h}=it(e,l);return l.width=u,l.height=h,l}function ge(e){if(!e)return;let n=e.createElement("style"),t=n.ownerDocument.createTextNode(`
.______background-clip--text {
  background-clip: text;
  -webkit-background-clip: text;
}
`);return n.appendChild(t),n}function it(e,n){let{width:t,height:r}=n;if(v(e)&&(!t||!r)){let s=e.getBoundingClientRect();t=t||s.width||Number(e.getAttribute("width"))||0,r=r||s.height||Number(e.getAttribute("height"))||0}return{width:t,height:r}}async function st(e,n){let{log:t,timeout:r,drawImageCount:s,drawImageInterval:i}=n;t.time("image to canvas");let a=await D(e,{timeout:r,onWarn:n.log.warn}),{canvas:o,context2d:c}=at(e.ownerDocument,n),d=()=>{try{c?.drawImage(a,0,0,o.width,o.height)}catch(l){n.log.warn("Failed to drawImage",l)}};if(d(),n.isEnable("fixSvgXmlDecode"))for(let l=0;l<s;l++)await new Promise(u=>{setTimeout(()=>{d(),u()},l+i)});return n.drawImageCount=0,t.timeEnd("image to canvas"),o}function at(e,n){let{width:t,height:r,scale:s,backgroundColor:i,maximumCanvasSize:a}=n,o=e.createElement("canvas");o.width=Math.floor(t*s),o.height=Math.floor(r*s),o.style.width=`${t}px`,o.style.height=`${r}px`,a&&(o.width>a||o.height>a)&&(o.width>a&&o.height>a?o.width>o.height?(o.height*=a/o.width,o.width=a):(o.width*=a/o.height,o.height=a):o.width>a?(o.height*=a/o.width,o.width=a):(o.width*=a/o.height,o.height=a));let c=o.getContext("2d");return c&&i&&(c.fillStyle=i,c.fillRect(0,0,o.width,o.height)),{canvas:o,context2d:c}}function fe(e,n){if(e.ownerDocument)try{let i=e.toDataURL();if(i!=="data:,")return L(i,e.ownerDocument)}catch(i){n.log.warn("Failed to clone canvas",i)}let t=e.cloneNode(!1),r=e.getContext("2d"),s=t.getContext("2d");try{return r&&s&&s.putImageData(r.getImageData(0,0,e.width,e.height),0,0),t}catch(i){n.log.warn("Failed to clone canvas",i)}return t}function lt(e,n){try{if(e?.contentDocument?.body)return q(e.contentDocument.body,n)}catch(t){n.log.warn("Failed to clone iframe",t)}return e.cloneNode(!1)}function ct(e){let n=e.cloneNode(!1);return e.currentSrc&&e.currentSrc!==e.src&&(n.src=e.currentSrc,n.srcset=""),n.loading==="lazy"&&(n.loading="eager"),n}async function ut(e,n){if(e.ownerDocument&&!e.currentSrc&&e.poster)return L(e.poster,e.ownerDocument);let t=e.cloneNode(!1);t.crossOrigin="anonymous",e.currentSrc&&e.currentSrc!==e.src&&(t.src=e.currentSrc);let r=t.ownerDocument;if(r){let s=!0;if(await D(t,{onError:()=>s=!1,onWarn:n.log.warn}),!s)return e.poster?L(e.poster,e.ownerDocument):t;t.currentTime=e.currentTime,await new Promise(a=>{t.addEventListener("seeked",a,{once:!0})});let i=r.createElement("canvas");i.width=e.offsetWidth,i.height=e.offsetHeight;try{let a=i.getContext("2d");a&&a.drawImage(t,0,0,i.width,i.height)}catch(a){return n.log.warn("Failed to clone video",a),e.poster?L(e.poster,e.ownerDocument):t}return fe(i,n)}return t}function dt(e,n){return Be(e)?fe(e,n):ze(e)?lt(e,n):N(e)?ct(e):k(e)?ut(e,n):e.cloneNode(!1)}function ht(e){let n=e.sandbox;if(!n){let{ownerDocument:t}=e;try{t&&(n=t.createElement("iframe"),n.id=`__SANDBOX__-${de()}`,n.width="0",n.height="0",n.style.visibility="hidden",n.style.position="fixed",t.body.appendChild(n),n.contentWindow?.document.write('<!DOCTYPE html><meta charset="UTF-8"><title></title><body>'),e.sandbox=n)}catch(r){e.log.warn("Failed to getSandBox",r)}}return n}var gt=["width","height","-webkit-text-fill-color"],ft=["stroke","fill"];function me(e,n,t){let{defaultComputedStyles:r}=t,s=e.nodeName.toLowerCase(),i=M(e)&&s!=="svg",a=i?ft.map(m=>[m,e.getAttribute(m)]).filter(([,m])=>m!==null):[],o=[i&&"svg",s,a.map((m,p)=>`${m}=${p}`).join(","),n].filter(Boolean).join(":");if(r.has(o))return r.get(o);let d=ht(t)?.contentWindow;if(!d)return new Map;let l=d?.document,u,h;i?(u=l.createElementNS(U,"svg"),h=u.ownerDocument.createElementNS(u.namespaceURI,s),a.forEach(([m,p])=>{h.setAttributeNS(null,m,p)}),u.appendChild(h)):u=h=l.createElement(s),h.textContent=" ",l.body.appendChild(u);let f=d.getComputedStyle(h,n),g=new Map;for(let m=f.length,p=0;p<m;p++){let w=f.item(p);gt.includes(w)||g.set(w,f.getPropertyValue(w))}return l.body.removeChild(u),r.set(o,g),g}function pe(e,n,t){let r=new Map,s=[],i=new Map;if(t)for(let o of t)a(o);else for(let o=e.length,c=0;c<o;c++){let d=e.item(c);a(d)}for(let o=s.length,c=0;c<o;c++)i.get(s[c])?.forEach((d,l)=>r.set(l,d));function a(o){let c=e.getPropertyValue(o),d=e.getPropertyPriority(o),l=o.lastIndexOf("-"),u=l>-1?o.substring(0,l):void 0;if(u){let h=i.get(u);h||(h=new Map,i.set(u,h)),h.set(o,[c,d])}n.get(o)===c&&!d||(u?s.push(u):r.set(o,[c,d]))}return r}function mt(e,n,t,r){let{ownerWindow:s,includeStyleProperties:i,currentParentNodeStyle:a}=r,o=n.style,c=s.getComputedStyle(e),d=me(e,null,r);a?.forEach((u,h)=>{d.delete(h)});let l=pe(c,d,i);l.delete("transition-property"),l.delete("all"),l.delete("d"),l.delete("content"),t&&(l.delete("margin-top"),l.delete("margin-right"),l.delete("margin-bottom"),l.delete("margin-left"),l.delete("margin-block-start"),l.delete("margin-block-end"),l.delete("margin-inline-start"),l.delete("margin-inline-end"),l.set("box-sizing",["border-box",""])),l.get("background-clip")?.[0]==="text"&&n.classList.add("______background-clip--text"),ae&&(l.has("font-kerning")||l.set("font-kerning",["normal",""]),(l.get("overflow-x")?.[0]==="hidden"||l.get("overflow-y")?.[0]==="hidden")&&l.get("text-overflow")?.[0]==="ellipsis"&&e.scrollWidth===e.clientWidth&&l.set("text-overflow",["clip",""]));for(let u=o.length,h=0;h<u;h++)o.removeProperty(o.item(h));return l.forEach(([u,h],f)=>{o.setProperty(f,u,h)}),l}function pt(e,n){(Ue(e)||$e(e)||je(e))&&n.setAttribute("value",e.value)}var wt=[":before",":after"],bt=[":-webkit-scrollbar",":-webkit-scrollbar-button",":-webkit-scrollbar-thumb",":-webkit-scrollbar-track",":-webkit-scrollbar-track-piece",":-webkit-scrollbar-corner",":-webkit-resizer"];function Et(e,n,t,r,s){let{ownerWindow:i,svgStyleElement:a,svgStyles:o,currentNodeStyle:c}=r;if(!a||!i)return;function d(l){let u=i.getComputedStyle(e,l),h=u.getPropertyValue("content");if(!h||h==="none")return;s?.(h),h=h.replace(/(')|(")|(counter\(.+\))/g,"");let f=[de()],g=me(e,l,r);c?.forEach((b,y)=>{g.delete(y)});let m=pe(u,g,r.includeStyleProperties);m.delete("content"),m.delete("-webkit-locale"),m.get("background-clip")?.[0]==="text"&&n.classList.add("______background-clip--text");let p=[`content: '${h}';`];if(m.forEach(([b,y],C)=>{p.push(`${C}: ${b}${y?" !important":""};`)}),p.length===1)return;try{n.className=[n.className,...f].join(" ")}catch(b){r.log.warn("Failed to copyPseudoClass",b);return}let w=p.join(`
  `),E=o.get(w);E||(E=[],o.set(w,E)),E.push(`.${f[0]}:${l}`)}wt.forEach(d),t&&bt.forEach(d)}var K=new Set(["symbol"]);async function Q(e,n,t,r,s){if(v(t)&&(We(t)||Ge(t))||r.filter&&!r.filter(t))return;K.has(n.nodeName)||K.has(t.nodeName)?r.currentParentNodeStyle=void 0:r.currentParentNodeStyle=r.currentNodeStyle;let i=await q(t,r,!1,s);r.isEnable("restoreScrollPosition")&&yt(e,i),n.appendChild(i)}async function Z(e,n,t,r){let s=(v(e)?e.shadowRoot?.firstChild:void 0)??e.firstChild;for(let i=s;i;i=i.nextSibling)if(!Oe(i))if(v(i)&&Ve(i)&&typeof i.assignedNodes=="function"){let a=i.assignedNodes();for(let o=0;o<a.length;o++)await Q(e,n,a[o],t,r)}else await Q(e,n,i,t,r)}function yt(e,n){if(!x(e)||!x(n))return;let{scrollTop:t,scrollLeft:r}=e;if(!t&&!r)return;let{transform:s}=n.style,i=new DOMMatrix(s),{a,b:o,c,d}=i;i.a=1,i.b=0,i.c=0,i.d=1,i.translateSelf(-r,-t),i.a=a,i.b=o,i.c=c,i.d=d,n.style.transform=i.toString()}function vt(e,n){let{backgroundColor:t,width:r,height:s,style:i}=n,a=e.style;if(t&&a.setProperty("background-color",t,"important"),r&&a.setProperty("width",`${r}px`,"important"),s&&a.setProperty("height",`${s}px`,"important"),i)for(let o in i)a[o]=i[o]}var St=/^[\w-:]+$/;async function q(e,n,t=!1,r){let{ownerDocument:s,ownerWindow:i,fontFamilies:a}=n;if(s&&ke(e))return r&&/\S/.test(e.data)&&r(e.data),s.createTextNode(e.data);if(s&&i&&v(e)&&(x(e)||M(e))){let c=await dt(e,n);if(n.isEnable("removeAbnormalAttributes")){let g=c.getAttributeNames();for(let m=g.length,p=0;p<m;p++){let w=g[p];St.test(w)||c.removeAttribute(w)}}let d=n.currentNodeStyle=mt(e,c,t,n);t&&vt(c,n);let l=!1;if(n.isEnable("copyScrollbar")){let g=[d.get("overflow-x")?.[0],d.get("overflow-y")?.[0]];l=g.includes("scroll")||(g.includes("auto")||g.includes("overlay"))&&(e.scrollHeight>e.clientHeight||e.scrollWidth>e.clientWidth)}let u=d.get("text-transform")?.[0],h=he(d.get("font-family")?.[0]),f=h?g=>{u==="uppercase"?g=g.toUpperCase():u==="lowercase"?g=g.toLowerCase():u==="capitalize"&&(g=g[0].toUpperCase()+g.substring(1)),h.forEach(m=>{let p=a.get(m);p||a.set(m,p=new Set),g.split("").forEach(w=>p.add(w))})}:void 0;return Et(e,c,l,n,f),pt(e,c),k(e)||await Z(e,c,n,f),c}let o=e.cloneNode(!1);return await Z(e,o,n),o}function Ct(e){if(e.ownerDocument=void 0,e.ownerWindow=void 0,e.svgStyleElement=void 0,e.svgDefsElement=void 0,e.svgStyles.clear(),e.defaultComputedStyles.clear(),e.sandbox){try{e.sandbox.remove()}catch(n){e.log.warn("Failed to destroyContext",n)}e.sandbox=void 0}e.workers=[],e.fontFamilies.clear(),e.fontCssTexts.clear(),e.requests.clear(),e.tasks=[]}function Tt(e){let{url:n,timeout:t,responseType:r,...s}=e,i=new AbortController,a=t?setTimeout(()=>i.abort(),t):void 0;return fetch(n,{signal:i.signal,...s}).then(o=>{if(!o.ok)throw new Error("Failed fetch, not 2xx response",{cause:o});switch(r){case"arrayBuffer":return o.arrayBuffer();case"dataUrl":return o.blob().then(Ze);case"text":default:return o.text()}}).finally(()=>clearTimeout(a))}function _(e,n){let{url:t,requestType:r="text",responseType:s="text",imageDom:i}=n,a=t,{timeout:o,acceptOfImage:c,requests:d,fetchFn:l,fetch:{requestInit:u,bypassingCache:h,placeholderImage:f},font:g,workers:m,fontFamilies:p}=e;r==="image"&&(O||V)&&e.drawImageCount++;let w=d.get(t);if(!w){h&&h instanceof RegExp&&h.test(a)&&(a+=(/\?/.test(a)?"&":"?")+new Date().getTime());let E=r.startsWith("font")&&g&&g.minify,b=new Set;E&&r.split(";")[1].split(",").forEach(P=>{p.has(P)&&p.get(P).forEach(Y=>b.add(Y))});let y=E&&b.size,C={url:a,timeout:o,responseType:y?"arrayBuffer":s,headers:r==="image"?{accept:c}:void 0,...u};w={type:r,resolve:void 0,reject:void 0,response:null},w.response=(async()=>{if(l&&r==="image"){let S=await l(t);if(S)return S}return!O&&t.startsWith("http")&&m.length?new Promise((S,P)=>{m[d.size&m.length-1].postMessage({rawUrl:t,...C}),w.resolve=S,w.reject=P}):Tt(C)})().catch(S=>{if(d.delete(t),r==="image"&&f)return e.log.warn("Failed to fetch image base64, trying to use placeholder image",a),typeof f=="string"?f:f(i);throw S}),d.set(t,w)}return w.response}async function we(e,n,t,r){if(!be(e))return e;for(let[s,i]of At(e,n))try{let a=await _(t,{url:i,requestType:r?"image":"text",responseType:"dataUrl"});e=e.replace(Lt(s),`$1${a}$3`)}catch(a){t.log.warn("Failed to fetch css data url",s,a)}return e}function be(e){return/url\((['"]?)([^'"]+?)\1\)/.test(e)}var Ee=/url\((['"]?)([^'"]+?)\1\)/g;function At(e,n){let t=[];return e.replace(Ee,(r,s,i)=>(t.push([i,ce(i,n)]),r)),t.filter(([r])=>!G(r))}function Lt(e){let n=e.replace(/([.*+?^${}()|\[\]\/\\])/g,"\\$1");return new RegExp(`(url\\(['"]?)(${n})(['"]?\\))`,"g")}var Nt=["background-image","border-image-source","-webkit-border-image","-webkit-mask-image","list-style-image"];function It(e,n){return Nt.map(t=>{let r=e.getPropertyValue(t);return!r||r==="none"?null:((O||V)&&n.drawImageCount++,we(r,null,n,!0).then(s=>{!s||r===s||e.setProperty(t,s,e.getPropertyPriority(t))}))}).filter(Boolean)}function xt(e,n){if(N(e)){let t=e.currentSrc||e.src;if(!G(t))return[_(n,{url:t,imageDom:e,requestType:"image",responseType:"dataUrl"}).then(r=>{r&&(e.srcset="",e.dataset.originalSrc=t,e.src=r||"")})];(O||V)&&n.drawImageCount++}else if(M(e)&&!G(e.href.baseVal)){let t=e.href.baseVal;return[_(n,{url:t,imageDom:e,requestType:"image",responseType:"dataUrl"}).then(r=>{r&&(e.dataset.originalSrc=t,e.href.baseVal=r||"")})]}return[]}function Dt(e,n){let{ownerDocument:t,svgDefsElement:r}=n,s=e.getAttribute("href")??e.getAttribute("xlink:href");if(!s)return[];let[i,a]=s.split("#");if(a){let o=`#${a}`,c=t?.querySelector(`svg ${o}`);if(i&&e.setAttribute("href",o),r?.querySelector(o))return[];if(c)return r?.appendChild(c.cloneNode(!0)),[];if(i)return[_(n,{url:i,responseType:"text"}).then(d=>{r?.insertAdjacentHTML("beforeend",d)})]}return[]}function ye(e,n){let{tasks:t}=n;v(e)&&((N(e)||le(e))&&t.push(...xt(e,n)),Fe(e)&&t.push(...Dt(e,n))),x(e)&&t.push(...It(e.style,n)),e.childNodes.forEach(r=>{ye(r,n)})}async function _t(e,n){let{ownerDocument:t,svgStyleElement:r,fontFamilies:s,fontCssTexts:i,tasks:a,font:o}=n;if(!(!t||!r||!s.size))if(o&&o.cssText){let c=te(o.cssText,n);r.appendChild(t.createTextNode(`${c}
`))}else{let c=Array.from(t.styleSheets).filter(l=>{try{return"cssRules"in l&&!!l.cssRules.length}catch(u){return n.log.warn(`Error while reading CSS rules from ${l.href}`,u),!1}});await Promise.all(c.flatMap(l=>Array.from(l.cssRules).map(async(u,h)=>{if(Pe(u)){let f=h+1,g=u.href,m="";try{m=await _(n,{url:g,requestType:"text",responseType:"text"})}catch(w){n.log.warn(`Error fetch remote css import from ${g}`,w)}let p=m.replace(Ee,(w,E,b)=>w.replace(b,ce(b,g)));for(let w of Ht(p))try{l.insertRule(w,w.startsWith("@import")?f+=1:l.cssRules.length)}catch(E){n.log.warn("Error inserting rule from remote css import",{rule:w,error:E})}}}))),c.flatMap(l=>Array.from(l.cssRules)).filter(l=>Re(l)&&be(l.style.getPropertyValue("src"))&&he(l.style.getPropertyValue("font-family"))?.some(u=>s.has(u))).forEach(l=>{let u=l,h=i.get(u.cssText);h?r.appendChild(t.createTextNode(`${h}
`)):a.push(we(u.cssText,u.parentStyleSheet?u.parentStyleSheet.href:null,n).then(f=>{f=te(f,n),i.set(u.cssText,f),r.appendChild(t.createTextNode(`${f}
`))}))})}}var Mt=/(\/\*[\s\S]*?\*\/)/g,ee=/((@.*?keyframes [\s\S]*?){([\s\S]*?}\s*?)})/gi;function Ht(e){if(e==null)return[];let n=[],t=e.replace(Mt,"");for(;;){let i=ee.exec(t);if(!i)break;n.push(i[0])}t=t.replace(ee,"");let r=/@import[\s\S]*?url\([^)]*\)[\s\S]*?;/gi,s=new RegExp("((\\s*?(?:\\/\\*[\\s\\S]*?\\*\\/)?\\s*?@media[\\s\\S]*?){([\\s\\S]*?)}\\s*?})|(([\\s\\S]*?){([\\s\\S]*?)})","gi");for(;;){let i=r.exec(t);if(i)s.lastIndex=r.lastIndex;else if(i=s.exec(t),i)r.lastIndex=s.lastIndex;else break;n.push(i[0])}return n}var Rt=/url\([^)]+\)\s*format\((["']?)([^"']+)\1\)/g,Pt=/src:\s*(?:url\([^)]+\)\s*format\([^)]+\)[,;]\s*)+/g;function te(e,n){let{font:t}=n,r=t?t?.preferredFormat:void 0;return r?e.replace(Pt,s=>{for(;;){let[i,,a]=Rt.exec(s)||[];if(!a)return"";if(a===r)return`src: ${i};`}}):e}async function Ft(e,n){let t=await z(e,n);if(v(t.node)&&M(t.node))return t.node;let{ownerDocument:r,log:s,tasks:i,svgStyleElement:a,svgDefsElement:o,svgStyles:c,font:d,progress:l,autoDestruct:u,onCloneNode:h,onEmbedNode:f,onCreateForeignObjectSvg:g}=t;s.time("clone node");let m=await q(t.node,t,!0);if(a&&r){let y="";c.forEach((C,S)=>{y+=`${C.join(`,
`)} {
  ${S}
}
`}),a.appendChild(r.createTextNode(y))}s.timeEnd("clone node"),await h?.(m),d!==!1&&v(m)&&(s.time("embed web font"),await _t(m,t),s.timeEnd("embed web font")),s.time("embed node"),ye(m,t);let p=i.length,w=0,E=async()=>{for(;;){let y=i.pop();if(!y)break;try{await y}catch(C){t.log.warn("Failed to run task",C)}l?.(++w,p)}};l?.(w,p),await Promise.all([...Array.from({length:4})].map(E)),s.timeEnd("embed node"),await f?.(m);let b=Ot(m,t);return o&&b.insertBefore(o,b.children[0]),a&&b.insertBefore(a,b.children[0]),u&&Ct(t),await g?.(b),b}function Ot(e,n){let{width:t,height:r}=n,s=Ye(t,r,e.ownerDocument),i=s.ownerDocument.createElementNS(s.namespaceURI,"foreignObject");return i.setAttributeNS(null,"x","0%"),i.setAttributeNS(null,"y","0%"),i.setAttributeNS(null,"width","100%"),i.setAttributeNS(null,"height","100%"),i.append(e),s.appendChild(i),s}async function kt(e,n){let t=await z(e,n),r=await Ft(t),s=Je(r,t.isEnable("removeControlCharacter"));t.autoDestruct||(t.svgStyleElement=ge(t.ownerDocument),t.svgDefsElement=t.ownerDocument?.createElementNS(U,"defs"),t.svgStyles.clear());let i=L(s,r.ownerDocument);return await st(i,t)}async function ve(e,n){let t=await z(e,n),{log:r,type:s,quality:i,dpi:a}=t,o=await kt(t);r.time("canvas to blob");let c=await Ke(o,s,i);if(["image/png","image/jpeg"].includes(s)&&a){let d=await et(c.slice(0,33)),l=new Uint8Array(d);return s==="image/png"?l=De(l,a):s==="image/jpeg"&&(l=Le(l,a)),r.timeEnd("canvas to blob"),new Blob([l,c.slice(33)],{type:s})}return r.timeEnd("canvas to blob"),c}var H={METADATA:"data-replit-metadata",COMPONENT_NAME:"data-component-name"};function Se(e){if(e.startsWith("http://localhost:"))return!0;try{return new URL(e).hostname.endsWith(T.ALLOWED_DOMAIN)}catch{return!1}}function Ce(e){return e?document.elementFromPoint(e.clientX,e.clientY):null}function X(e){return!!(e.getAttribute(H.METADATA)&&e.getAttribute(H.COMPONENT_NAME))}function $(e){let n={elementPath:e.getAttribute(H.METADATA)??"",elementName:e.getAttribute(H.COMPONENT_NAME)??"",textContent:e.textContent??"",tag:e.tagName.toLowerCase()};return n.textContent.length>100&&(n.textContent=n.textContent.slice(0,100)+"..."),n}async function Te(e){try{let t=window.getComputedStyle(e).backgroundColor;return Bt(t)&&(t=window.getComputedStyle(document.documentElement).backgroundColor),await ve(e,{type:"image/png",backgroundColor:t,fetch:{requestInit:{mode:"no-cors"}}})}catch(n){console.error("[replit-cartographer] Failed to take screenshot:",n);return}}function Bt(e){return e==="transparent"||e==="rgba(0, 0, 0, 0)"||e.endsWith(", 0)")||e.endsWith(",0)")}var R=class{selectedElement=null;isActive=!1;lastHighlightedElement=null;hoverHighlighter=null;hoverLabel=null;selectedHighlighter=null;selectedLabel=null;constructor(){this.setupMessageListener(),this.notifyScriptLoaded()}initializeHighlighter(){this.hoverHighlighter=document.createElement("div"),this.hoverLabel=document.createElement("div"),Object.assign(this.hoverHighlighter.style,{...I.highlighter,zIndex:Number.MAX_SAFE_INTEGER,pointerEvents:"all",position:"fixed"}),Object.assign(this.hoverLabel.style,{...I.label,zIndex:Number.MAX_SAFE_INTEGER,pointerEvents:"all",position:"fixed"}),this.selectedHighlighter=document.createElement("div"),this.selectedLabel=document.createElement("div"),Object.assign(this.selectedHighlighter.style,{...I.highlighter,pointerEvents:"none",position:"fixed"}),Object.assign(this.selectedLabel.style,{...I.label,pointerEvents:"none",position:"fixed"}),document.body.appendChild(this.selectedHighlighter),document.body.appendChild(this.selectedLabel),document.body.appendChild(this.hoverHighlighter),document.body.appendChild(this.hoverLabel)}setupMessageListener(){window.addEventListener("message",this.handleMessage.bind(this))}notifyScriptLoaded(){this.postMessageToParent({type:"SELECTOR_SCRIPT_LOADED",timestamp:Date.now(),version:F})}postMessageToParent(n){window.parent&&window.parent.postMessage(n,"*")}handleMouseMove=n=>{if(this.isActive&&this.hoverHighlighter){this.hoverHighlighter.style.pointerEvents="none";let t=Ce(n);if(!t||t===this.hoverHighlighter||t===this.selectedHighlighter||!X(t)){this.hideHighlight(this.hoverHighlighter,this.hoverLabel),this.lastHighlightedElement=null;return}this.lastHighlightedElement=t,this.hoverHighlighter.style.pointerEvents="all",this.hoverHighlighter.style.border=`2px dashed ${T.HIGHLIGHT_COLOR}`,this.updateHighlighterPosition(t,this.hoverHighlighter,this.hoverLabel)}};handleMouseLeave=()=>{this.isActive&&this.hideHighlight(this.hoverHighlighter,this.hoverLabel)};calculateLabelPosition(n,t){return t<24?{top:`${t+window.scrollY}px`,left:`${n.left+window.scrollX}px`,transform:"none",marginTop:"2px"}:{top:`${t+window.scrollY}px`,left:`${n.left+window.scrollX}px`,transform:"translateY(-100%)",marginTop:"-4px"}}updateHighlighterPosition(n,t,r){if(!t||!r)return;let s=n.getBoundingClientRect(),i=window.innerHeight,a=Math.max(0,s.top),o=Math.min(i,s.bottom),c=Math.max(0,o-a);Object.assign(t.style,{opacity:c>0?"1":"0",top:`${a}px`,left:`${s.left}px`,width:`${s.width}px`,height:`${c}px`});let d=$(n);r.textContent=d.elementName;let l=this.calculateLabelPosition(s,a);l.top=`${a}px`,l.left=`${s.left}px`,Object.assign(r.style,{...l,opacity:c>0?"1":"0"})}hideHighlight(n,t){n&&(n.style.opacity="0"),t&&(t.style.opacity="0")}handleClick=async n=>{if(!this.isActive)return;n.preventDefault(),n.stopPropagation();let t=this.lastHighlightedElement;if(!t||!X(t))return;if(t===this.selectedElement){this.unselectCurrentElement(),this.hideHighlight(this.selectedHighlighter,this.selectedLabel),this.postMessageToParent({type:"ELEMENT_UNSELECTED",timestamp:Date.now()});return}this.selectedElement=t,this.selectedHighlighter&&this.selectedLabel&&(this.selectedHighlighter.style.border=`2px solid ${T.HIGHLIGHT_COLOR}`,this.selectedHighlighter.style.opacity="1",this.selectedLabel.style.opacity="1",this.selectedLabel.textContent=$(t).elementName),this.updateHighlighterPosition(t,this.selectedHighlighter,this.selectedLabel);let r=$(t),s=await Te(t);this.postMessageToParent({type:"ELEMENT_SELECTED",payload:{...r,screenshotBlob:s},timestamp:Date.now()})};unselectCurrentElement(){this.selectedElement&&(this.selectedElement=null)}handleMessage=n=>{if(!Se(n.origin))return;let t=n.data;if(!(!t||typeof t!="object"))switch(t.type){case"TOGGLE_REPLIT_VISUAL_EDITOR":{this.handleVisualEditorToggle(t.enabled);break}case"CLEAR_SELECTION":{this.unselectCurrentElement(),this.hideHighlight(this.selectedHighlighter,this.selectedLabel);break}}};handleVisualEditorToggle(n){let t=!!n;t?this.postMessageToParent({type:"REPLIT_VISUAL_EDITOR_ENABLED",timestamp:Date.now()}):this.postMessageToParent({type:"REPLIT_VISUAL_EDITOR_DISABLED",timestamp:Date.now()}),this.isActive!==t&&(this.isActive=t,this.toggleEventListeners(t))}recalculateSelectedElement=()=>{this.isActive&&(this.selectedElement&&this.updateHighlighterPosition(this.selectedElement,this.selectedHighlighter,this.selectedLabel),this.lastHighlightedElement&&this.updateHighlighterPosition(this.lastHighlightedElement,this.hoverHighlighter,this.hoverLabel))};toggleEventListeners(n){n?(this.initializeHighlighter(),document.addEventListener("mousemove",this.handleMouseMove),document.addEventListener("mouseleave",this.handleMouseLeave),document.addEventListener("click",this.handleClick,!0),window.addEventListener("resize",this.recalculateSelectedElement),window.addEventListener("scroll",this.recalculateSelectedElement,!0),document.body.style.cursor="pointer"):(document.removeEventListener("mousemove",this.handleMouseMove),document.removeEventListener("click",this.handleClick,!0),document.removeEventListener("mouseleave",this.handleMouseLeave),window.removeEventListener("resize",this.recalculateSelectedElement),window.removeEventListener("scroll",this.recalculateSelectedElement,!0),this.hoverHighlighter?.remove(),this.hoverLabel?.remove(),this.selectedHighlighter?.remove(),this.selectedLabel?.remove(),this.hoverHighlighter=null,this.hoverLabel=null,this.selectedHighlighter=null,this.selectedLabel=null,document.body.style.cursor="",this.selectedElement=null)}};if(typeof window<"u")try{window.REPLIT_BEACON_VERSION||(window.REPLIT_BEACON_VERSION=F,new R)}catch(e){console.error("[replit-beacon] Failed to initialize:",e)}})();
</script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx?v=sMBl92sX47FPViJvQCupB"></script>
  </body>
</html>
