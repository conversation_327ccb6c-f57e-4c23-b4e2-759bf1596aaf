"use client"

import { useState } from "react"
import { Check } from "lucide-react"

import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Slider } from "@/components/ui/slider"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Checkbox } from "@/components/ui/checkbox"

interface ProductFiltersProps {
  className?: string
}

export function ProductFilters({ className }: ProductFiltersProps) {
  const [priceRange, setPriceRange] = useState([0, 500000])
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedBrands, setSelectedBrands] = useState<string[]>([])

  const categories = [
    { id: "beauty", label: "Beauty & Health", count: 120 },
    { id: "oral", label: "Oral Hygiene", count: 45 },
    { id: "bedding", label: "Beddings", count: 78 },
    { id: "footwear", label: "Footwear", count: 96 },
    { id: "perfumes", label: "Perfumes", count: 64 },
    { id: "clothing", label: "Clothing", count: 150 },
  ]

  const brands = [
    { id: "dove", label: "Dove", count: 24 },
    { id: "cantu", label: "Cantu", count: 18 },
    { id: "suave", label: "Suave", count: 12 },
    { id: "aveeno", label: "Aveeno", count: 15 },
    { id: "neutrogena", label: "Neutrogena", count: 20 },
    { id: "olay", label: "Olay", count: 16 },
    { id: "colgate", label: "Colgate", count: 10 },
    { id: "oral-b", label: "Oral-B", count: 8 },
  ]

  const toggleCategory = (categoryId: string) => {
    setSelectedCategories((prev) =>
      prev.includes(categoryId) ? prev.filter((id) => id !== categoryId) : [...prev, categoryId],
    )
  }

  const toggleBrand = (brandId: string) => {
    setSelectedBrands((prev) => (prev.includes(brandId) ? prev.filter((id) => id !== brandId) : [...prev, brandId]))
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-US").format(price)
  }

  return (
    <div className={cn("space-y-6", className)}>
      <div>
        <div className="flex items-center justify-between">
          <h3 className="font-medium">Price Range</h3>
          <span className="text-xs text-muted-foreground">
            UGX {formatPrice(priceRange[0])} - UGX {formatPrice(priceRange[1])}
          </span>
        </div>
        <Slider
          defaultValue={[0, 500000]}
          max={500000}
          step={5000}
          value={priceRange}
          onValueChange={setPriceRange}
          className="mt-4"
        />
      </div>

      <Separator />

      <Accordion type="multiple" defaultValue={["categories", "brands"]} className="w-full">
        <AccordionItem value="categories" className="border-none">
          <AccordionTrigger className="py-2 hover:no-underline">
            <h3 className="font-medium">Categories</h3>
          </AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2 mt-2">
              {categories.map((category) => (
                <div key={category.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`category-${category.id}`}
                      checked={selectedCategories.includes(category.id)}
                      onCheckedChange={() => toggleCategory(category.id)}
                    />
                    <label htmlFor={`category-${category.id}`} className="text-sm cursor-pointer flex items-center">
                      {category.label}
                    </label>
                  </div>
                  <Badge variant="outline" className="font-normal text-xs">
                    {category.count}
                  </Badge>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="brands" className="border-none">
          <AccordionTrigger className="py-2 hover:no-underline">
            <h3 className="font-medium">Brands</h3>
          </AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2 mt-2">
              {brands.map((brand) => (
                <div key={brand.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`brand-${brand.id}`}
                      checked={selectedBrands.includes(brand.id)}
                      onCheckedChange={() => toggleBrand(brand.id)}
                    />
                    <label htmlFor={`brand-${brand.id}`} className="text-sm cursor-pointer flex items-center">
                      {brand.label}
                    </label>
                  </div>
                  <Badge variant="outline" className="font-normal text-xs">
                    {brand.count}
                  </Badge>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="ratings" className="border-none">
          <AccordionTrigger className="py-2 hover:no-underline">
            <h3 className="font-medium">Ratings</h3>
          </AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2 mt-2">
              {[5, 4, 3, 2, 1].map((rating) => (
                <div key={rating} className="flex items-center space-x-2">
                  <Checkbox id={`rating-${rating}`} />
                  <label htmlFor={`rating-${rating}`} className="text-sm cursor-pointer flex items-center">
                    <div className="flex items-center">
                      {Array(5)
                        .fill(0)
                        .map((_, i) => (
                          <svg
                            key={i}
                            className={`h-4 w-4 ${i < rating ? "text-primary fill-primary" : "text-muted-foreground"}`}
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
                          </svg>
                        ))}
                      <span className="ml-1">& Up</span>
                    </div>
                  </label>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="discount" className="border-none">
          <AccordionTrigger className="py-2 hover:no-underline">
            <h3 className="font-medium">Discount</h3>
          </AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2 mt-2">
              {[10, 20, 30, 40, 50].map((discount) => (
                <div key={discount} className="flex items-center space-x-2">
                  <Checkbox id={`discount-${discount}`} />
                  <label htmlFor={`discount-${discount}`} className="text-sm cursor-pointer flex items-center">
                    {discount}% or more
                  </label>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <Separator />

      <div className="space-y-4">
        <h3 className="font-medium">Active Filters</h3>
        <div className="flex flex-wrap gap-2">
          {selectedCategories.map((categoryId) => {
            const category = categories.find((c) => c.id === categoryId)
            return (
              <Badge key={categoryId} variant="secondary" className="flex items-center gap-1">
                {category?.label}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-4 w-4 p-0 ml-1"
                  onClick={() => toggleCategory(categoryId)}
                >
                  <Check className="h-3 w-3" />
                  <span className="sr-only">Remove</span>
                </Button>
              </Badge>
            )
          })}
          {selectedBrands.map((brandId) => {
            const brand = brands.find((b) => b.id === brandId)
            return (
              <Badge key={brandId} variant="secondary" className="flex items-center gap-1">
                {brand?.label}
                <Button variant="ghost" size="icon" className="h-4 w-4 p-0 ml-1" onClick={() => toggleBrand(brandId)}>
                  <Check className="h-3 w-3" />
                  <span className="sr-only">Remove</span>
                </Button>
              </Badge>
            )
          })}
          {(selectedCategories.length > 0 || selectedBrands.length > 0) && (
            <Button variant="link" size="sm" className="h-7 px-2 text-xs">
              Clear All
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

