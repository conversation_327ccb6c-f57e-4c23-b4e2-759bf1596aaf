import csv
from pathlib import Path

# Define your CSV file paths
csv_files = [
    'station Xpress 01 UFT8.csv',
    'station Xpress 02 utf8.csv',
]

# Define a simple AI-like category mapping function (customize as needed)
def map_category(product_name, description):
    name = (product_name or '').lower()
    desc = (description or '').lower()
    text = name + ' ' + desc

    # Oral hygiene
    if any(word in text for word in ['tooth', 'oral', 'paste', 'brush', 'mouthwash', 'floss', 'dental', 'teeth', 'gum care']):
        return 'oral_hygiene'
    # Bedding
    if any(word in text for word in ['towel', 'bedding', 'sheet', 'pillow', 'duvet', 'blanket', 'comforter', 'linen', 'bedspread']):
        return 'bedding'
    # Footwear
    if any(word in text for word in ['shoes', 'sneaker', 'boot', 'foot', 'slipper', 'loafer', 'sandals', 'flip flop', 'trainer', 'mens shoes', 'womens shoes']):
        return 'footwear'
    # Perfumes
    if any(word in text for word in ['perfume', 'fragrance', 'eau de toilette', 'eau de parfum', 'cologne', 'body spray']):
        return 'perfumes'
    # Gadgets & Electronics
    if any(word in text for word in ['gadget', 'power bank', 'charger', 'magnetic', 'wireless', 'usb', 'adapter', 'cable', 'battery', 'headphone', 'earbud', 'speaker', 'tech', 'electronic', 'bluetooth', 'smart']):
        return 'gadgets'
    # Clothing
    if any(word in text for word in ['t-shirt', 'shirt', 'top', 'boxer', 'brief', 'clothing', 'pant', 'jean', 'sock', 'jacket', 'coat', 'short', 'skirt', 'dress', 'sweater', 'hoodie', 'uniform']):
        return 'clothing'
    # Accessories
    if any(word in text for word in ['accessory', 'cap', 'hat', 'bag', 'visor', 'belt', 'wallet', 'sunglass', 'watch', 'scarf', 'glove', 'keychain', 'bracelet', 'necklace', 'ring', 'earring', 'purse', 'backpack', 'luggage', 'accessories']):
        return 'accessories'
    # Beauty & Health
    if any(word in text for word in ['beauty', 'health', 'supplement', 'cream', 'moisturizer', 'lotion', 'serum', 'skincare', 'shampoo', 'conditioner', 'soap', 'deodorant', 'personal care', 'hair', 'face', 'body wash', 'cosmetic', 'makeup']):
        return 'beauty_health'
    # Snacks & Food
    if any(word in text for word in ['chocolate', 'candy', 'sweet', 'snack', 'biscuit', 'cookie', 'cracker', 'chips', 'gum', 'mint', 'energy bar', 'granola', 'confectionery']):
        return 'snacks'
    # Drinks
    if any(word in text for word in ['water', 'juice', 'drink', 'beverage', 'soda', 'cola', 'energy drink', 'coffee', 'tea', 'bottle', 'can', 'refreshment']):
        return 'drinks'
    # Stationery
    if any(word in text for word in ['pen', 'pencil', 'notebook', 'stationery', 'eraser', 'sharpener', 'marker', 'highlighter', 'folder', 'file', 'paper', 'envelope', 'diary', 'journal']):
        return 'stationery'
    # Travel
    if any(word in text for word in ['travel', 'passport', 'tag', 'luggage', 'suitcase', 'adapter', 'converter', 'travel kit', 'toiletry', 'organizer']):
        return 'travel'
    # Baby & Kids
    if any(word in text for word in ['baby', 'kids', 'child', 'children', 'infant', 'toddler', 'toy', 'game', 'puzzle', 'crib', 'stroller']):
        return 'baby_kids'
    # Home
    if any(word in text for word in ['home', 'kitchen', 'cook', 'utensil', 'appliance', 'cleaner', 'mop', 'broom', 'furniture', 'decor', 'candle', 'mat', 'rug']):
        return 'home'
    # Sports
    if any(word in text for word in ['sport', 'ball', 'fitness', 'gym', 'workout', 'yoga', 'exercise', 'dumbbell', 'athletic', 'training', 'outdoor', 'bottle', 'cycling']):
        return 'sports'
    # If product name or description is missing or unclear
    if not name.strip():
        return 'uncategorized'
    # Default fallback
    return 'other'

# Output SQL file
output_sql = Path('product_category_update_full.sql')

with output_sql.open('w', encoding='utf-8') as sqlfile:
    sqlfile.write('-- SQL script for bulk updating product categories by SKU\n')
    for csv_path in csv_files:
        with open(csv_path, encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                sku = row.get('SKU') or row.get('Sku') or row.get('sku')
                product = row.get('Product') or row.get('product') or ''
                desc = row.get('Description') or row.get('description') or ''
                # Only process rows with a valid SKU (alphanumeric, not too long, not a description) and a non-empty product name
                if sku and product:
                    # Filter: SKU should be short, alphanumeric, and not contain spaces or long text
                    if (len(sku) <= 20 and sku.replace('-', '').replace('_', '').isalnum()):
                        category = map_category(product, desc)
                        sqlfile.write(f"UPDATE products SET category_id = '{category}' WHERE sku = '{sku}';\n")

print(f"SQL update script generated: {output_sql.resolve()}")
