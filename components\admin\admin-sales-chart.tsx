"use client"

import { useEffect, useState } from "react"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from "recharts"

// Mock data for the sales chart
const generateSalesData = () => {
  const data = []
  const now = new Date()
  const currentMonth = now.getMonth()
  const daysInMonth = new Date(now.getFullYear(), currentMonth + 1, 0).getDate()

  for (let i = 1; i <= daysInMonth; i++) {
    const revenue = Math.floor(Math.random() * 5000000) + 1000000
    const orders = Math.floor(Math.random() * 100) + 20

    data.push({
      day: i,
      revenue,
      orders,
    })
  }

  return data
}

export function AdminSalesChart() {
  const [data, setData] = useState<any[]>([])
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setData(generateSalesData())
    setMounted(true)
  }, [])

  if (!mounted) return null

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "UGX",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value)
  }

  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="day" />
          <YAxis yAxisId="left" orientation="left" stroke="#ef4444" tickFormatter={(value) => formatCurrency(value)} />
          <YAxis yAxisId="right" orientation="right" stroke="#3b82f6" />
          <Tooltip
            formatter={(value, name) => {
              if (name === "revenue") {
                return [formatCurrency(value as number), "Revenue"]
              }
              return [value, "Orders"]
            }}
          />
          <Legend />
          <Line yAxisId="left" type="monotone" dataKey="revenue" stroke="#ef4444" activeDot={{ r: 8 }} name="Revenue" />
          <Line yAxisId="right" type="monotone" dataKey="orders" stroke="#3b82f6" name="Orders" />
        </LineChart>
      </ResponsiveContainer>
    </div>
  )
}

