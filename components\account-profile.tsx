"use client"

import type React from "react"

import { useState } from "react"
import { User, Mail, Phone, Calendar } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"

export function AccountProfile() {
  const [isEditing, setIsEditing] = useState(false)
  const [profileData, setProfileData] = useState({
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "+256 700 123 456",
    dateOfBirth: "1990-05-15",
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setProfileData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // In a real app, this would send the data to an API
    setIsEditing(false)
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>My Profile</CardTitle>
            <CardDescription>Manage your personal information.</CardDescription>
          </div>
          <Button variant={isEditing ? "ghost" : "default"} onClick={() => setIsEditing(!isEditing)}>
            {isEditing ? "Cancel" : "Edit Profile"}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit}>
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                {isEditing ? (
                  <Input
                    id="firstName"
                    name="firstName"
                    value={profileData.firstName}
                    onChange={handleChange}
                    required
                  />
                ) : (
                  <div className="flex items-center h-10 px-3 rounded-md border bg-muted/50">
                    <User className="h-4 w-4 text-muted-foreground mr-2" />
                    <span>{profileData.firstName}</span>
                  </div>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                {isEditing ? (
                  <Input id="lastName" name="lastName" value={profileData.lastName} onChange={handleChange} required />
                ) : (
                  <div className="flex items-center h-10 px-3 rounded-md border bg-muted/50">
                    <User className="h-4 w-4 text-muted-foreground mr-2" />
                    <span>{profileData.lastName}</span>
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              {isEditing ? (
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={profileData.email}
                  onChange={handleChange}
                  required
                />
              ) : (
                <div className="flex items-center h-10 px-3 rounded-md border bg-muted/50">
                  <Mail className="h-4 w-4 text-muted-foreground mr-2" />
                  <span>{profileData.email}</span>
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                {isEditing ? (
                  <Input id="phone" name="phone" value={profileData.phone} onChange={handleChange} />
                ) : (
                  <div className="flex items-center h-10 px-3 rounded-md border bg-muted/50">
                    <Phone className="h-4 w-4 text-muted-foreground mr-2" />
                    <span>{profileData.phone}</span>
                  </div>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="dateOfBirth">Date of Birth</Label>
                {isEditing ? (
                  <Input
                    id="dateOfBirth"
                    name="dateOfBirth"
                    type="date"
                    value={profileData.dateOfBirth}
                    onChange={handleChange}
                  />
                ) : (
                  <div className="flex items-center h-10 px-3 rounded-md border bg-muted/50">
                    <Calendar className="h-4 w-4 text-muted-foreground mr-2" />
                    <span>{profileData.dateOfBirth}</span>
                  </div>
                )}
              </div>
            </div>

            {isEditing && (
              <div className="flex justify-end gap-4 pt-4">
                <Button type="button" variant="outline" onClick={() => setIsEditing(false)}>
                  Cancel
                </Button>
                <Button type="submit">Save Changes</Button>
              </div>
            )}
          </div>
        </form>

        <Separator className="my-8" />

        <div>
          <h3 className="text-lg font-medium mb-4">Account Security</h3>
          <div className="space-y-4">
            <Button variant="outline" className="w-full sm:w-auto">
              Change Password
            </Button>
            <Button variant="outline" className="w-full sm:w-auto">
              Two-Factor Authentication
            </Button>
          </div>
        </div>

        <Separator className="my-8" />

        <div>
          <h3 className="text-lg font-medium mb-4">Delete Account</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Once you delete your account, there is no going back. Please be certain.
          </p>
          <Button variant="destructive">Delete Account</Button>
        </div>
      </CardContent>
    </Card>
  )
}

