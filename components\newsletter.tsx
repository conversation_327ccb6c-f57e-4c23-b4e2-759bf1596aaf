"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON>R<PERSON> } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Alert, AlertDescription } from "@/components/ui/alert"

export function Newsletter() {
  const [email, setEmail] = useState("")
  const [status, setStatus] = useState<"idle" | "loading" | "success" | "error">("idle")
  const [message, setMessage] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setStatus("loading")

    // Simulate API call
    setTimeout(() => {
      if (email && email.includes("@")) {
        setStatus("success")
        setMessage("Thank you for subscribing to our newsletter!")
        setEmail("")
      } else {
        setStatus("error")
        setMessage("Please enter a valid email address.")
      }
    }, 1000)
  }

  return (
    <div className="rounded-lg border bg-card p-8 md:p-12">
      <div className="grid gap-4 md:gap-8 md:grid-cols-2 items-center">
        <div>
          <h3 className="text-2xl font-bold mb-2">Subscribe to our Newsletter</h3>
          <p className="text-muted-foreground mb-4">
            Stay updated with our latest products, promotions, and exclusive offers.
          </p>
          <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-2">
            <Input
              type="email"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="flex-1"
              disabled={status === "loading"}
            />
            <Button type="submit" disabled={status === "loading"}>
              {status === "loading" ? "Subscribing..." : "Subscribe"}
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </form>
          {status === "success" && (
            <Alert variant="default" className="mt-4 bg-primary/10 text-primary border-primary/20">
              <AlertDescription>{message}</AlertDescription>
            </Alert>
          )}
          {status === "error" && (
            <Alert variant="destructive" className="mt-4">
              <AlertDescription>{message}</AlertDescription>
            </Alert>
          )}
        </div>
        <div className="hidden md:flex justify-end">
          <div className="relative w-full max-w-sm">
            <div className="absolute -top-8 -right-8 w-16 h-16 bg-primary/10 rounded-full" />
            <div className="absolute -bottom-8 -left-8 w-24 h-24 bg-primary/10 rounded-full" />
            <div className="relative bg-muted rounded-lg p-6 text-center">
              <h4 className="font-semibold mb-2">Join 10,000+ subscribers</h4>
              <p className="text-sm text-muted-foreground">
                Our subscribers enjoy exclusive deals and early access to new products.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

