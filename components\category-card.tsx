import Image from "next/image"
import Link from "next/link"

import { Card, CardContent } from "@/components/ui/card"

interface CategoryCardProps {
  title: string
  image: string
  href: string
  count?: number
}

export function CategoryCard({ title, image, href, count }: CategoryCardProps) {
  return (
    <Link href={href}>
      <Card className="overflow-hidden transition-all duration-300 hover:shadow-md">
        <CardContent className="p-4 flex flex-col items-center text-center">
          <div className="rounded-full overflow-hidden mb-4 w-20 h-20 bg-muted">
            <Image
              src={image || "/placeholder.svg"}
              alt={title}
              width={80}
              height={80}
              className="object-cover w-full h-full"
            />
          </div>
          <h3 className="font-medium text-sm">{title}</h3>
          {count !== undefined && <p className="text-xs text-muted-foreground">{count} products</p>}
        </CardContent>
      </Card>
    </Link>
  )
}

