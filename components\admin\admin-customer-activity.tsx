import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"

// Mock data for customer activity
const customerActivity = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/placeholder.svg?height=32&width=32",
    activity: "Placed an order",
    time: "10 minutes ago",
    amount: 450000,
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/placeholder.svg?height=32&width=32",
    activity: "Created an account",
    time: "25 minutes ago",
    amount: null,
  },
  {
    id: "3",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/placeholder.svg?height=32&width=32",
    activity: "Added items to cart",
    time: "45 minutes ago",
    amount: 635000,
  },
  {
    id: "4",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/placeholder.svg?height=32&width=32",
    activity: "Placed an order",
    time: "1 hour ago",
    amount: 275000,
  },
  {
    id: "5",
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/placeholder.svg?height=32&width=32",
    activity: "Subscribed to newsletter",
    time: "2 hours ago",
    amount: null,
  },
]

// Format currency
const formatCurrency = (amount: number | null) => {
  if (amount === null) return null
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "UGX",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

export function AdminCustomerActivity() {
  return (
    <div className="space-y-4">
      {customerActivity.map((activity) => (
        <div key={activity.id} className="flex items-start gap-3 border-b pb-4 last:border-b-0 last:pb-0">
          <Avatar className="h-8 w-8">
            <AvatarImage src={activity.avatar} alt={activity.name} />
            <AvatarFallback>
              {activity.name.charAt(0)}
              {activity.name.split(" ")[1]?.charAt(0)}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start">
              <div>
                <p className="font-medium text-sm">{activity.name}</p>
                <p className="text-xs text-muted-foreground">{activity.email}</p>
              </div>
              <div className="text-xs text-muted-foreground mt-1 sm:mt-0 sm:ml-4 sm:text-right">{activity.time}</div>
            </div>
            <div className="flex items-center justify-between mt-2">
              <Badge variant="outline" className="text-xs">
                {activity.activity}
              </Badge>
              {activity.amount !== null && (
                <span className="text-sm font-medium">{formatCurrency(activity.amount)}</span>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

