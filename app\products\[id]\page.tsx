import Image from "next/image"
import Link from "next/link"
import { Star, Truck, RotateCcw, Shield, Minus, Plus, Heart, ShoppingCart, Share2 } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { ProductCard } from "@/components/product-card"

// This would normally come from a database or API
const getProductData = (id: string) => {
  // Mock product data
  const products = {
    "cantu-shea-butter": {
      id: "cantu-shea-butter",
      title: "Cantu Shea Butter Tea Tree Oil Leave-In Conditioner",
      price: 140000,
      currency: "UGX",
      discount: 15,
      rating: 4.5,
      reviewCount: 124,
      stock: 45,
      sku: "CTU-SB-001",
      images: [
        "/placeholder.svg?height=600&width=600",
        "/placeholder.svg?height=600&width=600&text=Image 2",
        "/placeholder.svg?height=600&width=600&text=Image 3",
        "/placeholder.svg?height=600&width=600&text=Image 4",
      ],
      description:
        "Cantu Shea Butter Tea Tree Oil Leave-In Conditioner is a premium hair care product designed to nourish and hydrate your hair. It contains natural ingredients including shea butter and tea tree oil that help to moisturize, strengthen, and protect your hair from damage. Perfect for all hair types, especially dry and damaged hair that needs extra care and attention.",
      category: "Beauty & Health",
      brand: "Cantu",
      features: [
        "Contains natural shea butter and tea tree oil",
        "Moisturizes and strengthens hair",
        "Prevents breakage and split ends",
        "Suitable for all hair types",
        "Paraben and sulfate free",
      ],
      relatedProducts: ["suave-essentials", "aveeno-pure", "herbal-essentials", "neutrogena-revitalizing"],
    },
    "suave-essentials": {
      id: "suave-essentials",
      title: "Suave Essentials Gentle Body Wash Ocean Breeze",
      price: 145000,
      currency: "UGX",
      discount: 20,
      rating: 4.2,
      reviewCount: 98,
      stock: 32,
      sku: "SUV-EB-002",
      images: [
        "/placeholder.svg?height=600&width=600",
        "/placeholder.svg?height=600&width=600&text=Image 2",
        "/placeholder.svg?height=600&width=600&text=Image 3",
      ],
      description:
        "Suave Essentials Gentle Body Wash in Ocean Breeze scent provides a refreshing cleansing experience. This gentle formula is designed to cleanse without drying, leaving your skin feeling soft and smooth. The invigorating ocean breeze scent will transport you to a day at the beach.",
      category: "Beauty & Health",
      brand: "Suave",
      features: [
        "Gentle cleansing formula",
        "Refreshing ocean breeze scent",
        "Moisturizes while cleansing",
        "Dermatologist tested",
        "Suitable for daily use",
      ],
      relatedProducts: ["cantu-shea-butter", "aveeno-pure", "dove-body-wash", "wild-cherry"],
    },
    // Add more products as needed
  }

  // Return the product or a default if not found
  return (
    products[id as keyof typeof products] || {
      id: id,
      title: "Product Not Found",
      price: 0,
      currency: "UGX",
      discount: 0,
      rating: 0,
      reviewCount: 0,
      stock: 0,
      sku: "N/A",
      images: ["/placeholder.svg?height=600&width=600&text=Not Found"],
      description: "This product could not be found.",
      category: "Unknown",
      brand: "Unknown",
      features: [],
      relatedProducts: [],
    }
  )
}

export default function ProductPage({ params }: { params: { id: string } }) {
  const product = getProductData(params.id)

  const discountedPrice =
    product.discount > 0 ? product.price - (product.price * product.discount) / 100 : product.price

  const formattedPrice = new Intl.NumberFormat("en-US").format(product.price)
  const formattedDiscountedPrice = new Intl.NumberFormat("en-US").format(discountedPrice)

  return (
    <div className="flex flex-col min-h-screen">
      <main className="flex-1">
        {/* Breadcrumb */}
        <div className="container py-4">
          <div className="flex items-center text-sm text-muted-foreground">
            <Link href="/" className="hover:text-primary transition-colors">
              Home
            </Link>
            <span className="mx-2">/</span>
            <Link href="/categories" className="hover:text-primary transition-colors">
              Categories
            </Link>
            <span className="mx-2">/</span>
            <Link
              href={`/categories/${product.category.toLowerCase().replace(/\s+/g, "-")}`}
              className="hover:text-primary transition-colors"
            >
              {product.category}
            </Link>
            <span className="mx-2">/</span>
            <span className="text-foreground">{product.title}</span>
          </div>
        </div>

        {/* Product Details */}
        <section className="container py-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">
            {/* Product Images */}
            <div className="space-y-4">
              <div className="border rounded-lg overflow-hidden aspect-square">
                <Image
                  src={product.images[0] || "/placeholder.svg"}
                  alt={product.title}
                  width={600}
                  height={600}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="grid grid-cols-4 gap-2">
                {product.images.map((image, index) => (
                  <div key={index} className="border rounded-lg overflow-hidden aspect-square cursor-pointer">
                    <Image
                      src={image || "/placeholder.svg"}
                      alt={`${product.title} - Image ${index + 1}`}
                      width={150}
                      height={150}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Product Info */}
            <div className="space-y-6">
              <div>
                <h1 className="text-2xl md:text-3xl font-bold">{product.title}</h1>
                <div className="flex items-center gap-4 mt-2">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${i < Math.floor(product.rating) ? "fill-primary text-primary" : "text-muted-foreground"}`}
                      />
                    ))}
                    <span className="ml-2 text-sm text-muted-foreground">
                      {product.rating} ({product.reviewCount} reviews)
                    </span>
                  </div>
                  <Separator orientation="vertical" className="h-4" />
                  <span className="text-sm text-muted-foreground">
                    {product.stock > 0 ? (
                      <span className="text-green-600">In Stock ({product.stock})</span>
                    ) : (
                      <span className="text-destructive">Out of Stock</span>
                    )}
                  </span>
                </div>
              </div>

              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <span className="text-2xl font-bold">
                    {product.currency} {formattedDiscountedPrice}
                  </span>
                  {product.discount > 0 && (
                    <>
                      <span className="text-lg text-muted-foreground line-through">
                        {product.currency} {formattedPrice}
                      </span>
                      <Badge className="bg-destructive hover:bg-destructive">-{product.discount}%</Badge>
                    </>
                  )}
                </div>
                <p className="text-sm text-muted-foreground">Price includes all taxes</p>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <span className="font-medium">Brand:</span>
                  <Link href={`/brands/${product.brand.toLowerCase()}`} className="text-primary hover:underline">
                    {product.brand}
                  </Link>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">SKU:</span>
                  <span>{product.sku}</span>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="font-medium">Key Features:</h3>
                <ul className="space-y-2">
                  {product.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="rounded-full bg-primary/10 p-1 mt-0.5">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-3 w-3 text-primary"
                        >
                          <polyline points="20 6 9 17 4 12" />
                        </svg>
                      </div>
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <div className="flex items-center border rounded-md">
                    <Button variant="ghost" size="icon" className="rounded-none">
                      <Minus className="h-4 w-4" />
                    </Button>
                    <span className="w-12 text-center">1</span>
                    <Button variant="ghost" size="icon" className="rounded-none">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <Button className="flex-1 gap-2">
                    <ShoppingCart className="h-4 w-4" />
                    Add to Cart
                  </Button>
                  <Button variant="outline" size="icon">
                    <Heart className="h-4 w-4" />
                  </Button>
                </div>
                <Button variant="outline" className="w-full gap-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-4 w-4"
                  >
                    <path d="M2 12V2h10" />
                    <path d="M2 7l8.75 8.75" />
                    <path d="M17 17h5v5" />
                    <path d="M14 22l8.84-8.84" />
                    <path d="M22 12V7h-5" />
                    <path d="M7 7H2v5" />
                  </svg>
                  Buy Now
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-4 pt-4">
                <div className="flex items-center gap-2">
                  <Truck className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Free Delivery</p>
                    <p className="text-xs text-muted-foreground">For orders over UGX 100,000</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <RotateCcw className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">30 Days Return</p>
                    <p className="text-xs text-muted-foreground">If goods have problems</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Secure Payment</p>
                    <p className="text-xs text-muted-foreground">100% secure payment</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Share2 className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Share Product</p>
                    <div className="flex items-center gap-1 mt-1">
                      <Button variant="ghost" size="icon" className="h-6 w-6">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-3 w-3"
                        >
                          <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
                        </svg>
                      </Button>
                      <Button variant="ghost" size="icon" className="h-6 w-6">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-3 w-3"
                        >
                          <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
                        </svg>
                      </Button>
                      <Button variant="ghost" size="icon" className="h-6 w-6">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="h-3 w-3"
                        >
                          <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                          <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                          <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                        </svg>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Product Details Tabs */}
        <section className="container py-8">
          <Tabs defaultValue="description" className="w-full">
            <TabsList className="w-full justify-start border-b rounded-none h-auto p-0">
              <TabsTrigger
                value="description"
                className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary"
              >
                Description
              </TabsTrigger>
              <TabsTrigger
                value="specifications"
                className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary"
              >
                Specifications
              </TabsTrigger>
              <TabsTrigger
                value="reviews"
                className="rounded-none data-[state=active]:border-b-2 data-[state=active]:border-primary"
              >
                Reviews ({product.reviewCount})
              </TabsTrigger>
            </TabsList>
            <TabsContent value="description" className="pt-6">
              <div className="prose max-w-none">
                <p>{product.description}</p>
              </div>
            </TabsContent>
            <TabsContent value="specifications" className="pt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-medium mb-4">Product Specifications</h3>
                  <div className="space-y-2">
                    <div className="grid grid-cols-2 gap-4 py-2 border-b">
                      <span className="text-muted-foreground">Brand</span>
                      <span>{product.brand}</span>
                    </div>
                    <div className="grid grid-cols-2 gap-4 py-2 border-b">
                      <span className="text-muted-foreground">Category</span>
                      <span>{product.category}</span>
                    </div>
                    <div className="grid grid-cols-2 gap-4 py-2 border-b">
                      <span className="text-muted-foreground">SKU</span>
                      <span>{product.sku}</span>
                    </div>
                    <div className="grid grid-cols-2 gap-4 py-2 border-b">
                      <span className="text-muted-foreground">Weight</span>
                      <span>250g</span>
                    </div>
                    <div className="grid grid-cols-2 gap-4 py-2 border-b">
                      <span className="text-muted-foreground">Dimensions</span>
                      <span>10 × 5 × 5 cm</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-4">Shipping Information</h3>
                  <div className="space-y-2">
                    <div className="grid grid-cols-2 gap-4 py-2 border-b">
                      <span className="text-muted-foreground">Delivery</span>
                      <span>2-4 Business Days</span>
                    </div>
                    <div className="grid grid-cols-2 gap-4 py-2 border-b">
                      <span className="text-muted-foreground">Shipping</span>
                      <span>Free for orders over UGX 100,000</span>
                    </div>
                    <div className="grid grid-cols-2 gap-4 py-2 border-b">
                      <span className="text-muted-foreground">Returns</span>
                      <span>30 Days Return Policy</span>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="reviews" className="pt-6">
              <div className="space-y-8">
                <div className="flex flex-col md:flex-row gap-8">
                  <div className="md:w-1/3">
                    <div className="text-center p-6 border rounded-lg">
                      <h3 className="text-5xl font-bold mb-2">{product.rating.toFixed(1)}</h3>
                      <div className="flex justify-center mb-2">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-5 w-5 ${i < Math.floor(product.rating) ? "fill-primary text-primary" : "text-muted-foreground"}`}
                          />
                        ))}
                      </div>
                      <p className="text-sm text-muted-foreground mb-4">Based on {product.reviewCount} reviews</p>
                      <Button className="w-full">Write a Review</Button>
                    </div>
                  </div>
                  <div className="md:w-2/3">
                    <h3 className="text-lg font-medium mb-4">Customer Reviews</h3>
                    <div className="space-y-6">
                      {/* Sample reviews - in a real app these would come from an API */}
                      <div className="border-b pb-6">
                        <div className="flex justify-between mb-2">
                          <h4 className="font-medium">Sarah Johnson</h4>
                          <span className="text-sm text-muted-foreground">2 days ago</span>
                        </div>
                        <div className="flex items-center mb-2">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-4 w-4 ${i < 5 ? "fill-primary text-primary" : "text-muted-foreground"}`}
                            />
                          ))}
                        </div>
                        <p className="text-sm">
                          This product is amazing! I've been using it for a month now and I can already see the
                          difference. My hair feels softer and looks healthier. Highly recommend!
                        </p>
                      </div>
                      <div className="border-b pb-6">
                        <div className="flex justify-between mb-2">
                          <h4 className="font-medium">Michael Thompson</h4>
                          <span className="text-sm text-muted-foreground">1 week ago</span>
                        </div>
                        <div className="flex items-center mb-2">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-4 w-4 ${i < 4 ? "fill-primary text-primary" : "text-muted-foreground"}`}
                            />
                          ))}
                        </div>
                        <p className="text-sm">
                          Good product overall. It does what it claims to do. The only downside is the scent is a bit
                          strong for my liking, but it fades quickly. Would buy again.
                        </p>
                      </div>
                      <div>
                        <div className="flex justify-between mb-2">
                          <h4 className="font-medium">Emily Davis</h4>
                          <span className="text-sm text-muted-foreground">2 weeks ago</span>
                        </div>
                        <div className="flex items-center mb-2">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-4 w-4 ${i < 5 ? "fill-primary text-primary" : "text-muted-foreground"}`}
                            />
                          ))}
                        </div>
                        <p className="text-sm">
                          I've tried many similar products and this one is by far the best. The quality is excellent and
                          it lasts a long time. Will definitely repurchase when I run out.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </section>

        {/* Related Products */}
        <section className="container py-12">
          <h2 className="text-2xl font-bold tracking-tight mb-8">Related Products</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-6">
            {product.relatedProducts.map((relatedId) => {
              const relatedProduct = getProductData(relatedId)
              return (
                <ProductCard
                  key={relatedId}
                  id={relatedId}
                  title={relatedProduct.title}
                  price={relatedProduct.price}
                  currency={relatedProduct.currency}
                  image="/placeholder.svg?height=300&width=300"
                  discount={relatedProduct.discount}
                  rating={relatedProduct.rating}
                  href={`/products/${relatedId}`}
                />
              )
            })}
          </div>
        </section>
      </main>
    </div>
  )
}

