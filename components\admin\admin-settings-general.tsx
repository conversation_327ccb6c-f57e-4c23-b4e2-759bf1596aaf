"use client"

import type React from "react"

import { useState } from "react"
import { Mail, MapPin, Phone, Save } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export function AdminSettingsGeneral() {
  const [storeSettings, setStoreSettings] = useState({
    storeName: "STXpress",
    storeEmail: "<EMAIL>",
    storePhone: "+256 (0)704 407 407",
    storeAddress: "Plot 1000, Kampala, Uganda",
    storeCurrency: "UGX",
    storeLanguage: "en",
    storeTimeZone: "Africa/Kampala",
    storeDescription: "STXpress is your one-stop shop for all your personal care, beauty, and home essentials.",
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setStoreSettings((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setStoreSettings((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // In a real app, this would save the settings to an API
    console.log("Saving settings:", storeSettings)
  }

  return (
    <form onSubmit={handleSubmit}>
      <Card>
        <CardHeader>
          <CardTitle>Store Information</CardTitle>
          <CardDescription>Manage your store's basic information.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="storeName">Store Name</Label>
              <Input id="storeName" name="storeName" value={storeSettings.storeName} onChange={handleChange} required />
            </div>
            <div className="space-y-2">
              <Label htmlFor="storeEmail">Email Address</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="storeEmail"
                  name="storeEmail"
                  type="email"
                  className="pl-10"
                  value={storeSettings.storeEmail}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="storePhone">Phone Number</Label>
              <div className="relative">
                <Phone className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="storePhone"
                  name="storePhone"
                  className="pl-10"
                  value={storeSettings.storePhone}
                  onChange
                  className="pl-10"
                  value={storeSettings.storePhone}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="storeAddress">Address</Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="storeAddress"
                  name="storeAddress"
                  className="pl-10"
                  value={storeSettings.storeAddress}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="storeDescription">Store Description</Label>
            <Textarea
              id="storeDescription"
              name="storeDescription"
              rows={3}
              value={storeSettings.storeDescription}
              onChange={handleChange}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <Label htmlFor="storeCurrency">Currency</Label>
              <Select
                value={storeSettings.storeCurrency}
                onValueChange={(value) => handleSelectChange("storeCurrency", value)}
              >
                <SelectTrigger id="storeCurrency">
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="UGX">Ugandan Shilling (UGX)</SelectItem>
                  <SelectItem value="USD">US Dollar (USD)</SelectItem>
                  <SelectItem value="EUR">Euro (EUR)</SelectItem>
                  <SelectItem value="GBP">British Pound (GBP)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="storeLanguage">Language</Label>
              <Select
                value={storeSettings.storeLanguage}
                onValueChange={(value) => handleSelectChange("storeLanguage", value)}
              >
                <SelectTrigger id="storeLanguage">
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="en">English</SelectItem>
                  <SelectItem value="fr">French</SelectItem>
                  <SelectItem value="es">Spanish</SelectItem>
                  <SelectItem value="sw">Swahili</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="storeTimeZone">Time Zone</Label>
              <Select
                value={storeSettings.storeTimeZone}
                onValueChange={(value) => handleSelectChange("storeTimeZone", value)}
              >
                <SelectTrigger id="storeTimeZone">
                  <SelectValue placeholder="Select time zone" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Africa/Kampala">East Africa Time (UTC+3)</SelectItem>
                  <SelectItem value="UTC">Coordinated Universal Time (UTC)</SelectItem>
                  <SelectItem value="America/New_York">Eastern Time (UTC-5)</SelectItem>
                  <SelectItem value="Europe/London">Greenwich Mean Time (UTC+0)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end">
          <Button type="submit" className="gap-2">
            <Save className="h-4 w-4" />
            Save Changes
          </Button>
        </CardFooter>
      </Card>
    </form>
  )
}

