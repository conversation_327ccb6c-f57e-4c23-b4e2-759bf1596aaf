import Link from "next/link"
import Image from "next/image"
import { ChevronRight, Calendar, Package, Truck, CheckCircle } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"

// Mock orders data - in a real app, this would come from an API
const orders = [
  {
    id: "STX12345",
    date: "April 2, 2025",
    total: 450000,
    status: "Delivered",
    items: [
      {
        id: "cantu-shea-butter",
        title: "Cantu Shea Butter Tea Tree Oil Leave-In Conditioner",
        price: 140000,
        quantity: 2,
        image: "/placeholder.svg?height=80&width=80",
      },
      {
        id: "oral-b-electric",
        title: "Oral-B Pro 1000 Electric Toothbrush",
        price: 350000,
        quantity: 1,
        image: "/placeholder.svg?height=80&width=80",
      },
    ],
    timeline: [
      { status: "Order Placed", date: "April 2, 2025", time: "09:15 AM", completed: true },
      { status: "Processing", date: "April 2, 2025", time: "11:30 AM", completed: true },
      { status: "Shipped", date: "April 3, 2025", time: "02:45 PM", completed: true },
      { status: "Out for Delivery", date: "April 4, 2025", time: "08:20 AM", completed: true },
      { status: "Delivered", date: "April 4, 2025", time: "03:10 PM", completed: true },
    ],
  },
  {
    id: "STX12346",
    date: "March 28, 2025",
    total: 140000,
    status: "Processing",
    items: [
      {
        id: "suave-essentials",
        title: "Suave Essentials Gentle Body Wash Ocean Breeze",
        price: 140000,
        quantity: 1,
        image: "/placeholder.svg?height=80&width=80",
      },
    ],
    timeline: [
      { status: "Order Placed", date: "March 28, 2025", time: "02:30 PM", completed: true },
      { status: "Processing", date: "March 29, 2025", time: "10:15 AM", completed: true },
      { status: "Shipped", date: "Pending", time: "", completed: false },
      { status: "Out for Delivery", date: "Pending", time: "", completed: false },
      { status: "Delivered", date: "Pending", time: "", completed: false },
    ],
  },
  {
    id: "STX12347",
    date: "March 15, 2025",
    total: 635000,
    status: "Delivered",
    items: [
      {
        id: "bamboo-duvet",
        title: "Bamboo Duvet Cover Queen Size",
        price: 450000,
        quantity: 1,
        image: "/placeholder.svg?height=80&width=80",
      },
      {
        id: "egyptian-pillow",
        title: "Egyptian Cotton King Size Pillow",
        price: 185000,
        quantity: 1,
        image: "/placeholder.svg?height=80&width=80",
      },
    ],
    timeline: [
      { status: "Order Placed", date: "March 15, 2025", time: "11:20 AM", completed: true },
      { status: "Processing", date: "March 15, 2025", time: "03:45 PM", completed: true },
      { status: "Shipped", date: "March 16, 2025", time: "09:30 AM", completed: true },
      { status: "Out for Delivery", date: "March 17, 2025", time: "08:15 AM", completed: true },
      { status: "Delivered", date: "March 17, 2025", time: "02:30 PM", completed: true },
    ],
  },
]

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-US").format(amount)
}

export function AccountOrders() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>My Orders</CardTitle>
        <CardDescription>View and track your order history.</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="all" className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="all">All Orders</TabsTrigger>
            <TabsTrigger value="processing">Processing</TabsTrigger>
            <TabsTrigger value="shipped">Shipped</TabsTrigger>
            <TabsTrigger value="delivered">Delivered</TabsTrigger>
          </TabsList>
          <TabsContent value="all" className="space-y-6">
            {orders.map((order) => (
              <Card key={order.id} className="overflow-hidden">
                <div className="bg-muted p-4 border-b flex justify-between items-center">
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Order #{order.id}</span>
                      <span className="text-sm text-muted-foreground">•</span>
                      <span className="text-sm text-muted-foreground flex items-center gap-1">
                        <Calendar className="h-3.5 w-3.5" />
                        {order.date}
                      </span>
                    </div>
                    <div className="text-sm text-muted-foreground mt-1">
                      {order.items.length} item{order.items.length !== 1 ? "s" : ""} • UGX {formatCurrency(order.total)}
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <div
                      className={`px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        order.status === "Delivered"
                          ? "bg-green-100 text-green-800"
                          : order.status === "Processing"
                            ? "bg-blue-100 text-blue-800"
                            : "bg-yellow-100 text-yellow-800"
                      }`}
                    >
                      {order.status}
                    </div>
                    <Button variant="ghost" size="sm" asChild>
                      <Link href={`/account/orders/${order.id}`}>
                        Details
                        <ChevronRight className="ml-1 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </div>
                <CardContent className="p-4">
                  <div className="space-y-4">
                    {order.items.map((item) => (
                      <div key={item.id} className="flex gap-4">
                        <div className="w-20 h-20 rounded-md overflow-hidden bg-muted/20 flex-shrink-0">
                          <Image
                            src={item.image || "/placeholder.svg"}
                            alt={item.title}
                            width={80}
                            height={80}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex-1">
                          <Link
                            href={`/products/${item.id}`}
                            className="font-medium hover:text-primary transition-colors line-clamp-2"
                          >
                            {item.title}
                          </Link>
                          <div className="text-sm text-muted-foreground mt-1">
                            Qty: {item.quantity} × UGX {formatCurrency(item.price)}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">UGX {formatCurrency(item.price * item.quantity)}</div>
                          <Button variant="ghost" size="sm" className="mt-2" asChild>
                            <Link href={`/products/${item.id}`}>Buy Again</Link>
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="mt-6 pt-6 border-t">
                    <h4 className="font-medium mb-3">Order Timeline</h4>
                    <div className="relative">
                      {order.timeline.map((step, index) => (
                        <div key={index} className="flex mb-4 last:mb-0">
                          <div className="mr-4 relative">
                            <div
                              className={`w-8 h-8 rounded-full flex items-center justify-center ${
                                step.completed ? "bg-primary text-primary-foreground" : "bg-muted text-muted-foreground"
                              }`}
                            >
                              {index === 0 ? (
                                <Package className="h-4 w-4" />
                              ) : index === order.timeline.length - 1 ? (
                                <CheckCircle className="h-4 w-4" />
                              ) : (
                                <Truck className="h-4 w-4" />
                              )}
                            </div>
                            {index < order.timeline.length - 1 && (
                              <div
                                className={`absolute top-8 left-1/2 w-0.5 h-full -translate-x-1/2 ${
                                  step.completed ? "bg-primary" : "bg-muted"
                                }`}
                              />
                            )}
                          </div>
                          <div>
                            <p className="font-medium">{step.status}</p>
                            <p className="text-sm text-muted-foreground">
                              {step.completed ? `${step.date} at ${step.time}` : "Pending"}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>
          <TabsContent value="processing">
            {orders
              .filter((order) => order.status === "Processing")
              .map((order) => (
                <Card key={order.id} className="overflow-hidden mb-6">
                  {/* Same content as above, filtered for processing orders */}
                  <div className="bg-muted p-4 border-b flex justify-between items-center">
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">Order #{order.id}</span>
                        <span className="text-sm text-muted-foreground">•</span>
                        <span className="text-sm text-muted-foreground flex items-center gap-1">
                          <Calendar className="h-3.5 w-3.5" />
                          {order.date}
                        </span>
                      </div>
                      <div className="text-sm text-muted-foreground mt-1">
                        {order.items.length} item{order.items.length !== 1 ? "s" : ""} • UGX{" "}
                        {formatCurrency(order.total)}
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {order.status}
                      </div>
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/account/orders/${order.id}`}>
                          Details
                          <ChevronRight className="ml-1 h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                  <CardContent className="p-4">
                    {/* Order items and timeline */}
                    <div className="space-y-4">
                      {order.items.map((item) => (
                        <div key={item.id} className="flex gap-4">
                          <div className="w-20 h-20 rounded-md overflow-hidden bg-muted/20 flex-shrink-0">
                            <Image
                              src={item.image || "/placeholder.svg"}
                              alt={item.title}
                              width={80}
                              height={80}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div className="flex-1">
                            <Link
                              href={`/products/${item.id}`}
                              className="font-medium hover:text-primary transition-colors line-clamp-2"
                            >
                              {item.title}
                            </Link>
                            <div className="text-sm text-muted-foreground mt-1">
                              Qty: {item.quantity} × UGX {formatCurrency(item.price)}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-medium">UGX {formatCurrency(item.price * item.quantity)}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            {orders.filter((order) => order.status === "Processing").length === 0 && (
              <div className="text-center py-12">
                <p className="text-muted-foreground">No processing orders found.</p>
              </div>
            )}
          </TabsContent>
          <TabsContent value="shipped">
            {orders
              .filter((order) => order.status === "Shipped")
              .map((order) => (
                <Card key={order.id} className="overflow-hidden mb-6">
                  <div className="bg-muted p-4 border-b flex justify-between items-center">
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">Order #{order.id}</span>
                        <span className="text-sm text-muted-foreground">•</span>
                        <span className="text-sm text-muted-foreground flex items-center gap-1">
                          <Calendar className="h-3.5 w-3.5" />
                          {order.date}
                        </span>
                      </div>
                      <div className="text-sm text-muted-foreground mt-1">
                        {order.items.length} item{order.items.length !== 1 ? "s" : ""} • UGX{" "}
                        {formatCurrency(order.total)}
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        {order.status}
                      </div>
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/account/orders/${order.id}`}>
                          Details
                          <ChevronRight className="ml-1 h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <div className="space-y-4">
                      {order.items.map((item) => (
                        <div key={item.id} className="flex gap-4">
                          <div className="w-20 h-20 rounded-md overflow-hidden bg-muted/20 flex-shrink-0">
                            <Image
                              src={item.image || "/placeholder.svg"}
                              alt={item.title}
                              width={80}
                              height={80}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div className="flex-1">
                            <Link
                              href={`/products/${item.id}`}
                              className="font-medium hover:text-primary transition-colors line-clamp-2"
                            >
                              {item.title}
                            </Link>
                            <div className="text-sm text-muted-foreground mt-1">
                              Qty: {item.quantity} × UGX {formatCurrency(item.price)}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-medium">UGX {formatCurrency(item.price * item.quantity)}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            {orders.filter((order) => order.status === "Shipped").length === 0 && (
              <div className="text-center py-12">
                <p className="text-muted-foreground">No shipped orders found.</p>
              </div>
            )}
          </TabsContent>
          <TabsContent value="delivered">
            {orders
              .filter((order) => order.status === "Delivered")
              .map((order) => (
                <Card key={order.id} className="overflow-hidden mb-6">
                  <div className="bg-muted p-4 border-b flex justify-between items-center">
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">Order #{order.id}</span>
                        <span className="text-sm text-muted-foreground">•</span>
                        <span className="text-sm text-muted-foreground flex items-center gap-1">
                          <Calendar className="h-3.5 w-3.5" />
                          {order.date}
                        </span>
                      </div>
                      <div className="text-sm text-muted-foreground mt-1">
                        {order.items.length} item{order.items.length !== 1 ? "s" : ""} • UGX{" "}
                        {formatCurrency(order.total)}
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {order.status}
                      </div>
                      <Button variant="ghost" size="sm" asChild>
                        <Link href={`/account/orders/${order.id}`}>
                          Details
                          <ChevronRight className="ml-1 h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <div className="space-y-4">
                      {order.items.map((item) => (
                        <div key={item.id} className="flex gap-4">
                          <div className="w-20 h-20 rounded-md overflow-hidden bg-muted/20 flex-shrink-0">
                            <Image
                              src={item.image || "/placeholder.svg"}
                              alt={item.title}
                              width={80}
                              height={80}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div className="flex-1">
                            <Link
                              href={`/products/${item.id}`}
                              className="font-medium hover:text-primary transition-colors line-clamp-2"
                            >
                              {item.title}
                            </Link>
                            <div className="text-sm text-muted-foreground mt-1">
                              Qty: {item.quantity} × UGX {formatCurrency(item.price)}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-medium">UGX {formatCurrency(item.price * item.quantity)}</div>
                            <Button variant="ghost" size="sm" className="mt-2" asChild>
                              <Link href={`/products/${item.id}`}>Buy Again</Link>
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            {orders.filter((order) => order.status === "Delivered").length === 0 && (
              <div className="text-center py-12">
                <p className="text-muted-foreground">No delivered orders found.</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

