"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { Heart, ShoppingCart, Trash2, AlertCircle } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

// Mock wishlist data - in a real app, this would come from a wishlist context/store
const initialWishlistItems = [
  {
    id: "cantu-shea-butter",
    title: "Cantu Shea Butter Tea Tree Oil Leave-In Conditioner",
    price: 140000,
    discount: 15,
    image: "/placeholder.svg?height=300&width=300",
    inStock: true,
  },
  {
    id: "bamboo-duvet",
    title: "Bamboo Duvet Cover Queen Size",
    price: 450000,
    discount: 10,
    image: "/placeholder.svg?height=300&width=300",
    inStock: true,
  },
  {
    id: "oral-b-electric",
    title: "Oral-B Pro 1000 Electric Toothbrush",
    price: 350000,
    discount: 10,
    image: "/placeholder.svg?height=300&width=300",
    inStock: false,
  },
]

export default function WishlistPage() {
  const [wishlistItems, setWishlistItems] = useState(initialWishlistItems)

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US").format(amount)
  }

  // Remove item from wishlist
  const removeFromWishlist = (id: string) => {
    setWishlistItems(wishlistItems.filter((item) => item.id !== id))
  }

  // Calculate discounted price
  const getDiscountedPrice = (price: number, discount: number) => {
    return discount > 0 ? price - (price * discount) / 100 : price
  }

  return (
    <div className="flex flex-col min-h-screen">
      <main className="flex-1">
        <div className="container py-8">
          <h1 className="text-3xl font-bold tracking-tight mb-6">My Wishlist</h1>

          {wishlistItems.length === 0 ? (
            <div className="text-center py-12">
              <div className="mx-auto w-24 h-24 rounded-full bg-muted flex items-center justify-center mb-6">
                <Heart className="h-12 w-12 text-muted-foreground" />
              </div>
              <h2 className="text-xl font-semibold mb-2">Your wishlist is empty</h2>
              <p className="text-muted-foreground mb-6">
                Add items you love to your wishlist. Review them anytime and easily move them to your cart.
              </p>
              <Button asChild>
                <Link href="/shop">Continue Shopping</Link>
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {wishlistItems.map((item) => {
                const discountedPrice = getDiscountedPrice(item.price, item.discount)

                return (
                  <Card key={item.id} className="overflow-hidden">
                    <CardContent className="p-0">
                      <div className="relative">
                        <Link href={`/products/${item.id}`}>
                          <div className="aspect-square overflow-hidden bg-muted/20">
                            <Image
                              src={item.image || "/placeholder.svg"}
                              alt={item.title}
                              width={400}
                              height={400}
                              className="w-full h-full object-cover transition-transform hover:scale-105"
                            />
                          </div>
                        </Link>
                        <Button
                          variant="destructive"
                          size="icon"
                          className="absolute top-2 right-2 h-8 w-8 rounded-full"
                          onClick={() => removeFromWishlist(item.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="p-4">
                        <Link href={`/products/${item.id}`} className="block">
                          <h3 className="font-medium line-clamp-2 hover:text-primary transition-colors">
                            {item.title}
                          </h3>
                        </Link>
                        <div className="flex items-center gap-2 mt-2">
                          <p className="font-semibold">UGX {formatCurrency(discountedPrice)}</p>
                          {item.discount > 0 && (
                            <p className="text-sm text-muted-foreground line-through">
                              UGX {formatCurrency(item.price)}
                            </p>
                          )}
                        </div>
                        {!item.inStock && (
                          <div className="flex items-center gap-1 text-destructive text-sm mt-2">
                            <AlertCircle className="h-4 w-4" />
                            <span>Out of stock</span>
                          </div>
                        )}
                        <div className="mt-4">
                          <Button className="w-full gap-2" disabled={!item.inStock}>
                            <ShoppingCart className="h-4 w-4" />
                            {item.inStock ? "Add to Cart" : "Out of Stock"}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}
        </div>
      </main>
    </div>
  )
}

