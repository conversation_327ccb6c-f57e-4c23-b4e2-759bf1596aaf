import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON>art, Trash2, AlertCircle } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

// Mock wishlist data - in a real app, this would come from a wishlist context/store
const wishlistItems = [
  {
    id: "cantu-shea-butter",
    title: "Cantu Shea Butter Tea Tree Oil Leave-In Conditioner",
    price: 140000,
    discount: 15,
    image: "/placeholder.svg?height=300&width=300",
    inStock: true,
  },
  {
    id: "bamboo-duvet",
    title: "Bamboo Duvet Cover Queen Size",
    price: 450000,
    discount: 10,
    image: "/placeholder.svg?height=300&width=300",
    inStock: true,
  },
  {
    id: "oral-b-electric",
    title: "Oral-B Pro 1000 Electric Toothbrush",
    price: 350000,
    discount: 10,
    image: "/placeholder.svg?height=300&width=300",
    inStock: false,
  },
]

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-US").format(amount)
}

// Calculate discounted price
const getDiscountedPrice = (price: number, discount: number) => {
  return discount > 0 ? price - (price * discount) / 100 : price
}

export function AccountWishlist() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>My Wishlist</CardTitle>
        <CardDescription>Items you've saved for later.</CardDescription>
      </CardHeader>
      <CardContent>
        {wishlistItems.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-muted-foreground">Your wishlist is empty.</p>
            <Button asChild className="mt-4">
              <Link href="/shop">Browse Products</Link>
            </Button>
          </div>
        ) : (
          <div className="space-y-6">
            {wishlistItems.map((item) => {
              const discountedPrice = getDiscountedPrice(item.price, item.discount)

              return (
                <div key={item.id} className="flex gap-4 border-b pb-6 last:border-b-0 last:pb-0">
                  <div className="w-20 h-20 rounded-md overflow-hidden bg-muted/20 flex-shrink-0">
                    <Image
                      src={item.image || "/placeholder.svg"}
                      alt={item.title}
                      width={80}
                      height={80}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <Link
                      href={`/products/${item.id}`}
                      className="font-medium hover:text-primary transition-colors line-clamp-2"
                    >
                      {item.title}
                    </Link>
                    <div className="flex items-center gap-2 mt-1">
                      <p className="font-semibold">UGX {formatCurrency(discountedPrice)}</p>
                      {item.discount > 0 && (
                        <p className="text-sm text-muted-foreground line-through">UGX {formatCurrency(item.price)}</p>
                      )}
                    </div>
                    {!item.inStock && (
                      <div className="flex items-center gap-1 text-destructive text-sm mt-1">
                        <AlertCircle className="h-4 w-4" />
                        <span>Out of stock</span>
                      </div>
                    )}
                  </div>
                  <div className="flex flex-col gap-2">
                    <Button variant="outline" size="sm" className="gap-2" disabled={!item.inStock}>
                      <ShoppingCart className="h-4 w-4" />
                      {item.inStock ? "Add to Cart" : "Out of Stock"}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-destructive hover:text-destructive hover:bg-destructive/10"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Remove
                    </Button>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

