import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Calendar, Clock, Edit, Plus, Trash2 } from "lucide-react"

export function AdminMarketingPromotions() {
  return (
    <div className="grid gap-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">Promotions</h2>
        <Button>
          <Plus className="mr-2 h-4 w-4" /> Create Promotion
        </Button>
      </div>

      <Tabs defaultValue="active" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="active">Active</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
          <TabsTrigger value="ended">Ended</TabsTrigger>
        </TabsList>

        <TabsContent value="active">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {[
              {
                title: "Summer Sale",
                description: "20% off all summer items",
                startDate: "Jun 1, 2023",
                endDate: "Aug 31, 2023",
                status: "Active",
              },
              {
                title: "New Arrivals Promotion",
                description: "Free shipping on new arrivals",
                startDate: "May 15, 2023",
                endDate: "Jun 15, 2023",
                status: "Active",
              },
              {
                title: "Weekend Flash Sale",
                description: "Up to 30% off select items",
                startDate: "Every Friday",
                endDate: "Every Sunday",
                status: "Active",
              },
            ].map((promotion, i) => (
              <Card key={i}>
                <CardHeader>
                  <CardTitle>{promotion.title}</CardTitle>
                  <CardDescription>{promotion.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center text-sm">
                      <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>
                        {promotion.startDate} - {promotion.endDate}
                      </span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>{promotion.status}</span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" size="sm">
                    <Edit className="mr-2 h-4 w-4" /> Edit
                  </Button>
                  <Button variant="destructive" size="sm">
                    <Trash2 className="mr-2 h-4 w-4" /> Delete
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="scheduled">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {[
              {
                title: "Back to School",
                description: "15% off school supplies",
                startDate: "Aug 1, 2023",
                endDate: "Sep 15, 2023",
                status: "Scheduled",
              },
              {
                title: "Fall Collection Launch",
                description: "New fall items with special pricing",
                startDate: "Sep 1, 2023",
                endDate: "Sep 30, 2023",
                status: "Scheduled",
              },
            ].map((promotion, i) => (
              <Card key={i}>
                <CardHeader>
                  <CardTitle>{promotion.title}</CardTitle>
                  <CardDescription>{promotion.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center text-sm">
                      <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>
                        {promotion.startDate} - {promotion.endDate}
                      </span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>{promotion.status}</span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" size="sm">
                    <Edit className="mr-2 h-4 w-4" /> Edit
                  </Button>
                  <Button variant="destructive" size="sm">
                    <Trash2 className="mr-2 h-4 w-4" /> Delete
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="ended">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {[
              {
                title: "Spring Collection",
                description: "10% off spring items",
                startDate: "Mar 1, 2023",
                endDate: "May 31, 2023",
                status: "Ended",
              },
              {
                title: "Mother's Day Special",
                description: "Gift with purchase over $50",
                startDate: "May 1, 2023",
                endDate: "May 14, 2023",
                status: "Ended",
              },
            ].map((promotion, i) => (
              <Card key={i}>
                <CardHeader>
                  <CardTitle>{promotion.title}</CardTitle>
                  <CardDescription>{promotion.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center text-sm">
                      <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>
                        {promotion.startDate} - {promotion.endDate}
                      </span>
                    </div>
                    <div className="flex items-center text-sm">
                      <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                      <span>{promotion.status}</span>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" size="sm">
                    <Edit className="mr-2 h-4 w-4" /> Duplicate
                  </Button>
                  <Button variant="destructive" size="sm">
                    <Trash2 className="mr-2 h-4 w-4" /> Delete
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

