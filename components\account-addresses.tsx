"use client"

import type React from "react"

import { useState } from "react"
import { MapPin, Plus, Edit, Trash2, Check } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

// Mock addresses data - in a real app, this would come from an API
const initialAddresses = [
  {
    id: "1",
    name: "Home",
    fullName: "John Doe",
    address: "123 Main Street, Apartment 4B",
    city: "Kampala",
    country: "Uganda",
    postalCode: "10001",
    phone: "+256 700 123 456",
    isDefault: true,
  },
  {
    id: "2",
    name: "<PERSON>",
    fullName: "<PERSON> Doe",
    address: "456 Business Avenue, Floor 3",
    city: "Kampala",
    country: "Uganda",
    postalCode: "10002",
    phone: "+256 700 123 456",
    isDefault: false,
  },
]

export function AccountAddresses() {
  const [addresses, setAddresses] = useState(initialAddresses)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentAddress, setCurrentAddress] = useState<(typeof initialAddresses)[0] | null>(null)
  const [newAddress, setNewAddress] = useState({
    id: "",
    name: "",
    fullName: "",
    address: "",
    city: "",
    country: "Uganda",
    postalCode: "",
    phone: "",
    isDefault: false,
  })

  const handleAddressChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setNewAddress((prev) => ({ ...prev, [name]: value }))
  }

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewAddress((prev) => ({ ...prev, isDefault: e.target.checked }))
  }

  const handleAddAddress = () => {
    const id = Date.now().toString()
    const addressToAdd = { ...newAddress, id }

    // If this is the first address or marked as default, update other addresses
    if (addressToAdd.isDefault) {
      setAddresses((prev) => prev.map((addr) => ({ ...addr, isDefault: false })))
    }

    setAddresses((prev) => [...prev, addressToAdd])
    resetForm()
    setIsAddDialogOpen(false)
  }

  const handleEditAddress = () => {
    if (!currentAddress) return

    // If setting this address as default, update other addresses
    if (newAddress.isDefault && !currentAddress.isDefault) {
      setAddresses((prev) => prev.map((addr) => ({ ...addr, isDefault: false })))
    }

    setAddresses((prev) => prev.map((addr) => (addr.id === currentAddress.id ? { ...newAddress, id: addr.id } : addr)))

    resetForm()
    setIsEditDialogOpen(false)
  }

  const handleDeleteAddress = () => {
    if (!currentAddress) return

    // If deleting the default address and there are other addresses, make another one default
    if (currentAddress.isDefault && addresses.length > 1) {
      const otherAddresses = addresses.filter((addr) => addr.id !== currentAddress.id)
      otherAddresses[0].isDefault = true
    }

    setAddresses((prev) => prev.filter((addr) => addr.id !== currentAddress.id))
    setIsDeleteDialogOpen(false)
  }

  const handleSetDefault = (id: string) => {
    setAddresses((prev) =>
      prev.map((addr) => ({
        ...addr,
        isDefault: addr.id === id,
      })),
    )
  }

  const openEditDialog = (address: (typeof initialAddresses)[0]) => {
    setCurrentAddress(address)
    setNewAddress(address)
    setIsEditDialogOpen(true)
  }

  const openDeleteDialog = (address: (typeof initialAddresses)[0]) => {
    setCurrentAddress(address)
    setIsDeleteDialogOpen(true)
  }

  const resetForm = () => {
    setNewAddress({
      id: "",
      name: "",
      fullName: "",
      address: "",
      city: "",
      country: "Uganda",
      postalCode: "",
      phone: "",
      isDefault: false,
    })
    setCurrentAddress(null)
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>My Addresses</CardTitle>
            <CardDescription>Manage your shipping and billing addresses.</CardDescription>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="gap-2">
                <Plus className="h-4 w-4" />
                Add New Address
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Address</DialogTitle>
                <DialogDescription>Add a new shipping or billing address to your account.</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Address Name</Label>
                    <Input
                      id="name"
                      name="name"
                      placeholder="Home, Office, etc."
                      value={newAddress.name}
                      onChange={handleAddressChange}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="fullName">Full Name</Label>
                    <Input
                      id="fullName"
                      name="fullName"
                      placeholder="John Doe"
                      value={newAddress.fullName}
                      onChange={handleAddressChange}
                      required
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="address">Street Address</Label>
                  <Textarea
                    id="address"
                    name="address"
                    placeholder="123 Main St, Apt 4B"
                    value={newAddress.address}
                    onChange={handleAddressChange}
                    required
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      name="city"
                      placeholder="Kampala"
                      value={newAddress.city}
                      onChange={handleAddressChange}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="postalCode">Postal Code</Label>
                    <Input
                      id="postalCode"
                      name="postalCode"
                      placeholder="10001"
                      value={newAddress.postalCode}
                      onChange={handleAddressChange}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    name="phone"
                    placeholder="+256 700 123 456"
                    value={newAddress.phone}
                    onChange={handleAddressChange}
                    required
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="isDefault"
                    className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                    checked={newAddress.isDefault}
                    onChange={handleCheckboxChange}
                  />
                  <Label htmlFor="isDefault">Set as default address</Label>
                </div>
              </div>
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="button" onClick={handleAddAddress}>
                  Add Address
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {addresses.map((address) => (
            <Card key={address.id} className={`overflow-hidden ${address.isDefault ? "border-primary" : ""}`}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-5 w-5 text-primary" />
                    <h3 className="font-medium">{address.name}</h3>
                    {address.isDefault && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary">
                        Default
                      </span>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => openEditDialog(address)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-destructive"
                      onClick={() => openDeleteDialog(address)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="space-y-1 text-sm">
                  <p className="font-medium">{address.fullName}</p>
                  <p>{address.address}</p>
                  <p>
                    {address.city}, {address.postalCode}
                  </p>
                  <p>{address.country}</p>
                  <p className="pt-1">{address.phone}</p>
                </div>
                {!address.isDefault && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-4 gap-2"
                    onClick={() => handleSetDefault(address.id)}
                  >
                    <Check className="h-4 w-4" />
                    Set as Default
                  </Button>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Edit Address Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Address</DialogTitle>
              <DialogDescription>Update your address information.</DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-name">Address Name</Label>
                  <Input id="edit-name" name="name" value={newAddress.name} onChange={handleAddressChange} required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-fullName">Full Name</Label>
                  <Input
                    id="edit-fullName"
                    name="fullName"
                    value={newAddress.fullName}
                    onChange={handleAddressChange}
                    required
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-address">Street Address</Label>
                <Textarea
                  id="edit-address"
                  name="address"
                  value={newAddress.address}
                  onChange={handleAddressChange}
                  required
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-city">City</Label>
                  <Input id="edit-city" name="city" value={newAddress.city} onChange={handleAddressChange} required />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-postalCode">Postal Code</Label>
                  <Input
                    id="edit-postalCode"
                    name="postalCode"
                    value={newAddress.postalCode}
                    onChange={handleAddressChange}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-phone">Phone Number</Label>
                <Input id="edit-phone" name="phone" value={newAddress.phone} onChange={handleAddressChange} required />
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="edit-isDefault"
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  checked={newAddress.isDefault}
                  onChange={handleCheckboxChange}
                />
                <Label htmlFor="edit-isDefault">Set as default address</Label>
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="button" onClick={handleEditAddress}>
                Save Changes
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Address Dialog */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Address</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this address? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="button" variant="destructive" onClick={handleDeleteAddress}>
                Delete Address
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  )
}

