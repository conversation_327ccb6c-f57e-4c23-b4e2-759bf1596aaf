import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Edit, Plus, Trash2 } from "lucide-react"

export function AdminMarketingDiscounts() {
  return (
    <div className="grid gap-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">Discounts</h2>
        <Button>
          <Plus className="mr-2 h-4 w-4" /> Create Discount
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Active Discounts</CardTitle>
          <CardDescription>Manage your store's active product and category discounts</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Value</TableHead>
                <TableHead>Applies To</TableHead>
                <TableHead>Start Date</TableHead>
                <TableHead>End Date</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[
                {
                  name: "Summer Sale",
                  type: "Percentage",
                  value: "20%",
                  appliesTo: "Summer Collection",
                  startDate: "Jun 1, 2023",
                  endDate: "Aug 31, 2023",
                },
                {
                  name: "Clearance Items",
                  type: "Percentage",
                  value: "50%",
                  appliesTo: "Clearance Category",
                  startDate: "May 15, 2023",
                  endDate: "Until stock lasts",
                },
                {
                  name: "New Customer",
                  type: "Fixed Amount",
                  value: "$10",
                  appliesTo: "First Order",
                  startDate: "Jan 1, 2023",
                  endDate: "Dec 31, 2023",
                },
                {
                  name: "Weekend Special",
                  type: "Percentage",
                  value: "15%",
                  appliesTo: "All Products",
                  startDate: "Every Friday",
                  endDate: "Every Sunday",
                },
                {
                  name: "Bulk Purchase",
                  type: "Percentage",
                  value: "10%",
                  appliesTo: "Orders over $100",
                  startDate: "Jan 1, 2023",
                  endDate: "Dec 31, 2023",
                },
              ].map((discount, i) => (
                <TableRow key={i}>
                  <TableCell className="font-medium">{discount.name}</TableCell>
                  <TableCell>{discount.type}</TableCell>
                  <TableCell>{discount.value}</TableCell>
                  <TableCell>{discount.appliesTo}</TableCell>
                  <TableCell>{discount.startDate}</TableCell>
                  <TableCell>{discount.endDate}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button variant="ghost" size="icon">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Create New Discount</CardTitle>
          <CardDescription>Set up a new discount for products or categories</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-4">
            <div className="grid gap-2">
              <Label htmlFor="discount-name">Discount Name</Label>
              <Input id="discount-name" placeholder="e.g. Summer Sale" />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="discount-type">Discount Type</Label>
              <Select>
                <SelectTrigger id="discount-type">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="percentage">Percentage</SelectItem>
                  <SelectItem value="fixed">Fixed Amount</SelectItem>
                  <SelectItem value="bogo">Buy One Get One</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="discount-value">Discount Value</Label>
              <Input id="discount-value" placeholder="e.g. 20 or 10.99" />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="applies-to">Applies To</Label>
              <Select>
                <SelectTrigger id="applies-to">
                  <SelectValue placeholder="Select target" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Products</SelectItem>
                  <SelectItem value="category">Specific Category</SelectItem>
                  <SelectItem value="products">Specific Products</SelectItem>
                  <SelectItem value="collections">Collections</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="start-date">Start Date</Label>
              <Input id="start-date" type="date" />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="end-date">End Date</Label>
              <Input id="end-date" type="date" />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="min-purchase">Minimum Purchase</Label>
              <Input id="min-purchase" placeholder="e.g. 50.00" />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="usage-limit">Usage Limit</Label>
              <Input id="usage-limit" placeholder="e.g. 100" />
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end gap-2">
          <Button variant="outline">Cancel</Button>
          <Button>Create Discount</Button>
        </CardFooter>
      </Card>
    </div>
  )
}

