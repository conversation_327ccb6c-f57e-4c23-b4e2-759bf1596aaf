"use client"

import { useState } from "react"
import Link from "next/link"
import { Arrow<PERSON>pDown, ChevronDown, Download, Filter, MoreHorizontal, Search, UserPlus } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Checkbox } from "@/components/ui/checkbox"
import { Pagination } from "@/components/ui/pagination"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

// Mock customers data
const customers = [
  {
    id: "CUST-12345",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+256 700 123 456",
    orders: 8,
    totalSpent: 1250000,
    status: "Active",
    lastOrder: "2025-04-02T15:30:00",
    dateJoined: "2024-01-15T10:20:00",
  },
  {
    id: "CUST-12346",
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+256 701 234 567",
    orders: 5,
    totalSpent: 780000,
    status: "Active",
    lastOrder: "2025-03-28T12:45:00",
    dateJoined: "2024-02-10T14:30:00",
  },
  {
    id: "CUST-12347",
    name: "Michael Johnson",
    email: "<EMAIL>",
    phone: "+256 702 345 678",
    orders: 12,
    totalSpent: 2100000,
    status: "Active",
    lastOrder: "2025-04-04T09:15:00",
    dateJoined: "2023-11-05T09:10:00",
  },
  {
    id: "CUST-12348",
    name: "Sarah Williams",
    email: "<EMAIL>",
    phone: "+256 703 456 789",
    orders: 3,
    totalSpent: 450000,
    status: "Active",
    lastOrder: "2025-03-15T16:20:00",
    dateJoined: "2024-03-01T11:45:00",
  },
  {
    id: "CUST-12349",
    name: "David Brown",
    email: "<EMAIL>",
    phone: "+256 704 567 890",
    orders: 7,
    totalSpent: 1050000,
    status: "Inactive",
    lastOrder: "2025-02-20T14:10:00",
    dateJoined: "2023-12-12T13:20:00",
  },
  {
    id: "CUST-12350",
    name: "Emily Davis",
    email: "<EMAIL>",
    phone: "+256 705 678 901",
    orders: 0,
    totalSpent: 0,
    status: "New",
    lastOrder: null,
    dateJoined: "2025-04-01T10:30:00",
  },
  {
    id: "CUST-12351",
    name: "Robert Wilson",
    email: "<EMAIL>",
    phone: "+256 706 789 012",
    orders: 4,
    totalSpent: 620000,
    status: "Active",
    lastOrder: "2025-03-25T11:40:00",
    dateJoined: "2024-01-20T15:15:00",
  },
  {
    id: "CUST-12352",
    name: "Jennifer Taylor",
    email: "<EMAIL>",
    phone: "+256 707 890 123",
    orders: 6,
    totalSpent: 890000,
    status: "Active",
    lastOrder: "2025-04-01T13:20:00",
    dateJoined: "2023-10-15T09:30:00",
  },
  {
    id: "CUST-12353",
    name: "Thomas Anderson",
    email: "<EMAIL>",
    phone: "+256 708 901 234",
    orders: 2,
    totalSpent: 320000,
    status: "Inactive",
    lastOrder: "2025-01-15T10:10:00",
    dateJoined: "2023-12-05T14:20:00",
  },
  {
    id: "CUST-12354",
    name: "Lisa Martinez",
    email: "<EMAIL>",
    phone: "+256 709 012 345",
    orders: 9,
    totalSpent: 1450000,
    status: "Active",
    lastOrder: "2025-03-30T16:30:00",
    dateJoined: "2023-09-10T11:10:00",
  },
]

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "UGX",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

// Format date
const formatDate = (dateString: string | null) => {
  if (!dateString) return "Never"

  const date = new Date(dateString)
  return new Intl.DateTimeFormat("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  }).format(date)
}

export default function CustomersPage() {
  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([])

  const handleSelectAll = () => {
    if (selectedCustomers.length === customers.length) {
      setSelectedCustomers([])
    } else {
      setSelectedCustomers(customers.map((customer) => customer.id))
    }
  }

  const handleSelectCustomer = (customerId: string) => {
    if (selectedCustomers.includes(customerId)) {
      setSelectedCustomers(selectedCustomers.filter((id) => id !== customerId))
    } else {
      setSelectedCustomers([...selectedCustomers, customerId])
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Customers</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" className="gap-2">
            <Download className="h-4 w-4" />
            Export
          </Button>
          <Button asChild>
            <Link href="/admin/customers/new" className="gap-2">
              <UserPlus className="h-4 w-4" />
              Add Customer
            </Link>
          </Button>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4 md:items-center justify-between">
        <div className="flex flex-col md:flex-row gap-4 md:items-center">
          <div className="relative w-full md:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input type="search" placeholder="Search customers..." className="pl-8" />
          </div>
          <Button variant="outline" className="gap-2">
            <Filter className="h-4 w-4" />
            Filter
            <ChevronDown className="h-4 w-4" />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="gap-2">
                Status
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem>All Statuses</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>Active</DropdownMenuItem>
              <DropdownMenuItem>Inactive</DropdownMenuItem>
              <DropdownMenuItem>New</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {selectedCustomers.length > 0 && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">{selectedCustomers.length} selected</span>
            <Button variant="outline" size="sm" className="gap-1">
              Send Email
            </Button>
          </div>
        )}
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedCustomers.length === customers.length && customers.length > 0}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>Customer</TableHead>
              <TableHead>Contact</TableHead>
              <TableHead>
                <div className="flex items-center gap-1">
                  Orders
                  <ArrowUpDown className="h-4 w-4" />
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center gap-1">
                  Total Spent
                  <ArrowUpDown className="h-4 w-4" />
                </div>
              </TableHead>
              <TableHead>Status</TableHead>
              <TableHead>
                <div className="flex items-center gap-1">
                  Last Order
                  <ArrowUpDown className="h-4 w-4" />
                </div>
              </TableHead>
              <TableHead className="w-12"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {customers.map((customer) => (
              <TableRow key={customer.id}>
                <TableCell>
                  <Checkbox
                    checked={selectedCustomers.includes(customer.id)}
                    onCheckedChange={() => handleSelectCustomer(customer.id)}
                  />
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <Avatar className="h-9 w-9">
                      <AvatarImage
                        src={`/placeholder.svg?height=36&width=36&text=${customer.name.charAt(0)}`}
                        alt={customer.name}
                      />
                      <AvatarFallback>{customer.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div className="min-w-0">
                      <p className="font-medium truncate">{customer.name}</p>
                      <p className="text-xs text-muted-foreground">Since {formatDate(customer.dateJoined)}</p>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm">{customer.email}</div>
                  <div className="text-xs text-muted-foreground">{customer.phone}</div>
                </TableCell>
                <TableCell>{customer.orders}</TableCell>
                <TableCell>{formatCurrency(customer.totalSpent)}</TableCell>
                <TableCell>
                  <Badge
                    variant={
                      customer.status === "Active" ? "default" : customer.status === "New" ? "default" : "secondary"
                    }
                    className={
                      customer.status === "Active"
                        ? "bg-green-100 text-green-800 hover:bg-green-100"
                        : customer.status === "New"
                          ? "bg-blue-100 text-blue-800 hover:bg-blue-100"
                          : "bg-gray-100 text-gray-800 hover:bg-gray-100"
                    }
                  >
                    {customer.status}
                  </Badge>
                </TableCell>
                <TableCell>{formatDate(customer.lastOrder)}</TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link href={`/admin/customers/${customer.id}`}>View Profile</Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/admin/customers/${customer.id}/edit`}>Edit</Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link href={`/admin/customers/${customer.id}/orders`}>View Orders</Link>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>Send Email</DropdownMenuItem>
                      <DropdownMenuItem className="text-destructive focus:text-destructive">Delete</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          Showing <strong>1-10</strong> of <strong>100</strong> customers
        </div>
        <Pagination />
      </div>
    </div>
  )
}

