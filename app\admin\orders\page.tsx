"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowUpDown, ChevronDown, Download, Eye, Filter, MoreHorizontal, Search } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Checkbox } from "@/components/ui/checkbox"
import { Pagination } from "@/components/ui/pagination"

// Mock orders data
const orders = [
  {
    id: "ORD-12345",
    customer: "<PERSON>",
    email: "<EMAIL>",
    date: "2025-04-04T10:30:00",
    total: 450000,
    status: "Delivered",
    items: 3,
    paymentStatus: "Paid",
  },
  {
    id: "ORD-12346",
    customer: "<PERSON>",
    email: "<EMAIL>",
    date: "2025-04-04T09:15:00",
    total: 140000,
    status: "Processing",
    items: 1,
    paymentStatus: "Paid",
  },
  {
    id: "ORD-12347",
    customer: "Michael Johnson",
    email: "<EMAIL>",
    date: "2025-04-04T08:45:00",
    total: 635000,
    status: "Pending",
    items: 4,
    paymentStatus: "Pending",
  },
  {
    id: "ORD-12348",
    customer: "Sarah Williams",
    email: "<EMAIL>",
    date: "2025-04-03T16:20:00",
    total: 275000,
    status: "Shipped",
    items: 2,
    paymentStatus: "Paid",
  },
  {
    id: "ORD-12349",
    customer: "David Brown",
    email: "<EMAIL>",
    date: "2025-04-03T14:10:00",
    total: 520000,
    status: "Delivered",
    items: 3,
    paymentStatus: "Paid",
  },
  {
    id: "ORD-12350",
    customer: "Emily Davis",
    email: "<EMAIL>",
    date: "2025-04-03T11:30:00",
    total: 320000,
    status: "Cancelled",
    items: 2,
    paymentStatus: "Refunded",
  },
  {
    id: "ORD-12351",
    customer: "Robert Wilson",
    email: "<EMAIL>",
    date: "2025-04-02T15:45:00",
    total: 180000,
    status: "Delivered",
    items: 1,
    paymentStatus: "Paid",
  },
  {
    id: "ORD-12352",
    customer: "Jennifer Taylor",
    email: "<EMAIL>",
    date: "2025-04-02T13:20:00",
    total: 420000,
    status: "Processing",
    items: 3,
    paymentStatus: "Paid",
  },
  {
    id: "ORD-12353",
    customer: "Thomas Anderson",
    email: "<EMAIL>",
    date: "2025-04-02T10:10:00",
    total: 195000,
    status: "Pending",
    items: 2,
    paymentStatus: "Pending",
  },
  {
    id: "ORD-12354",
    customer: "Lisa Martinez",
    email: "<EMAIL>",
    date: "2025-04-01T16:30:00",
    total: 560000,
    status: "Delivered",
    items: 4,
    paymentStatus: "Paid",
  },
]

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "UGX",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

// Format date
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return new Intl.DateTimeFormat("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
    hour: "numeric",
    minute: "numeric",
    hour12: true,
  }).format(date)
}

export default function OrdersPage() {
  const [selectedOrders, setSelectedOrders] = useState<string[]>([])

  const handleSelectAll = () => {
    if (selectedOrders.length === orders.length) {
      setSelectedOrders([])
    } else {
      setSelectedOrders(orders.map((order) => order.id))
    }
  }

  const handleSelectOrder = (orderId: string) => {
    if (selectedOrders.includes(orderId)) {
      setSelectedOrders(selectedOrders.filter((id) => id !== orderId))
    } else {
      setSelectedOrders([...selectedOrders, orderId])
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Orders</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" className="gap-2">
            <Download className="h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4 md:items-center justify-between">
        <div className="flex flex-col md:flex-row gap-4 md:items-center">
          <div className="relative w-full md:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input type="search" placeholder="Search orders..." className="pl-8" />
          </div>
          <Button variant="outline" className="gap-2">
            <Filter className="h-4 w-4" />
            Filter
            <ChevronDown className="h-4 w-4" />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="gap-2">
                Status
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem>All Statuses</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>Pending</DropdownMenuItem>
              <DropdownMenuItem>Processing</DropdownMenuItem>
              <DropdownMenuItem>Shipped</DropdownMenuItem>
              <DropdownMenuItem>Delivered</DropdownMenuItem>
              <DropdownMenuItem>Cancelled</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="gap-2">
                Payment
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem>All Payments</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>Paid</DropdownMenuItem>
              <DropdownMenuItem>Pending</DropdownMenuItem>
              <DropdownMenuItem>Refunded</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {selectedOrders.length > 0 && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">{selectedOrders.length} selected</span>
            <Button variant="outline" size="sm" className="gap-1">
              Update Status
            </Button>
          </div>
        )}
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={selectedOrders.length === orders.length && orders.length > 0}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>Order</TableHead>
              <TableHead>Customer</TableHead>
              <TableHead>
                <div className="flex items-center gap-1">
                  Date
                  <ArrowUpDown className="h-4 w-4" />
                </div>
              </TableHead>
              <TableHead>
                <div className="flex items-center gap-1">
                  Total
                  <ArrowUpDown className="h-4 w-4" />
                </div>
              </TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Payment</TableHead>
              <TableHead className="w-12"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {orders.map((order) => (
              <TableRow key={order.id}>
                <TableCell>
                  <Checkbox
                    checked={selectedOrders.includes(order.id)}
                    onCheckedChange={() => handleSelectOrder(order.id)}
                  />
                </TableCell>
                <TableCell>
                  <div className="font-medium">{order.id}</div>
                  <div className="text-xs text-muted-foreground">{order.items} items</div>
                </TableCell>
                <TableCell>
                  <div className="font-medium">{order.customer}</div>
                  <div className="text-xs text-muted-foreground">{order.email}</div>
                </TableCell>
                <TableCell>{formatDate(order.date)}</TableCell>
                <TableCell>{formatCurrency(order.total)}</TableCell>
                <TableCell>
                  <Badge
                    variant={
                      order.status === "Delivered"
                        ? "default"
                        : order.status === "Processing"
                          ? "default"
                          : order.status === "Shipped"
                            ? "warning"
                            : order.status === "Pending"
                              ? "secondary"
                              : "destructive"
                    }
                    className={
                      order.status === "Delivered"
                        ? "bg-green-100 text-green-800 hover:bg-green-100"
                        : order.status === "Processing"
                          ? "bg-blue-100 text-blue-800 hover:bg-blue-100"
                          : order.status === "Shipped"
                            ? "bg-yellow-100 text-yellow-800 hover:bg-yellow-100"
                            : order.status === "Pending"
                              ? "bg-gray-100 text-gray-800 hover:bg-gray-100"
                              : "bg-red-100 text-red-800 hover:bg-red-100"
                    }
                  >
                    {order.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge
                    variant={
                      order.paymentStatus === "Paid"
                        ? "default"
                        : order.paymentStatus === "Pending"
                          ? "secondary"
                          : "destructive"
                    }
                    className={
                      order.paymentStatus === "Paid"
                        ? "bg-green-100 text-green-800 hover:bg-green-100"
                        : order.paymentStatus === "Pending"
                          ? "bg-gray-100 text-gray-800 hover:bg-gray-100"
                          : "bg-red-100 text-red-800 hover:bg-red-100"
                    }
                  >
                    {order.paymentStatus}
                  </Badge>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link href={`/admin/orders/${order.id}`} className="flex items-center">
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem>Update Status</DropdownMenuItem>
                      <DropdownMenuItem>Send Invoice</DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-destructive focus:text-destructive">
                        Cancel Order
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          Showing <strong>1-10</strong> of <strong>100</strong> orders
        </div>
        <Pagination />
      </div>
    </div>
  )
}

