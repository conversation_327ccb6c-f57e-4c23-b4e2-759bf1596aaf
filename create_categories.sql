create table if not exists categories (
  id text primary key,
  name text not null unique,
  image text,
  created_at timestamptz default now()
);

insert into categories (id, name, image) values
  ('beauty', 'Beauty & Health', '/placeholder.svg?height=200&width=200'),
  ('oral', 'Oral Hygiene', '/placeholder.svg?height=200&width=200'),
  ('bedding', 'Beddings', '/placeholder.svg?height=200&width=200'),
  ('footwear', 'Footwear', '/placeholder.svg?height=200&width=200'),
  ('perfumes', 'Perfumes', '/placeholder.svg?height=200&width=200')
on conflict (id) do nothing;
