import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { AdminMarketingOverview } from "@/components/admin/admin-marketing-overview"
import { AdminMarketingPromotions } from "@/components/admin/admin-marketing-promotions"
import { AdminMarketingDiscounts } from "@/components/admin/admin-marketing-discounts"
import { AdminMarketingCoupons } from "@/components/admin/admin-marketing-coupons"

export default function MarketingPage() {
  return (
    <div className="flex flex-col gap-6 p-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Marketing</h1>
        <p className="text-muted-foreground">
          Manage your store's marketing campaigns, promotions, discounts, and coupons.
        </p>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="promotions">Promotions</TabsTrigger>
          <TabsTrigger value="discounts">Discounts</TabsTrigger>
          <TabsTrigger value="coupons">Coupons</TabsTrigger>
        </TabsList>
        <TabsContent value="overview">
          <AdminMarketingOverview />
        </TabsContent>
        <TabsContent value="promotions">
          <AdminMarketingPromotions />
        </TabsContent>
        <TabsContent value="discounts">
          <AdminMarketingDiscounts />
        </TabsContent>
        <TabsContent value="coupons">
          <AdminMarketingCoupons />
        </TabsContent>
      </Tabs>
    </div>
  )
}

