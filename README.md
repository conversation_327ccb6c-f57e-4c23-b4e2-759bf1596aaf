# STXpress E-commerce Platform

<div align="center">
  <img src="public/logo.png" alt="STXpress Logo" width="200" height="auto">
  
  <p align="center">
    A modern, full-featured e-commerce platform built with Next.js
    <br />
    <a href="#demo"><strong>View Demo »</strong></a>
    <br />
    <br />
    <a href="#getting-started">Getting Started</a>
    ·
    <a href="#features">Features</a>
    ·
    <a href="#contributing">Contributing</a>
    ·
    <a href="#license">License</a>
  </p>
</div>

## 📋 Table of Contents

- [Project Overview](#-project-overview)
- [Features](#-features)
- [Tech Stack](#-tech-stack)
- [Folder Structure](#-folder-structure)
- [Getting Started](#-getting-started)
- [Usage Guide](#-usage-guide)
- [Component Design](#-component-design)
- [Environment Variables](#-environment-variables)
- [Testing](#-testing)
- [Deployment](#-deployment)
- [Contributing](#-contributing)
- [License](#-license)

## 🚀 Project Overview

STXpress is a comprehensive e-commerce platform designed for modern online retail businesses. Built with Next.js 14 and featuring a responsive design, it provides both customer-facing shopping experiences and powerful administrative tools for store management.

The platform offers a complete solution for:
- **Customers**: Browse products, manage wishlists, secure checkout, order tracking
- **Administrators**: Product management, order processing, analytics, marketing tools
- **Store Owners**: Complete control over store appearance, settings, and operations

### Key Highlights
- 🛍️ **Full E-commerce Functionality** - Complete shopping cart, checkout, and order management
- 📱 **Responsive Design** - Optimized for desktop, tablet, and mobile devices
- 🎨 **Modern UI/UX** - Clean, intuitive interface built with Tailwind CSS and shadcn/ui
- 📊 **Admin Dashboard** - Comprehensive management tools with analytics
- 🔍 **Advanced Search** - Product filtering, sorting, and search functionality
- 💳 **Multiple Payment Options** - Support for various payment methods
- 🚚 **Shipping Management** - Flexible shipping options and tracking

## ✨ Features

### Customer Features
- **Product Browsing**
  - Category-based navigation
  - Advanced filtering and sorting
  - Product search with suggestions
  - Product comparison
  
- **User Account Management**
  - User registration and authentication
  - Profile management
  - Order history and tracking
  - Address book management
  - Payment method storage

- **Shopping Experience**
  - Shopping cart with quantity management
  - Wishlist functionality
  - Product reviews and ratings
  - Recently viewed products
  - Product recommendations

- **Checkout Process**
  - Guest and registered user checkout
  - Multiple shipping options
  - Coupon and discount codes
  - Order confirmation and tracking

### Admin Features
- **Dashboard Overview**
  - Sales analytics and metrics
  - Order status monitoring
  - Customer activity tracking
  - Revenue reports

- **Product Management**
  - Add, edit, and delete products
  - Category management
  - Inventory tracking
  - Bulk product operations

- **Order Management**
  - Order processing workflow
  - Status updates and notifications
  - Refund and return handling
  - Invoice generation

- **Marketing Tools**
  - Promotional campaigns
  - Discount and coupon management
  - Email marketing integration
  - SEO optimization tools

- **Analytics & Reporting**
  - Sales performance tracking
  - Customer behavior analysis
  - Product performance metrics
  - Custom report generation

## 🛠 Tech Stack

### Frontend Framework
- **[Next.js 14](https://nextjs.org/)** - React framework with App Router
- **[React 18](https://reactjs.org/)** - UI library
- **[TypeScript](https://www.typescriptlang.org/)** - Type safety and better developer experience

### Styling & UI
- **[Tailwind CSS](https://tailwindcss.com/)** - Utility-first CSS framework
- **[shadcn/ui](https://ui.shadcn.com/)** - Re-usable component library
- **[Lucide React](https://lucide.dev/)** - Icon library
- **[Framer Motion](https://www.framer.com/motion/)** - Animation library

### Data Visualization
- **[@nivo/bar](https://nivo.rocks/)** - Chart components for analytics
- **[@nivo/line](https://nivo.rocks/)** - Line charts for trends
- **[@nivo/pie](https://nivo.rocks/)** - Pie charts for data distribution

### Development Tools
- **[ESLint](https://eslint.org/)** - Code linting
- **[Prettier](https://prettier.io/)** - Code formatting
- **[Husky](https://typicode.github.io/husky/)** - Git hooks

### Deployment
- **[Vercel](https://vercel.com/)** - Hosting and deployment platform

## 📁 Folder Structure

```
stxpress-ecommerce/
├── 📁 app/                     # Next.js App Router pages
│   ├── 📁 admin/               # Admin dashboard pages
│   │   ├── 📁 analytics/       # Analytics page
│   │   ├── 📁 content/         # Content management
│   │   ├── 📁 customers/       # Customer management
│   │   ├── 📁 marketing/       # Marketing tools
│   │   ├── 📁 orders/          # Order management
│   │   ├── 📁 products/        # Product management
│   │   └── 📁 settings/        # Admin settings
│   ├── 📁 account/             # User account pages
│   ├── 📁 cart/                # Shopping cart
│   ├── 📁 categories/          # Product categories
│   ├── 📁 checkout/            # Checkout process
│   ├── 📁 products/            # Product detail pages
│   ├── 📁 search/              # Search results
│   ├── 📁 shop/                # Product listing
│   ├── 📁 wishlist/            # User wishlist
│   ├── 📄 globals.css          # Global styles
│   ├── 📄 layout.tsx           # Root layout
│   ├── 📄 loading.tsx          # Loading UI
│   └── 📄 page.tsx             # Home page
├── 📁 components/              # React components
│   ├── 📁 admin/               # Admin-specific components
│   ├── 📁 ui/                  # shadcn/ui components
│   ├── 📄 footer.tsx           # Site footer
│   ├── 📄 header.tsx           # Site header
│   └── 📄 ...                  # Other shared components
├── 📁 hooks/                   # Custom React hooks
│   ├── 📄 use-mobile.tsx       # Mobile detection hook
│   └── 📄 use-toast.ts         # Toast notification hook
├── 📁 lib/                     # Utility functions
│   └── 📄 utils.ts             # Helper functions
├── 📁 public/                  # Static assets
│   ├── 📄 logo.png             # Site logo
│   ├── 📄 logo.svg             # SVG logo
│   └── 📄 ...                  # Other static files
├── 📄 .env.local               # Environment variables
├── 📄 .gitignore               # Git ignore rules
├── 📄 next.config.mjs          # Next.js configuration
├── 📄 package.json             # Dependencies and scripts
├── 📄 tailwind.config.ts       # Tailwind CSS configuration
└── 📄 tsconfig.json            # TypeScript configuration
```

## 🚀 Getting Started

### Prerequisites

Before you begin, ensure you have the following installed:
- **Node.js** (version 18.x or later) - [Download here](https://nodejs.org/)
- **npm** or **yarn** package manager
- **Git** for version control

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/stxpress-ecommerce.git
   cd stxpress-ecommerce