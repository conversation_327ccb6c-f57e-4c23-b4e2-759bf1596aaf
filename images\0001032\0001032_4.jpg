<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    Fore more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base
  -->
  <base href="/">
  <meta name="google-signin-client_id" content="YOUR_AUTH_CLIENT_ID">
  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="GroFresh.">
  <!-- DEMO ONLY -->


  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="flutter_grocery">
  <link rel="apple-touch-icon" href="icons/app_logo.png">

  <!-- Favicon -->

  <link rel="icon" type="image/png" href="favicon.png"/>


  <title>WillkinsPharmacy</title>
  <link rel="manifest" href="manifest.json">
  <link rel="stylesheet" type="text/css" href="style.css">

  <style>.grecaptcha-badge { visibility: hidden;} </style>
</head>
<body>


<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDJ6JSd1feeuCT9zXP7NHLNtCvuTIarTIA&callback=Function.prototype"></script>


<script>
    if ("serviceWorker" in navigator) {
      window.addEventListener("load", function () {
        navigator.serviceWorker.register("/firebase-messaging-sw.js");
      });
      window.addEventListener('flutter-first-frame', function () {
        navigator.serviceWorker.register('flutter_service_worker.js?v=3211578450');
      });
    }
  </script>


<script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
<script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-messaging.js"></script>

<div class="preloader">
  <div class="preloader-container">
    <img width="55" class="preloader-img" src="assets/preloader.svg" alt="">
    <div class="loader"></div>
  </div>
</div>
<!-- Header -->
<header class="header">
  <div class="header-top-line"></div>
  <div class="header-container d-flex align-items-center justify-content-between">
    <div class="header-start d-flex gap-4 align-items-center">
      <img class="logo" src="assets/logo.svg" alt="">
      <div class="placeholder"></div>
      <div class="placeholder"></div>
      <div class="placeholder"></div>
    </div>
    <div class="header-end d-flex align-items-center gap-4">
      <div class="placeholder placeholder-wide"></div>
      <div class="placeholder"></div>
      <div class="placeholder"></div>
      <img class="svg" src="assets/cart_icon.svg" alt="">
      <img class="svg" src="assets/menu_icon.svg" alt="">
    </div>
  </div>
</header>

<script>
  // Check if localStorage is supported in the browser
  if (typeof Storage !== "undefined") {
    var itemValue = localStorage.getItem("flutter.theme");
    document.body.classList.toggle("theme-dark", itemValue === "true");
  }

</script>



<script>
    const firebaseConfig = {
      apiKey: "AIzaSyDJ6JSd1feeuCT9zXP7NHLNtCvuTIarTIA",
      authDomain: "grofresh-3986f.firebaseapp.com",
      projectId: "grofresh-3986f",
      storageBucket: "grofresh-3986f.appspot.com",
      messagingSenderId: "250728969979",
      appId: "1:250728969979:web:b79642a7b2d2400b75a25e",
      measurementId: "G-X1HCG4K8HJ"
    };

    // Initialize Firebase
    firebase.initializeApp(firebaseConfig);
  </script>

<script src="main.dart.js?version=7.3" type="application/javascript"></script>
</body>
</html>
