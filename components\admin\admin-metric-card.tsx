import type React from "react"
import { ArrowDown, ArrowUp } from "lucide-react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface AdminMetricCardProps {
  title: string
  value: string
  description: string
  icon: React.ReactNode
  trend: "up" | "down" | "neutral"
}

export function AdminMetricCard({ title, value, description, icon, trend }: AdminMetricCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <CardDescription className="flex items-center pt-1">
          {trend === "up" && <ArrowUp className="mr-1 h-4 w-4 text-green-500" />}
          {trend === "down" && <ArrowDown className="mr-1 h-4 w-4 text-red-500" />}
          <span className={trend === "up" ? "text-green-500" : trend === "down" ? "text-red-500" : ""}>
            {description}
          </span>
        </CardDescription>
      </CardContent>
    </Card>
  )
}

