import { Filter, <PERSON>lide<PERSON>H<PERSON>zon<PERSON>, ChevronDown, Search } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Sheet, She<PERSON><PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { ProductFilters } from "@/components/product-filters"
import { ProductSort } from "@/components/product-sort"
import { Pagination } from "@/components/pagination"
import { ProductGrid } from "@/components/product-grid"
import { ShopBanner } from "@/components/shop-banner"
import { ShopCategories } from "@/components/shop-categories"
import { FeaturedProducts } from "@/components/featured-products"

export default function ShopPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <ShopBanner />

      <div className="container py-8">
        <ShopCategories />
      </div>

      <div className="bg-muted/40">
        <div className="container py-8">
          <div className="flex items-center gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input type="search" placeholder="Search products..." className="pl-8 bg-background" />
            </div>
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" size="icon" className="flex md:hidden">
                  <Filter className="h-4 w-4" />
                  <span className="sr-only">Filter</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-[300px] sm:w-[400px]">
                <div className="px-1">
                  <h2 className="text-lg font-semibold mb-4">Filters</h2>
                  <ProductFilters className="mt-4" />
                </div>
              </SheetContent>
            </Sheet>
            <ProductSort />
          </div>
        </div>
      </div>

      <div className="container py-8">
        <FeaturedProducts />
      </div>

      <div className="flex-1 container py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="hidden md:block">
            <div className="sticky top-20">
              <div className="flex items-center justify-between mb-4">
                <h2 className="font-semibold text-lg">Filters</h2>
                <Button variant="ghost" size="sm" className="h-8 text-xs">
                  Reset All
                </Button>
              </div>
              <Separator className="mb-4" />
              <ProductFilters />
            </div>
          </div>
          <div className="md:col-span-3">
            <div className="flex items-center justify-between mb-6">
              <p className="text-sm text-muted-foreground">Showing 1-20 of 248 products</p>
              <div className="hidden md:flex items-center gap-2">
                <Button variant="outline" size="sm" className="h-8 gap-1">
                  <SlidersHorizontal className="h-3.5 w-3.5" />
                  <span>View</span>
                  <ChevronDown className="h-3.5 w-3.5" />
                </Button>
              </div>
            </div>
            <ProductGrid />
            <Pagination className="mt-12" />
          </div>
        </div>
      </div>
    </div>
  )
}

