"use client"

import type React from "react"

import { useState } from "react"
import Image from "next/image"
import { Check, Save, Upload } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"

export function AdminSettingsAppearance() {
  const [theme, setTheme] = useState("light")
  const [primaryColor, setPrimaryColor] = useState("red")
  const [logo, setLogo] = useState("/logo.png")
  const [favicon, setFavicon] = useState("/favicon.ico")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // In a real app, this would save the settings to an API
    console.log("Saving appearance settings:", { theme, primaryColor, logo, favicon })
  }

  return (
    <form onSubmit={handleSubmit}>
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Theme</CardTitle>
            <CardDescription>Choose the theme for your store's admin dashboard.</CardDescription>
          </CardHeader>
          <CardContent>
            <RadioGroup value={theme} onValueChange={setTheme} className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <RadioGroupItem value="light" id="theme-light" className="sr-only peer" />
                <Label
                  htmlFor="theme-light"
                  className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                >
                  <div className="mb-3 rounded-md border border-muted p-1 bg-background">
                    <div className="h-24 w-full rounded-sm bg-background">
                      <div className="h-8 rounded-sm bg-muted/50"></div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between w-full">
                    <span className="text-sm font-medium">Light</span>
                    {theme === "light" && <Check className="h-4 w-4" />}
                  </div>
                </Label>
              </div>
              <div>
                <RadioGroupItem value="dark" id="theme-dark" className="sr-only peer" />
                <Label
                  htmlFor="theme-dark"
                  className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                >
                  <div className="mb-3 rounded-md border border-muted p-1 bg-slate-950">
                    <div className="h-24 w-full rounded-sm bg-slate-950">
                      <div className="h-8 rounded-sm bg-slate-800"></div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between w-full">
                    <span className="text-sm font-medium">Dark</span>
                    {theme === "dark" && <Check className="h-4 w-4" />}
                  </div>
                </Label>
              </div>
              <div>
                <RadioGroupItem value="system" id="theme-system" className="sr-only peer" />
                <Label
                  htmlFor="theme-system"
                  className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-popover p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                >
                  <div className="mb-3 rounded-md border border-muted p-1 bg-background">
                    <div className="flex h-24 w-full">
                      <div className="w-1/2 rounded-l-sm bg-background">
                        <div className="h-8 rounded-sm bg-muted/50"></div>
                      </div>
                      <div className="w-1/2 rounded-r-sm bg-slate-950">
                        <div className="h-8 rounded-sm bg-slate-800"></div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between w-full">
                    <span className="text-sm font-medium">System</span>
                    {theme === "system" && <Check className="h-4 w-4" />}
                  </div>
                </Label>
              </div>
            </RadioGroup>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Brand Colors</CardTitle>
            <CardDescription>Choose the primary color for your store's branding.</CardDescription>
          </CardHeader>
          <CardContent>
            <RadioGroup
              value={primaryColor}
              onValueChange={setPrimaryColor}
              className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-4"
            >
              <div>
                <RadioGroupItem value="red" id="color-red" className="sr-only peer" />
                <Label
                  htmlFor="color-red"
                  className="flex flex-col items-center justify-between rounded-md border-2 border-muted p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                >
                  <div className="mb-3 h-10 w-10 rounded-full bg-red-500"></div>
                  <div className="flex items-center justify-between w-full">
                    <span className="text-sm font-medium">Red</span>
                    {primaryColor === "red" && <Check className="h-4 w-4" />}
                  </div>
                </Label>
              </div>
              <div>
                <RadioGroupItem value="blue" id="color-blue" className="sr-only peer" />
                <Label
                  htmlFor="color-blue"
                  className="flex flex-col items-center justify-between rounded-md border-2 border-muted p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                >
                  <div className="mb-3 h-10 w-10 rounded-full bg-blue-500"></div>
                  <div className="flex items-center justify-between w-full">
                    <span className="text-sm font-medium">Blue</span>
                    {primaryColor === "blue" && <Check className="h-4 w-4" />}
                  </div>
                </Label>
              </div>
              <div>
                <RadioGroupItem value="green" id="color-green" className="sr-only peer" />
                <Label
                  htmlFor="color-green"
                  className="flex flex-col items-center justify-between rounded-md border-2 border-muted p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                >
                  <div className="mb-3 h-10 w-10 rounded-full bg-green-500"></div>
                  <div className="flex items-center justify-between w-full">
                    <span className="text-sm font-medium">Green</span>
                    {primaryColor === "green" && <Check className="h-4 w-4" />}
                  </div>
                </Label>
              </div>
              <div>
                <RadioGroupItem value="purple" id="color-purple" className="sr-only peer" />
                <Label
                  htmlFor="color-purple"
                  className="flex flex-col items-center justify-between rounded-md border-2 border-muted p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                >
                  <div className="mb-3 h-10 w-10 rounded-full bg-purple-500"></div>
                  <div className="flex items-center justify-between w-full">
                    <span className="text-sm font-medium">Purple</span>
                    {primaryColor === "purple" && <Check className="h-4 w-4" />}
                  </div>
                </Label>
              </div>
              <div>
                <RadioGroupItem value="orange" id="color-orange" className="sr-only peer" />
                <Label
                  htmlFor="color-orange"
                  className="flex flex-col items-center justify-between rounded-md border-2 border-muted p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                >
                  <div className="mb-3 h-10 w-10 rounded-full bg-orange-500"></div>
                  <div className="flex items-center justify-between w-full">
                    <span className="text-sm font-medium">Orange</span>
                    {primaryColor === "orange" && <Check className="h-4 w-4" />}
                  </div>
                </Label>
              </div>
              <div>
                <RadioGroupItem value="custom" id="color-custom" className="sr-only peer" />
                <Label
                  htmlFor="color-custom"
                  className="flex flex-col items-center justify-between rounded-md border-2 border-muted p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
                >
                  <div className="mb-3 h-10 w-10 rounded-full bg-gradient-to-r from-pink-500 via-red-500 to-yellow-500"></div>
                  <div className="flex items-center justify-between w-full">
                    <span className="text-sm font-medium">Custom</span>
                    {primaryColor === "custom" && <Check className="h-4 w-4" />}
                  </div>
                </Label>
              </div>
            </RadioGroup>

            {primaryColor === "custom" && (
              <div className="mt-4 space-y-2">
                <Label htmlFor="customColor">Custom Color (HEX)</Label>
                <Input id="customColor" placeholder="#FF0000" className="w-full max-w-xs" />
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Brand Assets</CardTitle>
            <CardDescription>Upload your store's logo and favicon.</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="logo" className="w-full">
              <TabsList className="grid w-full max-w-md grid-cols-2">
                <TabsTrigger value="logo">Logo</TabsTrigger>
                <TabsTrigger value="favicon">Favicon</TabsTrigger>
              </TabsList>
              <TabsContent value="logo" className="mt-4">
                <div className="flex flex-col items-center gap-4">
                  <div className="border rounded-lg p-4 bg-muted/20 w-full max-w-md flex justify-center">
                    <Image
                      src={logo || "/placeholder.svg"}
                      alt="Store Logo"
                      width={200}
                      height={60}
                      className="max-h-20 w-auto"
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <Button type="button" variant="outline" className="gap-2">
                      <Upload className="h-4 w-4" />
                      Upload New Logo
                    </Button>
                    <Button type="button" variant="outline" className="text-destructive hover:text-destructive">
                      Remove
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Recommended size: 200x60 pixels. Max file size: 2MB. Supported formats: PNG, JPG, SVG.
                  </p>
                </div>
              </TabsContent>
              <TabsContent value="favicon" className="mt-4">
                <div className="flex flex-col items-center gap-4">
                  <div className="border rounded-lg p-4 bg-muted/20 w-full max-w-md flex justify-center">
                    <Image
                      src={favicon || "/placeholder.svg"}
                      alt="Store Favicon"
                      width={64}
                      height={64}
                      className="max-h-16 w-auto"
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <Button type="button" variant="outline" className="gap-2">
                      <Upload className="h-4 w-4" />
                      Upload New Favicon
                    </Button>
                    <Button type="button" variant="outline" className="text-destructive hover:text-destructive">
                      Remove
                    </Button>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Recommended size: 32x32 pixels. Max file size: 1MB. Supported formats: ICO, PNG.
                  </p>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
          <CardFooter className="flex justify-end">
            <Button type="submit" className="gap-2">
              <Save className="h-4 w-4" />
              Save Changes
            </Button>
          </CardFooter>
        </Card>
      </div>
    </form>
  )
}

