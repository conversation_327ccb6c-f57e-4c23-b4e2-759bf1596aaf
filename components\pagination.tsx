import { ChevronLeft, ChevronRight } from "lucide-react"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"

interface PaginationProps {
  className?: string
}

export function Pagination({ className }: PaginationProps) {
  return (
    <div className={cn("flex items-center justify-center space-x-2", className)}>
      <Button variant="outline" size="icon" disabled>
        <ChevronLeft className="h-4 w-4" />
        <span className="sr-only">Previous page</span>
      </Button>
      <Button variant="outline" size="sm" className="h-8 w-8">
        1
      </Button>
      <Button variant="outline" size="sm" className="h-8 w-8" disabled>
        2
      </Button>
      <Button variant="outline" size="sm" className="h-8 w-8" disabled>
        3
      </Button>
      <Button variant="outline" size="sm" className="h-8 w-8" disabled>
        4
      </Button>
      <Button variant="outline" size="sm" className="h-8 w-8" disabled>
        5
      </Button>
      <Button variant="outline" size="icon" disabled>
        <ChevronRight className="h-4 w-4" />
        <span className="sr-only">Next page</span>
      </Button>
    </div>
  )
}

