"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import {
  Package,
  Heart,
  MapPin,
  ChevronRight,
  ShoppingBag,
  Edit,
  Calendar,
  Bell,
  TrendingUp,
  Eye,
  ArrowUpRight,
  Clock,
  CheckCircle2,
  AlertCircle,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Tabs, TabsContent } from "@/components/ui/tabs"
import { AccountNav } from "@/components/account-nav"
import { AccountOrders } from "@/components/account-orders"
import { AccountProfile } from "@/components/account-profile"
import { AccountAddresses } from "@/components/account-addresses"
import { AccountWishlist } from "@/components/account-wishlist"
import { AccountPayments } from "@/components/account-payments"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Skeleton } from "@/components/ui/skeleton"

export default function AccountPage() {
  const [activeTab, setActiveTab] = useState("dashboard")
  const [loading, setLoading] = useState(true)
  const [progress, setProgress] = useState(0)

  // Simulate loading state
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false)
    }, 1000)
    return () => clearTimeout(timer)
  }, [])

  // Simulate progress animation
  useEffect(() => {
    const timer = setTimeout(() => {
      setProgress(75)
    }, 500)
    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="flex flex-col min-h-screen bg-muted/30">
      <main className="flex-1">
        <div className="container py-8">
          {loading ? (
            <DashboardSkeleton />
          ) : (
            <div className="flex flex-col md:flex-row gap-8">
              <div className="md:w-1/4">
                <AccountNav activeTab={activeTab} setActiveTab={setActiveTab} />
              </div>
              <div className="md:w-3/4">
                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                  <TabsContent value="dashboard" className="mt-0 space-y-6">
                    {/* Welcome Banner */}
                    <Card className="overflow-hidden border-0 shadow-sm bg-gradient-to-r from-primary/10 to-primary/5">
                      <CardContent className="p-6 md:p-8">
                        <div className="flex flex-col md:flex-row md:items-center gap-4 md:gap-6">
                          <Avatar className="h-16 w-16 border-4 border-white shadow-md">
                            <AvatarImage src="/placeholder.svg?height=64&width=64" alt="John Doe" />
                            <AvatarFallback>JD</AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <h1 className="text-2xl font-bold">Welcome back, John!</h1>
                            <p className="text-muted-foreground mt-1">
                              Here's what's happening with your account today.
                            </p>
                          </div>
                          <Button variant="outline" size="sm" className="md:self-start gap-1 bg-white">
                            <Edit className="h-4 w-4" />
                            Edit Profile
                          </Button>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Stats Overview */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Card className="overflow-hidden border shadow-sm hover:shadow-md transition-shadow">
                        <CardContent className="p-6 flex flex-col items-center text-center">
                          <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-3">
                            <Package className="h-6 w-6 text-primary" />
                          </div>
                          <h3 className="font-medium text-lg">Orders</h3>
                          <p className="text-3xl font-bold mt-1">5</p>
                          <p className="text-sm text-muted-foreground mt-1">2 pending delivery</p>
                          <Button variant="ghost" size="sm" className="mt-3 gap-1" asChild>
                            <Link href="/account/orders">
                              View All
                              <ChevronRight className="h-4 w-4" />
                            </Link>
                          </Button>
                        </CardContent>
                      </Card>

                      <Card className="overflow-hidden border shadow-sm hover:shadow-md transition-shadow">
                        <CardContent className="p-6 flex flex-col items-center text-center">
                          <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-3">
                            <Heart className="h-6 w-6 text-primary" />
                          </div>
                          <h3 className="font-medium text-lg">Wishlist</h3>
                          <p className="text-3xl font-bold mt-1">12</p>
                          <p className="text-sm text-muted-foreground mt-1">3 items on sale</p>
                          <Button variant="ghost" size="sm" className="mt-3 gap-1" asChild>
                            <Link href="/account/wishlist">
                              View All
                              <ChevronRight className="h-4 w-4" />
                            </Link>
                          </Button>
                        </CardContent>
                      </Card>

                      <Card className="overflow-hidden border shadow-sm hover:shadow-md transition-shadow">
                        <CardContent className="p-6 flex flex-col items-center text-center">
                          <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-3">
                            <MapPin className="h-6 w-6 text-primary" />
                          </div>
                          <h3 className="font-medium text-lg">Addresses</h3>
                          <p className="text-3xl font-bold mt-1">2</p>
                          <p className="text-sm text-muted-foreground mt-1">Home & Office</p>
                          <Button variant="ghost" size="sm" className="mt-3 gap-1" asChild>
                            <Link href="/account/addresses">
                              Manage
                              <ChevronRight className="h-4 w-4" />
                            </Link>
                          </Button>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Loyalty Program */}
                    <Card className="overflow-hidden border shadow-sm">
                      <CardHeader className="pb-3">
                        <CardTitle className="flex items-center gap-2">
                          <TrendingUp className="h-5 w-5 text-primary" />
                          Loyalty Program
                        </CardTitle>
                        <CardDescription>You're making great progress toward your next reward!</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="flex justify-between items-center text-sm">
                            <span>
                              Current Points: <strong>750</strong>
                            </span>
                            <span>
                              Next Reward: <strong>1,000 points</strong>
                            </span>
                          </div>
                          <Progress value={progress} className="h-2" />
                          <div className="flex justify-between text-xs text-muted-foreground">
                            <span>0</span>
                            <span>1,000</span>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter className="bg-muted/50 pt-3 pb-3">
                        <div className="flex justify-between items-center w-full">
                          <span className="text-sm font-medium">Silver Member</span>
                          <Button variant="outline" size="sm" className="gap-1">
                            View Benefits
                            <ArrowUpRight className="h-3.5 w-3.5" />
                          </Button>
                        </div>
                      </CardFooter>
                    </Card>

                    {/* Recent Orders */}
                    <Card className="overflow-hidden border shadow-sm">
                      <CardHeader className="pb-3">
                        <div className="flex justify-between items-center">
                          <CardTitle className="flex items-center gap-2">
                            <ShoppingBag className="h-5 w-5 text-primary" />
                            Recent Orders
                          </CardTitle>
                          <Button variant="ghost" size="sm" className="gap-1" asChild>
                            <Link href="/account/orders">
                              View All
                              <ChevronRight className="h-4 w-4" />
                            </Link>
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent className="p-0">
                        <div className="divide-y">
                          {[
                            {
                              id: "STX12345",
                              date: "April 2, 2025",
                              total: "450,000",
                              status: "Delivered",
                              items: 3,
                              statusColor: "bg-green-100 text-green-800",
                            },
                            {
                              id: "STX12346",
                              date: "March 28, 2025",
                              total: "140,000",
                              status: "Processing",
                              items: 1,
                              statusColor: "bg-blue-100 text-blue-800",
                            },
                          ].map((order) => (
                            <div key={order.id} className="p-4 hover:bg-muted/50 transition-colors">
                              <div className="flex justify-between items-start mb-2">
                                <div>
                                  <div className="flex items-center gap-2">
                                    <span className="font-medium">Order #{order.id}</span>
                                    <Badge variant="outline" className={order.statusColor}>
                                      {order.status}
                                    </Badge>
                                  </div>
                                  <div className="text-sm text-muted-foreground mt-1 flex items-center gap-2">
                                    <Calendar className="h-3.5 w-3.5" />
                                    <span>{order.date}</span>
                                    <span className="inline-block w-1 h-1 rounded-full bg-muted-foreground"></span>
                                    <span>
                                      {order.items} item{order.items !== 1 ? "s" : ""}
                                    </span>
                                  </div>
                                </div>
                                <div className="text-right">
                                  <div className="font-medium">UGX {order.total}</div>
                                  <div className="flex gap-2 mt-1">
                                    <Button variant="ghost" size="sm" className="h-8 px-2" asChild>
                                      <Link href={`/account/orders/${order.id}`}>
                                        <Eye className="h-3.5 w-3.5 mr-1" />
                                        Details
                                      </Link>
                                    </Button>
                                    {order.status === "Delivered" && (
                                      <Button variant="ghost" size="sm" className="h-8 px-2">
                                        <ArrowUpRight className="h-3.5 w-3.5 mr-1" />
                                        Review
                                      </Button>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>

                    {/* Activity & Notifications */}
                    <Card className="overflow-hidden border shadow-sm">
                      <CardHeader className="pb-3">
                        <div className="flex justify-between items-center">
                          <CardTitle className="flex items-center gap-2">
                            <Bell className="h-5 w-5 text-primary" />
                            Recent Activity
                          </CardTitle>
                          <Button variant="ghost" size="sm">
                            Mark All as Read
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent className="p-0">
                        <div className="divide-y">
                          {[
                            {
                              id: 1,
                              title: "Your order #STX12345 has been delivered",
                              time: "2 days ago",
                              icon: <CheckCircle2 className="h-5 w-5 text-green-600" />,
                              read: true,
                            },
                            {
                              id: 2,
                              title: "Price drop on an item in your wishlist",
                              time: "3 days ago",
                              icon: <TrendingUp className="h-5 w-5 text-blue-600" />,
                              read: false,
                            },
                            {
                              id: 3,
                              title: "Your payment method will expire soon",
                              time: "5 days ago",
                              icon: <AlertCircle className="h-5 w-5 text-amber-600" />,
                              read: false,
                            },
                          ].map((activity) => (
                            <div
                              key={activity.id}
                              className={`p-4 hover:bg-muted/50 transition-colors flex items-start gap-3 ${
                                !activity.read ? "bg-primary/5" : ""
                              }`}
                            >
                              <div className="mt-0.5">{activity.icon}</div>
                              <div className="flex-1">
                                <p className={`${!activity.read ? "font-medium" : ""}`}>{activity.title}</p>
                                <div className="flex items-center gap-2 mt-1 text-sm text-muted-foreground">
                                  <Clock className="h-3.5 w-3.5" />
                                  <span>{activity.time}</span>
                                </div>
                              </div>
                              {!activity.read && <Badge className="bg-primary h-2 w-2 rounded-full p-0" />}
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>

                    {/* Recently Viewed */}
                    <Card className="overflow-hidden border shadow-sm">
                      <CardHeader className="pb-3">
                        <CardTitle className="flex items-center gap-2">
                          <Eye className="h-5 w-5 text-primary" />
                          Recently Viewed
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                          {[1, 2, 3].map((item) => (
                            <Link href={`/products/product-${item}`} key={item} className="group block">
                              <div className="aspect-square bg-muted rounded-md overflow-hidden mb-2">
                                <Image
                                  src={`/placeholder.svg?height=200&width=200&text=Product ${item}`}
                                  alt={`Product ${item}`}
                                  width={200}
                                  height={200}
                                  className="w-full h-full object-cover transition-transform group-hover:scale-105"
                                />
                              </div>
                              <h4 className="font-medium text-sm line-clamp-1 group-hover:text-primary transition-colors">
                                Product Name Example {item}
                              </h4>
                              <p className="text-sm text-muted-foreground">UGX 120,000</p>
                            </Link>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  <TabsContent value="orders" className="mt-0">
                    <AccountOrders />
                  </TabsContent>

                  <TabsContent value="wishlist" className="mt-0">
                    <AccountWishlist />
                  </TabsContent>

                  <TabsContent value="profile" className="mt-0">
                    <AccountProfile />
                  </TabsContent>

                  <TabsContent value="addresses" className="mt-0">
                    <AccountAddresses />
                  </TabsContent>

                  <TabsContent value="payments" className="mt-0">
                    <AccountPayments />
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}

function DashboardSkeleton() {
  return (
    <div className="flex flex-col md:flex-row gap-8">
      <div className="md:w-1/4">
        <Skeleton className="h-[400px] w-full rounded-lg" />
      </div>
      <div className="md:w-3/4 space-y-6">
        <Skeleton className="h-[120px] w-full rounded-lg" />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Skeleton className="h-[180px] w-full rounded-lg" />
          <Skeleton className="h-[180px] w-full rounded-lg" />
          <Skeleton className="h-[180px] w-full rounded-lg" />
        </div>
        <Skeleton className="h-[200px] w-full rounded-lg" />
        <Skeleton className="h-[300px] w-full rounded-lg" />
      </div>
    </div>
  )
}

