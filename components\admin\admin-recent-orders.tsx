import Link from "next/link"
import { ChevronRight, ExternalLink } from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

// Mock data for recent orders
const recentOrders = [
  {
    id: "ORD-12345",
    customer: "<PERSON>",
    date: "2025-04-04T10:30:00",
    total: 450000,
    status: "Delivered",
    items: 3,
  },
  {
    id: "ORD-12346",
    customer: "<PERSON>",
    date: "2025-04-04T09:15:00",
    total: 140000,
    status: "Processing",
    items: 1,
  },
  {
    id: "ORD-12347",
    customer: "<PERSON>",
    date: "2025-04-04T08:45:00",
    total: 635000,
    status: "Pending",
    items: 4,
  },
  {
    id: "ORD-12348",
    customer: "<PERSON>",
    date: "2025-04-03T16:20:00",
    total: 275000,
    status: "Shipped",
    items: 2,
  },
  {
    id: "ORD-12349",
    customer: "<PERSON>",
    date: "2025-04-03T14:10:00",
    total: 520000,
    status: "Delivered",
    items: 3,
  },
]

// Format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "UGX",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

// Format date
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return new Intl.DateTimeFormat("en-US", {
    month: "short",
    day: "numeric",
    hour: "numeric",
    minute: "numeric",
    hour12: true,
  }).format(date)
}

export function AdminRecentOrders() {
  return (
    <div className="space-y-4">
      {recentOrders.map((order) => (
        <div key={order.id} className="flex items-center justify-between border-b pb-4 last:border-b-0 last:pb-0">
          <div>
            <div className="flex items-center gap-2">
              <Link href={`/admin/orders/${order.id}`} className="font-medium hover:underline">
                {order.id}
              </Link>
              <Badge
                variant={
                  order.status === "Delivered"
                    ? "success"
                    : order.status === "Processing"
                      ? "default"
                      : order.status === "Shipped"
                        ? "warning"
                        : "secondary"
                }
                className={
                  order.status === "Delivered"
                    ? "bg-green-100 text-green-800 hover:bg-green-100"
                    : order.status === "Processing"
                      ? "bg-blue-100 text-blue-800 hover:bg-blue-100"
                      : order.status === "Shipped"
                        ? "bg-yellow-100 text-yellow-800 hover:bg-yellow-100"
                        : "bg-gray-100 text-gray-800 hover:bg-gray-100"
                }
              >
                {order.status}
              </Badge>
            </div>
            <div className="text-sm text-muted-foreground mt-1">
              {order.customer} • {formatDate(order.date)}
            </div>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-right">
              <div className="font-medium">{formatCurrency(order.total)}</div>
              <div className="text-xs text-muted-foreground">{order.items} items</div>
            </div>
            <Button variant="ghost" size="icon" className="h-8 w-8" asChild>
              <Link href={`/admin/orders/${order.id}`}>
                <ChevronRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>
      ))}
      <div className="text-center pt-2">
        <Button variant="outline" size="sm" asChild>
          <Link href="/admin/orders" className="gap-1">
            View All Orders
            <ExternalLink className="h-3.5 w-3.5" />
          </Link>
        </Button>
      </div>
    </div>
  )
}

