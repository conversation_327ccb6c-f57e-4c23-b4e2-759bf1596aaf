import { DollarSign, Package, ShoppingCart, Users } from "lucide-react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { AdminMetricCard } from "@/components/admin/admin-metric-card"
import { AdminRecentOrders } from "@/components/admin/admin-recent-orders"
import { AdminSalesChart } from "@/components/admin/admin-sales-chart"
import { AdminTopProducts } from "@/components/admin/admin-top-products"
import { AdminCustomerActivity } from "@/components/admin/admin-customer-activity"

export default function AdminDashboard() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <div className="flex items-center gap-2">
          <Tabs defaultValue="today" className="w-[400px]">
            <TabsList>
              <TabsTrigger value="today">Today</TabsTrigger>
              <TabsTrigger value="week">This Week</TabsTrigger>
              <TabsTrigger value="month">This Month</TabsTrigger>
              <TabsTrigger value="year">This Year</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <AdminMetricCard
          title="Total Revenue"
          value="UGX 45,231,890"
          description="+20.1% from last month"
          icon={<DollarSign className="h-4 w-4 text-muted-foreground" />}
          trend="up"
        />
        <AdminMetricCard
          title="Orders"
          value="1,234"
          description="+12.5% from last month"
          icon={<ShoppingCart className="h-4 w-4 text-muted-foreground" />}
          trend="up"
        />
        <AdminMetricCard
          title="Customers"
          value="3,456"
          description="+8.2% from last month"
          icon={<Users className="h-4 w-4 text-muted-foreground" />}
          trend="up"
        />
        <AdminMetricCard
          title="Products Sold"
          value="5,678"
          description="-2.5% from last month"
          icon={<Package className="h-4 w-4 text-muted-foreground" />}
          trend="down"
        />
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Sales Overview</CardTitle>
            <CardDescription>Daily sales revenue for the current month</CardDescription>
          </CardHeader>
          <CardContent className="pl-2">
            <AdminSalesChart />
          </CardContent>
        </Card>
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Top Selling Products</CardTitle>
            <CardDescription>Products with the highest sales this month</CardDescription>
          </CardHeader>
          <CardContent>
            <AdminTopProducts />
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Recent Orders</CardTitle>
            <CardDescription>Latest customer orders and their status</CardDescription>
          </CardHeader>
          <CardContent>
            <AdminRecentOrders />
          </CardContent>
        </Card>
        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Customer Activity</CardTitle>
            <CardDescription>Recent customer registrations and purchases</CardDescription>
          </CardHeader>
          <CardContent>
            <AdminCustomerActivity />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

