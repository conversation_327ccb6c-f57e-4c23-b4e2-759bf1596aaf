<!DOCTYPE HTML>
<html lang="en-CA">
<head>
    <meta charset="UTF-8"/>
    <title>
        POWERADE® - Nutrition Facts &amp; Products | Coca-Cola CA
    </title>
    
    <meta name="description" content="Discover Powerade sports drinks and stay hydrated during your workout. Made with vitamins and minerals to help you perform at your best."/>
    <meta property="og:image" content="/content/dam/onexp/ca/en/brands/powerade/en-powerade-brands-logo.png"/>
    <meta property="og:image:alt" content="Powerade Canada logo"/>
    <meta name="template" content="brand-landing-page"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    

    <!-- css resources and design tokens have to be loaded firstly on the page due to the performance reason -->
    <style id="design-token" data-brand-expression="powerade-brand-theme">
        :root, .brand-colors { 
    --color-icon-action-main-default: #000000; 
    --color-icon-action-main-disabled: #7d7d87; 
    --color-icon-action-main-hover: #303039; 
    --color-icon-action-main-pressed: #54545c; 
    --color-icon-action-on_brand-primary-default: #000000; 
    --color-icon-action-on_brand-primary-disabled: #7d7d87; 
    --color-icon-action-on_brand-primary-hover: #303039; 
    --color-icon-action-on_brand-primary-pressed: #54545c; 
    --color-icon-action-on_brand-secondary-default: #ffffff; 
    --color-icon-action-on_brand-secondary-disabled: #b0b0bd; 
    --color-icon-action-on_brand-secondary-hover: #e7e7ef; 
    --color-icon-action-on_brand-secondary-pressed: #c8c8d2; 
    --color-icon-brand-primary-regular: #0f8cf0; 
    --color-icon-brand-secondary-regular: #000000; 
    --color-icon-component-footer-action-default: #ffffff; 
    --color-icon-component-footer-action-hover: #d5d5d5; 
    --color-icon-component-footer-action-pressed: #a5a5a5; 
    --color-icon-component-footer-neutral-regular: #ffffff; 
    --color-icon-component-input-critical: #bf1004; 
    --color-icon-component-input-default: #000000; 
    --color-icon-component-input-positive: #1d6e17; 
    --color-icon-component-nav-action-default: #000000; 
    --color-icon-component-nav-action-disabled: #8b8b8b; 
    --color-icon-component-nav-action-hover: #6c6c6c; 
    --color-icon-component-nav-action-pressed: #a5a5a5; 
    --color-icon-component-nav-neutral-main-default: #000000; 
    --color-icon-component-nav-neutral-main-disabled: #8b8b8b; 
    --color-icon-component-nav-neutral-on_button-default: #ffffff; 
    --color-icon-component-nav-neutral-on_button-disabled: #8b8b8b; 
    --color-icon-neutral-main-regular-default: #000000; 
    --color-icon-neutral-main-regular-disabled: #7d7d87; 
    --color-icon-neutral-main-subtle-default: #54545c; 
    --color-icon-neutral-on_brand-primary-default: #000000; 
    --color-icon-neutral-on_brand-primary-disabled: #404040; 
    --color-icon-neutral-on_brand-secondary-default: #ffffff; 
    --color-icon-neutral-on_brand-secondary-disabled: #b0b0bd; 
    --color-icon-neutral-on_button-neutral-default: #000000; 
    --color-icon-neutral-on_button-neutral-disabled: #7d7d87; 
    --color-icon-neutral-on_button-primary-default: #ffffff; 
    --color-icon-neutral-on_button-primary-disabled: #7d7d87; 
    --color-icon-neutral-on_button-secondary-default: #000000; 
    --color-icon-neutral-on_button-secondary-disabled: #b0b0bd; 
    --color-icon-status-critical: #bf1004; 
    --color-icon-status-info: #0048ff; 
    --color-icon-status-positive: #1d6e17; 
    --color-icon-status-warning: #725d27; 
    --color-shadow-interactive-hover: #00000052; 
    --color-shadow-neutral-regular: #00000014; 
    --color-stroke-action-default: #000000; 
    --color-stroke-brand-primary-default: #0f8cf0; 
    --color-stroke-brand-primary-subtle: #9ebef8; 
    --color-stroke-brand-secondary-default: #000000; 
    --color-stroke-brand-secondary-subtle: #54545c; 
    --color-stroke-component-footer-interactive-focus: #ffffff; 
    --color-stroke-component-footer-neutral-regular: #ffffff; 
    --color-stroke-component-footer-neutral-subtle: #d5d5d5; 
    --color-stroke-component-input-field-active: #000000; 
    --color-stroke-component-input-field-critical: #bf1004; 
    --color-stroke-component-input-field-default: #000000; 
    --color-stroke-component-input-field-hover: #54545c; 
    --color-stroke-component-input-field-positive: #1d6e17; 
    --color-stroke-component-input-tag-subtle: #c8c8d2; 
    --color-stroke-component-nav-interactive-active: #000000; 
    --color-stroke-component-nav-interactive-default: #000000; 
    --color-stroke-component-nav-interactive-focus: #000000; 
    --color-stroke-component-nav-interactive-hover: #6c6c6c; 
    --color-stroke-component-nav-neutral-main-regular: #000000; 
    --color-stroke-component-nav-neutral-main-subtle: #d5d5d5; 
    --color-stroke-component-nav-neutral-on_button-default: #000000; 
    --color-stroke-component-nav-neutral-on_button-disabled: #8b8b8b; 
    --color-stroke-interactive-main-active: #000000; 
    --color-stroke-interactive-main-default: #000000; 
    --color-stroke-interactive-main-disabled: #b0b0bd; 
    --color-stroke-interactive-main-focus: #000000; 
    --color-stroke-interactive-main-hover: #54545c; 
    --color-stroke-interactive-main-pressed: #9a9aa6; 
    --color-stroke-interactive-on_brand-primary-focus: #000000; 
    --color-stroke-interactive-on_brand-secondary-focus: #ffffff; 
    --color-stroke-neutral-main-regular-default: #000000; 
    --color-stroke-neutral-main-regular-disabled: #7d7d87; 
    --color-stroke-neutral-main-subtle-default: #c8c8d2; 
    --color-stroke-neutral-on_brand-primary-default: #000000; 
    --color-stroke-neutral-on_brand-primary-disabled: #404040; 
    --color-stroke-neutral-on_brand-secondary-default: #ffffff; 
    --color-stroke-neutral-on_brand-secondary-disabled: #b0b0bd; 
    --color-stroke-neutral-on_button-neutral-default: #000000; 
    --color-stroke-neutral-on_button-neutral-disabled: #7d7d87; 
    --color-stroke-neutral-on_button-primary-default: #000000; 
    --color-stroke-neutral-on_button-primary-disabled: #404040; 
    --color-stroke-neutral-on_button-secondary-default: #ffffff; 
    --color-stroke-neutral-on_button-secondary-disabled: #b0b0bd; 
    --color-stroke-status-critical: #bf1004; 
    --color-stroke-status-info: #0048ff; 
    --color-stroke-status-positive: #1d6e17; 
    --color-stroke-status-warning: #725d27; 
    --color-surface-action-alpha-main-active: #00000014; 
    --color-surface-action-alpha-main-default: #00000000; 
    --color-surface-action-alpha-main-hover: #00000014; 
    --color-surface-action-alpha-main-pressed: #00000029; 
    --color-surface-action-alpha-on_brand-primary-default: #00000000; 
    --color-surface-action-alpha-on_brand-primary-hover: #00000014; 
    --color-surface-action-alpha-on_brand-primary-pressed: #00000029; 
    --color-surface-action-alpha-on_brand-secondary-default: #ffffff00; 
    --color-surface-action-alpha-on_brand-secondary-hover: #ffffff14; 
    --color-surface-action-alpha-on_brand-secondary-pressed: #ffffff29; 
    --color-surface-action-opaque-main-default: #0f8cf0; 
    --color-surface-action-opaque-main-disabled: #b0b0bd; 
    --color-surface-action-opaque-main-hover: #9ebef8; 
    --color-surface-action-opaque-main-pressed: #e7eefd; 
    --color-surface-action-opaque-on_brand-primary-default: #000000; 
    --color-surface-action-opaque-on_brand-primary-disabled: #b0b0bd; 
    --color-surface-action-opaque-on_brand-primary-hover: #303039; 
    --color-surface-action-opaque-on_brand-primary-pressed: #54545c; 
    --color-surface-action-opaque-on_brand-secondary-default: #0f8cf0; 
    --color-surface-action-opaque-on_brand-secondary-disabled: #7d7d87; 
    --color-surface-action-opaque-on_brand-secondary-hover: #9ebef8; 
    --color-surface-action-opaque-on_brand-secondary-pressed: #e7eefd; 
    --color-surface-brand-primary-regular: #0f8cf0; 
    --color-surface-brand-secondary-regular: #000000; 
    --color-surface-component-Input-dropdown-hover: #00000014; 
    --color-surface-component-Input-dropdown-pressed: #00000029; 
    --color-surface-component-Input-field-regular: #ffffff; 
    --color-surface-component-footer-regular: #000000; 
    --color-surface-component-footer-action-default: #ffffff00; 
    --color-surface-component-footer-action-hover: #ffffff1f; 
    --color-surface-component-footer-action-pressed: #ffffff3d; 
    --color-surface-component-footer-neutral-regular: #000000; 
    --color-surface-component-footer-neutral-subtle: #353535; 
    --color-surface-component-live_badge-regular: #ff0000; 
    --color-surface-component-nav-action-alpha-default: #00000000; 
    --color-surface-component-nav-action-alpha-hover: #0000001f; 
    --color-surface-component-nav-action-alpha-pressed: #0000003d; 
    --color-surface-component-nav-action-opaque-default: #000000; 
    --color-surface-component-nav-action-opaque-disabled: #bdbdbd; 
    --color-surface-component-nav-action-opaque-hover: #6c6c6c; 
    --color-surface-component-nav-action-opaque-pressed: #a5a5a5; 
    --color-surface-component-nav-interactive-hover: #0000001f; 
    --color-surface-component-nav-interactive-pressed: #0000003d; 
    --color-surface-component-nav-neutral-alpha-regular: #0000008f; 
    --color-surface-component-nav-neutral-opaque-regular: #ffffff; 
    --color-surface-component-nav-neutral-opaque-subtle: #eeeeee; 
    --color-surface-interactive-alpha-default: #00000000; 
    --color-surface-interactive-alpha-hover: #00000014; 
    --color-surface-interactive-alpha-pressed: #00000029; 
    --color-surface-interactive-opaque-active: #0f8cf0; 
    --color-surface-interactive-opaque-default: #0f8cf0; 
    --color-surface-interactive-opaque-disabled: #b0b0bd; 
    --color-surface-interactive-opaque-hover: #2161a3; 
    --color-surface-interactive-opaque-pressed: #1c314e; 
    --color-surface-neutral-alpha-regular: #0000008f; 
    --color-surface-neutral-opaque-bold: #b0b0bd; 
    --color-surface-neutral-opaque-disabled: #9a9aa6; 
    --color-surface-neutral-opaque-regular: #ffffff; 
    --color-surface-neutral-opaque-subtle: #c8c8d2; 
    --color-surface-status-critical: #ffe9e2; 
    --color-surface-status-info: #f2eaff; 
    --color-surface-status-warning: #fceccf; 
    --color-surface-status-positive-bold: #e4f2df; 
    --color-surface-status-positive-subtle: #95cb87; 
    --color-text-action-main-default: #000000; 
    --color-text-action-main-disabled: #7d7d87; 
    --color-text-action-main-hover: #303039; 
    --color-text-action-main-pressed: #54545c; 
    --color-text-action-on_brand-primary-default: #000000; 
    --color-text-action-on_brand-primary-disabled: #7d7d87; 
    --color-text-action-on_brand-primary-hover: #303039; 
    --color-text-action-on_brand-primary-pressed: #54545c; 
    --color-text-action-on_brand-secondary-default: #ffffff; 
    --color-text-action-on_brand-secondary-disabled: #b0b0bd; 
    --color-text-action-on_brand-secondary-hover: #e7e7ef; 
    --color-text-action-on_brand-secondary-pressed: #c8c8d2; 
    --color-text-component-footer-action-default: #ffffff; 
    --color-text-component-footer-action-hover: #d5d5d5; 
    --color-text-component-footer-action-pressed: #a5a5a5; 
    --color-text-component-footer-neutral-regular: #ffffff; 
    --color-text-component-footer-neutral-subtle: #d5d5d5; 
    --color-text-component-input-default: #000000; 
    --color-text-component-input-placeholder: #7d7d87; 
    --color-text-component-nav-action-default: #000000; 
    --color-text-component-nav-action-disabled: #8b8b8b; 
    --color-text-component-nav-action-hover: #6c6c6c; 
    --color-text-component-nav-action-pressed: #a5a5a5; 
    --color-text-component-nav-neutral-main-default: #000000; 
    --color-text-component-nav-neutral-main-disabled: #8b8b8b; 
    --color-text-component-nav-neutral-main-placeholder: #8b8b8b; 
    --color-text-component-nav-neutral-on_button-default: #ffffff; 
    --color-text-component-nav-neutral-on_button-disabled: #8b8b8b; 
    --color-text-component-nav-status-critical: #bf1004; 
    --color-text-interactive-active: #000000; 
    --color-text-neutral-main-regular-default: #000000; 
    --color-text-neutral-main-regular-disabled: #7d7d87; 
    --color-text-neutral-main-regular-placeholder: #7d7d87; 
    --color-text-neutral-main-subtle-default: #54545c; 
    --color-text-neutral-on_brand-primary-default: #000000; 
    --color-text-neutral-on_brand-primary-disabled: #404040; 
    --color-text-neutral-on_brand-secondary-default: #ffffff; 
    --color-text-neutral-on_brand-secondary-disabled: #b0b0bd; 
    --color-text-neutral-on_button-neutral-default: #000000; 
    --color-text-neutral-on_button-neutral-disabled: #7d7d87; 
    --color-text-neutral-on_button-primary-default: #ffffff; 
    --color-text-neutral-on_button-primary-disabled: #7d7d87; 
    --color-text-neutral-on_button-secondary-default: #000000; 
    --color-text-neutral-on_button-secondary-disabled: #b0b0bd; 
    --color-text-neutral-on_image-default: #ffffff; 
    --color-text-status-critical: #bf1004; 
    --color-text-status-info: #0048ff; 
    --color-text-status-positive: #1d6e17; 
    --color-text-status-warning: #725d27; 
    --typography-font-family-brand-action: Degular; 
    --typography-font-family-brand-body: Degular; 
    --typography-font-family-brand-heading: Degular; 
    --typography-font-weight-action-default: normal; 
    --typography-font-weight-action-heavy: bold; 
    --typography-font-weight-body-default: normal; 
    --typography-font-weight-body-heavy: bold; 
    --typography-font-weight-heading-default: bold; 
    --variables-brand: Powerade; 
    --dimension-radius-component-button: 0px; 
    --dimension-radius-component-card: 0px; 
    --dimension-radius-component-image: 0px; 
    --dimension-radius-component-modal: 0px; 
    --dimension-radius-generic-min: 0px; 
    --dimension-radius-generic-s: 4px; 
    --dimension-radius-generic-m: 8px; 
    --dimension-radius-generic-l: 12px; 
    --dimension-radius-generic-xl: 16px; 
    --dimension-radius-generic-max: 8000px; 
    --dimension-radius-universal-button: 8000px; 
    --dimension-shadow-blur-none: 0px; 
    --dimension-shadow-blur-m: 16px; 
    --dimension-shadow-blur-l: 24px; 
    --dimension-shadow-position-none: 0px; 
    --dimension-shadow-position-s: 8px; 
    --dimension-shadow-position-m: 12px; 
    --dimension-shadow-spread-none: 0px; 
    --dimension-shadow-spread-s: 2px; 
    --dimension-size-2xs: 8px; 
    --dimension-size-xs: 16px; 
    --dimension-size-s: 24px; 
    --dimension-size-m: 32px; 
    --dimension-size-l: 40px; 
    --dimension-size-xl: 48px; 
    --dimension-size-2xl: 56px; 
    --dimension-size-3xl: 64px; 
    --dimension-size-4xl: 72px; 
    --dimension-size-5xl: 80px; 
    --dimension-spacing-min: 0px; 
    --dimension-spacing-3xs: 2px; 
    --dimension-spacing-2xs: 4px; 
    --dimension-spacing-xs: 8px; 
    --dimension-spacing-s: 12px; 
    --dimension-spacing-m: 16px; 
    --dimension-spacing-l: 20px; 
    --dimension-spacing-xl: 24px; 
    --dimension-spacing-2xl: 32px; 
    --dimension-spacing-3xl: 40px; 
    --dimension-spacing-4xl: 64px; 
    --dimension-spacing-5xl: 80px; 
    --dimension-spacing-6xl: 120px; 
    --dimension-stroke_weight-s: 1px; 
    --dimension-stroke_weight-m: 2px; 
    --dimension-stroke_weight-l: 4px; 
    --dimension-stroke_weight-xl: 5px; 
    --dimension-stroke_weight-2xl: 8px; 
    --typography-letter_spacing-action: 0px; 
    --typography-letter_spacing-body: 0px; 
    --typography-letter_spacing-heading: 0px; 
    --typography-paragraph_spacing-action-s: 0px; 
    --typography-paragraph_spacing-body-s: 4px; 
    --typography-paragraph_spacing-body-m: 8px; 
    --typography-paragraph_spacing-heading-s: 0px; 
    --mobile-dimension-layout-gutter: 16px; 
    --mobile-dimension-layout-margin: 24px; 
    --mobile-dimension-layout-viewport-width: 375px; 
    --mobile-dimension-layout-viewport-height: 812px; 
    --mobile-dimension-radius-component-full_width_card: 0px; 
    --mobile-dimension-radius-component-lower_modal: 0px; 
    --mobile-dimension-radius-component-side_panel: 16px; 
    --mobile-dimension-size-height-component-hero_cropped-default: 281px; 
    --mobile-dimension-size-width-column-2: 155px; 
    --mobile-dimension-size-width-column-12: 327px; 
    --mobile-dimension-size-width-column-4: 327px; 
    --mobile-dimension-size-width-column-5: 327px; 
    --mobile-dimension-size-width-column-6: 327px; 
    --mobile-dimension-size-width-column-8: 327px; 
    --mobile-dimension-size-width-column-expanded: 327px; 
    --mobile-dimension-size-width-column-max: 1152px; 
    --mobile-dimension-size-width-component-brand_card-default: 155px; 
    --mobile-dimension-size-width-component-feed_card-max: 375px; 
    --mobile-dimension-size-width-component-horizontal_card-content-max: 280px; 
    --mobile-dimension-size-width-component-horizontal_card-image-max: 327px; 
    --mobile-dimension-size-width-component-icons_list-min: 240px; 
    --mobile-dimension-size-width-documentation-component_example-max: 407px; 
    --mobile-dimension-spacing-component-side_panel: 280px; 
    --mobile-dimension-spacing-padding-card-l: 24px; 
    --mobile-dimension-spacing-padding-card-s: 24px; 
    --mobile-dimension-spacing-padding-modal-s: 24px; 
    --mobile-dimension-spacing-vertical-text_to_text: 8px; 
    --mobile-dimension-spacing-vertical-module_to_text: 32px; 
    --mobile-dimension-spacing-vertical-hero_to_module: 40px; 
    --mobile-dimension-spacing-vertical-text_to_module: 40px; 
    --mobile-dimension-spacing-vertical-module_to_module: 56px; 
    --mobile-typography-line_height-action-m: 24px; 
    --mobile-typography-line_height-body-s: 20px; 
    --mobile-typography-line_height-body-m: 24px; 
    --mobile-typography-line_height-heading-s: 28px; 
    --mobile-typography-line_height-heading-m: 32px; 
    --mobile-typography-line_height-heading-l: 36px; 
    --mobile-typography-line_height-heading-xl: 40px; 
    --mobile-typography-line_height-heading-xxl: 56px; 
    --mobile-typography-size-action-m: 16px; 
    --mobile-typography-size-body-s: 12px; 
    --mobile-typography-size-body-m: 16px; 
    --mobile-typography-size-heading-s: 20px; 
    --mobile-typography-size-heading-m: 24px; 
    --mobile-typography-size-heading-l: 28px; 
    --mobile-typography-size-heading-xl: 32px; 
    --mobile-typography-size-heading-xxl: 48px; 
    --mobile-variables-breakpoint-type-column_number: 4px; 
    --mobile-variables-breakpoint-type-layout_margin: 24px; 
    --desktop-dimension-layout-margin: 0px; 
    --desktop-dimension-layout-gutter: 24px; 
    --desktop-dimension-layout-viewport-height: 1024px; 
    --desktop-dimension-layout-viewport-width: 1440px; 
    --desktop-dimension-radius-component-side_panel: 0px; 
    --desktop-dimension-radius-component-full_width_card: 16px; 
    --desktop-dimension-radius-component-lower_modal: 16px; 
    --desktop-dimension-size-height-component-hero_cropped-default: 628px; 
    --desktop-dimension-size-width-column-2: 167px; 
    --desktop-dimension-size-width-column-4: 357px; 
    --desktop-dimension-size-width-column-5: 453px; 
    --desktop-dimension-size-width-column-6: 548px; 
    --desktop-dimension-size-width-column-8: 739px; 
    --desktop-dimension-size-width-column-12: 1120px; 
    --desktop-dimension-size-width-column-max: 1120px; 
    --desktop-dimension-size-width-column-expanded: 1280px; 
    --desktop-dimension-size-width-component-brand_card-default: 357px; 
    --desktop-dimension-size-width-component-feed_card-max: 544px; 
    --desktop-dimension-size-width-component-horizontal_card-content-max: 400px; 
    --desktop-dimension-size-width-component-horizontal_card-image-max: 167px; 
    --desktop-dimension-size-width-component-icons_list-min: 240px; 
    --desktop-dimension-size-width-documentation-component_example-max: 407px; 
    --desktop-dimension-spacing-component-side_panel: 0px; 
    --desktop-dimension-spacing-padding-card-s: 32px; 
    --desktop-dimension-spacing-padding-card-l: 64px; 
    --desktop-dimension-spacing-padding-modal-s: 32px; 
    --desktop-dimension-spacing-vertical-text_to_text: 8px; 
    --desktop-dimension-spacing-vertical-module_to_text: 32px; 
    --desktop-dimension-spacing-vertical-hero_to_module: 56px; 
    --desktop-dimension-spacing-vertical-text_to_module: 56px; 
    --desktop-dimension-spacing-vertical-module_to_module: 120px; 
    --desktop-typography-line_height-action-m: 24px; 
    --desktop-typography-line_height-body-s: 20px; 
    --desktop-typography-line_height-body-m: 24px; 
    --desktop-typography-line_height-heading-s: 32px; 
    --desktop-typography-line_height-heading-m: 36px; 
    --desktop-typography-line_height-heading-l: 40px; 
    --desktop-typography-line_height-heading-xl: 48px; 
    --desktop-typography-line_height-heading-xxl: 112px; 
    --desktop-typography-size-action-m: 16px; 
    --desktop-typography-size-body-s: 12px; 
    --desktop-typography-size-body-m: 16px; 
    --desktop-typography-size-heading-s: 24px; 
    --desktop-typography-size-heading-m: 28px; 
    --desktop-typography-size-heading-l: 32px; 
    --desktop-typography-size-heading-xl: 38px; 
    --desktop-typography-size-heading-xxl: 96px; 
    --desktop-variables-breakpoint-type-column_number: 12px; 
    --desktop-variables-breakpoint-type-layout_margin: 160px; 
    --inverse-color-icon-action-main-default: #ffffff; 
    --inverse-color-icon-action-main-disabled: #b0b0bd; 
    --inverse-color-icon-action-main-hover: #e7e7ef; 
    --inverse-color-icon-action-main-pressed: #c8c8d2; 
    --inverse-color-icon-action-on_brand-primary-default: #000000; 
    --inverse-color-icon-action-on_brand-primary-disabled: #7d7d87; 
    --inverse-color-icon-action-on_brand-primary-hover: #303039; 
    --inverse-color-icon-action-on_brand-primary-pressed: #54545c; 
    --inverse-color-icon-action-on_brand-secondary-default: #ffffff; 
    --inverse-color-icon-action-on_brand-secondary-disabled: #b0b0bd; 
    --inverse-color-icon-action-on_brand-secondary-hover: #e7e7ef; 
    --inverse-color-icon-action-on_brand-secondary-pressed: #c8c8d2; 
    --inverse-color-icon-brand-primary-regular: #0f8cf0; 
    --inverse-color-icon-brand-secondary-regular: #ffffff; 
    --inverse-color-icon-component-footer-action-default: #ffffff; 
    --inverse-color-icon-component-footer-action-hover: #d5d5d5; 
    --inverse-color-icon-component-footer-action-pressed: #a5a5a5; 
    --inverse-color-icon-component-footer-neutral-regular: #ffffff; 
    --inverse-color-icon-component-input-critical: #bf1004; 
    --inverse-color-icon-component-input-default: #000000; 
    --inverse-color-icon-component-input-positive: #1d6e17; 
    --inverse-color-icon-component-nav-action-default: #ffffff; 
    --inverse-color-icon-component-nav-action-disabled: #bdbdbd; 
    --inverse-color-icon-component-nav-action-hover: #d5d5d5; 
    --inverse-color-icon-component-nav-action-pressed: #a5a5a5; 
    --inverse-color-icon-component-nav-neutral-main-default: #ffffff; 
    --inverse-color-icon-component-nav-neutral-main-disabled: #bdbdbd; 
    --inverse-color-icon-component-nav-neutral-on_button-default: #000000; 
    --inverse-color-icon-component-nav-neutral-on_button-disabled: #bdbdbd; 
    --inverse-color-icon-neutral-main-regular-default: #ffffff; 
    --inverse-color-icon-neutral-main-regular-disabled: #b0b0bd; 
    --inverse-color-icon-neutral-main-subtle-default: #c8c8d2; 
    --inverse-color-icon-neutral-on_brand-primary-default: #000000; 
    --inverse-color-icon-neutral-on_brand-primary-disabled: #404040; 
    --inverse-color-icon-neutral-on_brand-secondary-default: #ffffff; 
    --inverse-color-icon-neutral-on_brand-secondary-disabled: #b0b0bd; 
    --inverse-color-icon-neutral-on_button-neutral-default: #000000; 
    --inverse-color-icon-neutral-on_button-neutral-disabled: #b0b0bd; 
    --inverse-color-icon-neutral-on_button-primary-default: #ffffff; 
    --inverse-color-icon-neutral-on_button-primary-disabled: #7d7d87; 
    --inverse-color-icon-neutral-on_button-secondary-default: #000000; 
    --inverse-color-icon-neutral-on_button-secondary-disabled: #b0b0bd; 
    --inverse-color-icon-status-critical: #ffa68f; 
    --inverse-color-icon-status-info: #c6b0ff; 
    --inverse-color-icon-status-positive: #95cb87; 
    --inverse-color-icon-status-warning: #e3b743; 
    --inverse-color-shadow-interactive-hover: #ffffff52; 
    --inverse-color-shadow-neutral-regular: #00000014; 
    --inverse-color-stroke-action-default: #ffffff; 
    --inverse-color-stroke-brand-primary-default: #0f8cf0; 
    --inverse-color-stroke-brand-primary-subtle: #9ebef8; 
    --inverse-color-stroke-brand-secondary-default: #000000; 
    --inverse-color-stroke-brand-secondary-subtle: #54545c; 
    --inverse-color-stroke-component-footer-interactive-focus: #ffffff; 
    --inverse-color-stroke-component-footer-neutral-regular: #ffffff; 
    --inverse-color-stroke-component-footer-neutral-subtle: #d5d5d5; 
    --inverse-color-stroke-component-input-field-active: #000000; 
    --inverse-color-stroke-component-input-field-critical: #ffa68f; 
    --inverse-color-stroke-component-input-field-default: #000000; 
    --inverse-color-stroke-component-input-field-hover: #54545c; 
    --inverse-color-stroke-component-input-field-positive: #95cb87; 
    --inverse-color-stroke-component-input-tag-subtle: #c8c8d2; 
    --inverse-color-stroke-component-nav-interactive-active: #ffffff; 
    --inverse-color-stroke-component-nav-interactive-default: #ffffff; 
    --inverse-color-stroke-component-nav-interactive-focus: #ffffff; 
    --inverse-color-stroke-component-nav-interactive-hover: #d5d5d5; 
    --inverse-color-stroke-component-nav-neutral-main-regular: #ffffff; 
    --inverse-color-stroke-component-nav-neutral-main-subtle: #717171; 
    --inverse-color-stroke-component-nav-neutral-on_button-default: #ffffff; 
    --inverse-color-stroke-component-nav-neutral-on_button-disabled: #bdbdbd; 
    --inverse-color-stroke-interactive-main-active: #ffffff; 
    --inverse-color-stroke-interactive-main-default: #ffffff; 
    --inverse-color-stroke-interactive-main-disabled: #7d7d87; 
    --inverse-color-stroke-interactive-main-focus: #ffffff; 
    --inverse-color-stroke-interactive-main-hover: #c8c8d2; 
    --inverse-color-stroke-interactive-main-pressed: #9a9aa6; 
    --inverse-color-stroke-interactive-on_brand-primary-focus: #000000; 
    --inverse-color-stroke-interactive-on_brand-secondary-focus: #ffffff; 
    --inverse-color-stroke-neutral-main-regular-default: #ffffff; 
    --inverse-color-stroke-neutral-main-regular-disabled: #b0b0bd; 
    --inverse-color-stroke-neutral-main-subtle-default: #54545c; 
    --inverse-color-stroke-neutral-on_brand-primary-default: #000000; 
    --inverse-color-stroke-neutral-on_brand-primary-disabled: #404040; 
    --inverse-color-stroke-neutral-on_brand-secondary-default: #ffffff; 
    --inverse-color-stroke-neutral-on_brand-secondary-disabled: #b0b0bd; 
    --inverse-color-stroke-neutral-on_button-neutral-default: #ffffff; 
    --inverse-color-stroke-neutral-on_button-neutral-disabled: #b0b0bd; 
    --inverse-color-stroke-neutral-on_button-primary-default: #000000; 
    --inverse-color-stroke-neutral-on_button-primary-disabled: #404040; 
    --inverse-color-stroke-neutral-on_button-secondary-default: #ffffff; 
    --inverse-color-stroke-neutral-on_button-secondary-disabled: #b0b0bd; 
    --inverse-color-stroke-status-critical: #ffa68f; 
    --inverse-color-stroke-status-info: #c6b0ff; 
    --inverse-color-stroke-status-positive: #95cb87; 
    --inverse-color-stroke-status-warning: #e3b743; 
    --inverse-color-surface-action-alpha-main-active: #ffffff14; 
    --inverse-color-surface-action-alpha-main-default: #ffffff00; 
    --inverse-color-surface-action-alpha-main-hover: #ffffff14; 
    --inverse-color-surface-action-alpha-main-pressed: #ffffff29; 
    --inverse-color-surface-action-alpha-on_brand-primary-default: #00000000; 
    --inverse-color-surface-action-alpha-on_brand-primary-hover: #00000014; 
    --inverse-color-surface-action-alpha-on_brand-primary-pressed: #00000029; 
    --inverse-color-surface-action-alpha-on_brand-secondary-default: #ffffff00; 
    --inverse-color-surface-action-alpha-on_brand-secondary-hover: #ffffff14; 
    --inverse-color-surface-action-alpha-on_brand-secondary-pressed: #ffffff29; 
    --inverse-color-surface-action-opaque-main-default: #0f8cf0; 
    --inverse-color-surface-action-opaque-main-disabled: #7d7d87; 
    --inverse-color-surface-action-opaque-main-hover: #9ebef8; 
    --inverse-color-surface-action-opaque-main-pressed: #e7eefd; 
    --inverse-color-surface-action-opaque-on_brand-primary-default: #000000; 
    --inverse-color-surface-action-opaque-on_brand-primary-disabled: #b0b0bd; 
    --inverse-color-surface-action-opaque-on_brand-primary-hover: #303039; 
    --inverse-color-surface-action-opaque-on_brand-primary-pressed: #54545c; 
    --inverse-color-surface-action-opaque-on_brand-secondary-default: #0f8cf0; 
    --inverse-color-surface-action-opaque-on_brand-secondary-disabled: #7d7d87; 
    --inverse-color-surface-action-opaque-on_brand-secondary-hover: #9ebef8; 
    --inverse-color-surface-action-opaque-on_brand-secondary-pressed: #e7eefd; 
    --inverse-color-surface-brand-primary-regular: #0f8cf0; 
    --inverse-color-surface-brand-secondary-regular: #000000; 
    --inverse-color-surface-component-Input-dropdown-hover: #00000014; 
    --inverse-color-surface-component-Input-dropdown-pressed: #00000029; 
    --inverse-color-surface-component-Input-field-regular: #ffffff; 
    --inverse-color-surface-component-footer-regular: #000000; 
    --inverse-color-surface-component-footer-action-default: #ffffff00; 
    --inverse-color-surface-component-footer-action-hover: #ffffff1f; 
    --inverse-color-surface-component-footer-action-pressed: #ffffff3d; 
    --inverse-color-surface-component-footer-neutral-regular: #000000; 
    --inverse-color-surface-component-footer-neutral-subtle: #353535; 
    --inverse-color-surface-component-live_badge-regular: #ff0000; 
    --inverse-color-surface-component-nav-action-alpha-default: #ffffff00; 
    --inverse-color-surface-component-nav-action-alpha-hover: #ffffff1f; 
    --inverse-color-surface-component-nav-action-alpha-pressed: #ffffff3d; 
    --inverse-color-surface-component-nav-action-opaque-default: #ffffff; 
    --inverse-color-surface-component-nav-action-opaque-disabled: #8b8b8b; 
    --inverse-color-surface-component-nav-action-opaque-hover: #d5d5d5; 
    --inverse-color-surface-component-nav-action-opaque-pressed: #a5a5a5; 
    --inverse-color-surface-component-nav-interactive-hover: #ffffff1f; 
    --inverse-color-surface-component-nav-interactive-pressed: #ffffff3d; 
    --inverse-color-surface-component-nav-neutral-alpha-regular: #0000008f; 
    --inverse-color-surface-component-nav-neutral-opaque-regular: #000000; 
    --inverse-color-surface-component-nav-neutral-opaque-subtle: #353535; 
    --inverse-color-surface-interactive-alpha-default: #ffffff00; 
    --inverse-color-surface-interactive-alpha-hover: #ffffff14; 
    --inverse-color-surface-interactive-alpha-pressed: #ffffff29; 
    --inverse-color-surface-interactive-opaque-active: #0f8cf0; 
    --inverse-color-surface-interactive-opaque-default: #0f8cf0; 
    --inverse-color-surface-interactive-opaque-disabled: #7d7d87; 
    --inverse-color-surface-interactive-opaque-hover: #2161a3; 
    --inverse-color-surface-interactive-opaque-pressed: #1c314e; 
    --inverse-color-surface-neutral-alpha-regular: #ffffff8f; 
    --inverse-color-surface-neutral-opaque-bold: #7d7d87; 
    --inverse-color-surface-neutral-opaque-disabled: #9a9aa6; 
    --inverse-color-surface-neutral-opaque-regular: #000000; 
    --inverse-color-surface-neutral-opaque-subtle: #54545c; 
    --inverse-color-surface-status-critical: #69180a; 
    --inverse-color-surface-status-info: #0029b6; 
    --inverse-color-surface-status-warning: #40341a; 
    --inverse-color-surface-status-positive-bold: #193e13; 
    --inverse-color-surface-status-positive-subtle: #1d6e17; 
    --inverse-color-text-action-main-default: #ffffff; 
    --inverse-color-text-action-main-disabled: #b0b0bd; 
    --inverse-color-text-action-main-hover: #e7e7ef; 
    --inverse-color-text-action-main-pressed: #c8c8d2; 
    --inverse-color-text-action-on_brand-primary-default: #000000; 
    --inverse-color-text-action-on_brand-primary-disabled: #7d7d87; 
    --inverse-color-text-action-on_brand-primary-hover: #303039; 
    --inverse-color-text-action-on_brand-primary-pressed: #54545c; 
    --inverse-color-text-action-on_brand-secondary-default: #ffffff; 
    --inverse-color-text-action-on_brand-secondary-disabled: #b0b0bd; 
    --inverse-color-text-action-on_brand-secondary-hover: #e7e7ef; 
    --inverse-color-text-action-on_brand-secondary-pressed: #c8c8d2; 
    --inverse-color-text-component-footer-action-default: #ffffff; 
    --inverse-color-text-component-footer-action-hover: #d5d5d5; 
    --inverse-color-text-component-footer-action-pressed: #a5a5a5; 
    --inverse-color-text-component-footer-neutral-regular: #ffffff; 
    --inverse-color-text-component-footer-neutral-subtle: #d5d5d5; 
    --inverse-color-text-component-input-default: #000000; 
    --inverse-color-text-component-input-placeholder: #7d7d87; 
    --inverse-color-text-component-nav-action-default: #ffffff; 
    --inverse-color-text-component-nav-action-disabled: #bdbdbd; 
    --inverse-color-text-component-nav-action-hover: #d5d5d5; 
    --inverse-color-text-component-nav-action-pressed: #a5a5a5; 
    --inverse-color-text-component-nav-neutral-main-default: #ffffff; 
    --inverse-color-text-component-nav-neutral-main-disabled: #bdbdbd; 
    --inverse-color-text-component-nav-neutral-main-placeholder: #bdbdbd; 
    --inverse-color-text-component-nav-neutral-on_button-default: #000000; 
    --inverse-color-text-component-nav-neutral-on_button-disabled: #bdbdbd; 
    --inverse-color-text-component-nav-status-critical: #ffa68f; 
    --inverse-color-text-interactive-active: #ffffff; 
    --inverse-color-text-neutral-main-regular-default: #ffffff; 
    --inverse-color-text-neutral-main-regular-disabled: #b0b0bd; 
    --inverse-color-text-neutral-main-regular-placeholder: #b0b0bd; 
    --inverse-color-text-neutral-main-subtle-default: #c8c8d2; 
    --inverse-color-text-neutral-on_brand-primary-default: #000000; 
    --inverse-color-text-neutral-on_brand-primary-disabled: #404040; 
    --inverse-color-text-neutral-on_brand-secondary-default: #ffffff; 
    --inverse-color-text-neutral-on_brand-secondary-disabled: #b0b0bd; 
    --inverse-color-text-neutral-on_button-neutral-default: #000000; 
    --inverse-color-text-neutral-on_button-neutral-disabled: #b0b0bd; 
    --inverse-color-text-neutral-on_button-primary-default: #ffffff; 
    --inverse-color-text-neutral-on_button-primary-disabled: #7d7d87; 
    --inverse-color-text-neutral-on_button-secondary-default: #000000; 
    --inverse-color-text-neutral-on_button-secondary-disabled: #b0b0bd; 
    --inverse-color-text-neutral-on_image-default: #ffffff; 
    --inverse-color-text-status-critical: #ffa68f; 
    --inverse-color-text-status-info: #c6b0ff; 
    --inverse-color-text-status-positive: #95cb87; 
    --inverse-color-text-status-warning: #e3b743; 
}
    </style>
    
        <script defer="defer" type="text/javascript" src="https://rum.hlx.page/.rum/@adobe/helix-rum-js@%5E2/dist/rum-standalone.js" data-routing="program=58902,environment=658605,tier=publish"></script>
<link as="style" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/theme.css" rel="preload stylesheet" type="text/css">
    
        
    

    <!-- OneTrust has to be loaded early due to the performance reason -->
    
        
            <!-- OneTrust Cookies Consent Notice start for coca-cola.com/ca/en -->

<script src="https://cdn.cookielaw.org/scripttemplates/otSDKStub.js" data-document-language="true" type="text/javascript" charset="UTF-8" data-domain-script="d43f1e72-3bfe-4627-b3f6-c74b0a7b46b7"></script>
<script type="text/javascript">
function OptanonWrapper() {
	        function e() {
	            var e = document.getElementById("accept-recommended-btn-handler");
	            document.getElementsByClassName("ot-btn-container")[0].prepend(e), n.classList.add("ot-modal-open");
	            var t = document.getElementById("close-pc-btn-handler"),
	                c = document.getElementById("accept-recommended-btn-handler"),
	                s = document.getElementsByClassName("save-preference-btn-handler")[0],
	                r = document.getElementsByClassName("ot-pc-refuse-all-handler")[0],
	                o = document.getElementsByClassName("ot-fade-in")[0];
	            t.addEventListener("click", function (e) {
	                n.classList.remove("ot-modal-open");
	            }),
	                c.addEventListener("click", function (e) {
	                    n.classList.remove("ot-modal-open");
	                }),
	                s.addEventListener("click", function (e) {
	                    n.classList.remove("ot-modal-open");
	                }),
	                r.addEventListener("click", function (e) {
	                    n.classList.remove("ot-modal-open");
	                }),
	                o.addEventListener("click", function (e) {
	                    t.click(), n.classList.remove("ot-modal-open");
	                });
	        }
	        var n = document.documentElement;
	        if (0 == Optanon.IsAlertBoxClosed()) Optanon.ToggleInfoDisplay(), e();
	        else {
	            var t = document.getElementsByClassName("ot-floating-button__open")[0];
	            (t.innerHTML =
	                '<svg version="1.1" id="Layer_1" xmlns= http://www.w3.org/2000/svg xmlns:xlink= http://www.w3.org/1999/xlink x="0px" y="0px" viewBox="0 0 32 32" style="enable-background:new 0 0 32 32;" xml:space="preserve"><g><path class="st0" d="M29.67,7.41c-0.51,0-0.92-0.41-0.92-0.91c0-0.5,0.41-0.91,0.92-0.91C30.18,5.6,30.6,6,30.6,6.5 C30.6,7.01,30.18,7.41,29.67,7.41 M25.59,11.16c0.83,0,1.5-0.66,1.5-1.48c0-0.82-0.67-1.48-1.5-1.48c-0.83,0-1.5,0.66-1.5,1.48 C24.09,10.5,24.76,11.16,25.59,11.16 M32,13.88c0-0.5-0.41-0.91-0.92-0.91c-0.51,0-0.92,0.41-0.92,0.91c0,0.5,0.41,0.91,0.92,0.91 C31.59,14.79,32,14.38,32,13.88 M15.55,31.97c8.26,0,15.01-6.34,15.52-14.34c-1.88-0.49-3.35-1.98-3.78-3.87 c-3.11-0.16-5.58-2.69-5.58-5.79c0-0.26,0.02-0.52,0.05-0.77c-2.07-0.76-3.55-2.73-3.55-5.03c0-0.2,0.01-0.4,0.03-0.6 c-0.88-0.15-1.78-0.23-2.71-0.23C6.96,1.34,0,8.2,0,16.65C0,25.11,6.96,31.97,15.55,31.97 M22.72-0.03c-1.01,0-1.83,0.81-1.83,1.8 s0.82,1.8,1.83,1.8c1.01,0,1.83-0.81,1.83-1.8S23.73-0.03,22.72-0.03"/><path class="st1" d="M7.06,17.17c1.02,0,1.85,0.81,1.85,1.82c0,1.01-0.83,1.82-1.85,1.82c-1.02,0-1.85-0.81-1.85-1.82 C5.21,17.98,6.04,17.17,7.06,17.17 M10.76,8.46c-1.02,0-1.85,0.81-1.85,1.82c0,1.01,0.83,1.82,1.85,1.82 c1.02,0,1.85-0.81,1.85-1.82C12.6,9.28,11.78,8.46,10.76,8.46 M14.45,24.52c0-1-0.83-1.82-1.85-1.82c-1.02,0-1.85,0.81-1.85,1.82 c0,1,0.83,1.82,1.85,1.82C13.62,26.34,14.45,25.53,14.45,24.52 M18.23,12.33c-1.02,0-1.85,0.81-1.85,1.82 c0,1.01,0.83,1.82,1.85,1.82c1.02,0,1.85-0.81,1.85-1.82C20.07,13.14,19.25,12.33,18.23,12.33 M23.75,20.36 c-1.02,0-1.85,0.81-1.85,1.82c0,1.01,0.83,1.82,1.85,1.82c1.02,0,1.85-0.81,1.85-1.82C25.59,21.18,24.77,20.36,23.75,20.36"/></g></svg>'),
	                t.addEventListener("click", function (n) {
	                    e();
	                });
	            var c = document.getElementsByClassName("ot-floating-button__close")[0];
	            (c.innerHTML =
	                '<svg version="1.1" id="Layer_1" xmlns= http://www.w3.org/2000/svg xmlns:xlink= http://www.w3.org/1999/xlink x="0px" y="0px" viewBox="0 0 18 18" style="enable-background:new 0 0 18 18;" xml:space="preserve"><path class="st0" d="M12.27,9l5.05-5.05c0.9-0.9,0.9-2.37,0-3.27c-0.9-0.9-2.37-0.9-3.27,0L9,5.73L3.95,0.68 c-0.9-0.9-2.37-0.9-3.27,0s-0.9,2.37,0,3.27L5.73,9l-5.05,5.05c-0.9,0.9-0.9,2.37,0,3.27c0.9,0.9,2.37,0.9,3.27,0L9,12.27l5.05,5.05 c0.9,0.9,2.37,0.9,3.27,0c0.9-0.9,0.9-2.37,0-3.27L12.27,9z"/></svg>'),
	                c.addEventListener("click", function (e) {
	                    n.classList.remove("ot-modal-open");
	                });
	        }
	    }

</script>
<!-- OneTrust Cookies Consent Notice end for coca-cola.com/ca/en -->
        
        
    

    

    

    <meta name="cepVersion" content="1.845.0"/>
    <meta name="frontEndVersion" content="1.2664.0"/>
    <meta name="publicationDate" content="2025-02-12T16:32:00Z"/>
    
    <meta name="thumbnailImage" content="/content/dam/onexp/ca/en/brands/powerade/en-powerade.png"/>
    
    <meta name="contentType" content="Brands"/>
    <meta name="brand" content="Powerade"/>
    
    
    <meta name="siteSection" content="Brands - Powerade"/>
    <meta name="instanceName" content="75f9f5b79c-m5g5w"/>
    <meta name="xtndRecaptchaSiteKeyV3" content="6Lfe5b8kAAAAAItc37dlx65sVLiwPNekg84cNLzm"/>
    

    
    

    
    

    <!-- PWA Configuration for iOS -->
    
<meta name="apple-mobile-web-app-capable" content="yes"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 440px) and (device-height: 956px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/iPhone_16_Pro_Max_landscape.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 402px) and (device-height: 874px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/iPhone_16_Pro_landscape.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 430px) and (device-height: 932px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/iPhone_16_Pl__iPhone_15_Pro_Max__iPhone_15_Pl__iPhone_14_Pro_Max_land.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 393px) and (device-height: 852px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/iPhone_16__iPhone_15_Pro__iPhone_15__iPhone_14_Pro_landscape.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 428px) and (device-height: 926px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/iPhone_14_Plus__iPhone_13_Pro_Max__iPhone_12_Pro_Max_landscape.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 390px) and (device-height: 844px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/iPhone_14__iPhone_13_Pro__iPhone_13__iPhone_12_Pro__iPhone_12_land.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/iPhone_13_mi__iPhone_12_mi__iPhone_11_Pro__iPhone_XS__iPhone_X_land.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/iPhone_11_Pro_Max__iPhone_XS_Max_landscape.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/iPhone_11__iPhone_XR_landscape.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3) and (orientation: landscape)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/iPhone_8_Plus__iPhone_7_Plus__iPhone_6s_Plus__iPhone_6_Plus_landscape.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/iPhone_8__iPhone_7__iPhone_6s__iPhone_6__4.7__iPhone_SE_landscape.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/4__iPhone_SE__iPod_touch_5th_generation_and_later_landscape.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 1032px) and (device-height: 1376px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/13__iPad_Pro_M4_landscape.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/12.9__iPad_Pro_landscape.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 834px) and (device-height: 1210px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/11__iPad_Pro_M4_landscape.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/11__iPad_Pro__10.5__iPad_Pro_landscape.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 820px) and (device-height: 1180px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/10.9__iPad_Air_landscape.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/10.5__iPad_Air_landscape.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 810px) and (device-height: 1080px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/10.2__iPad_landscape.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/9.7__iPad_Pro__7.9__iPad_mini__9.7__iPad_Air__9.7__iPad_landscape.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 744px) and (device-height: 1133px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/8.3__iPad_Mini_landscape.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 440px) and (device-height: 956px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/iPhone_16_Pro_Max_portrait.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 402px) and (device-height: 874px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/iPhone_16_Pro_portrait.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 430px) and (device-height: 932px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/iPhone_16_Pl__iPhone_15_Pro_Max__iPhone_15_Pl__iPhone_14_Pro_Max_port.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 393px) and (device-height: 852px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/iPhone_16__iPhone_15_Pro__iPhone_15__iPhone_14_Pro_portrait.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 428px) and (device-height: 926px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/iPhone_14_Plus__iPhone_13_Pro_Max__iPhone_12_Pro_Max_portrait.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 390px) and (device-height: 844px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/iPhone_14__iPhone_13_Pro__iPhone_13__iPhone_12_Pro__iPhone_12_port.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/iPhone_13_mi__iPhone_12_mi__iPhone_11_Pro__iPhone_XS__iPhone_X_port.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/iPhone_11_Pro_Max__iPhone_XS_Max_portrait.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/iPhone_11__iPhone_XR_portrait.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3) and (orientation: portrait)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/iPhone_8_Plus__iPhone_7_Plus__iPhone_6s_Plus__iPhone_6_Plus_portrait.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/iPhone_8__iPhone_7__iPhone_6s__iPhone_6__4.7__iPhone_SE_portrait.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/4__iPhone_SE__iPod_touch_5th_generation_and_later_portrait.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 1032px) and (device-height: 1376px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/13__iPad_Pro_M4_portrait.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/12.9__iPad_Pro_portrait.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 834px) and (device-height: 1210px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/11__iPad_Pro_M4_portrait.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/11__iPad_Pro__10.5__iPad_Pro_portrait.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 820px) and (device-height: 1180px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/10.9__iPad_Air_portrait.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/10.5__iPad_Air_portrait.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 810px) and (device-height: 1080px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/10.2__iPad_portrait.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/9.7__iPad_Pro__7.9__iPad_mini__9.7__iPad_Air__9.7__iPad_portrait.png"/>
<link rel="apple-touch-startup-image" media="screen and (device-width: 744px) and (device-height: 1133px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/splash-screens/8.3__iPad_Mini_portrait.png"/>



    
    <script async src="/etc.clientlibs/cep/clientlibs/clientlib-cwr.lc-085b194b52d77cc30f6cc60ab771e406-lc.min.js"></script>

    <script>
        (function (n, i, v, r, c, x) {
            x = window.AwsRumClient = {q: [], n: n, i: i, v: v, r: r, c: c};
            window[n] = function (c, p) {
                x.q.push({c: c, p: p});
            };
        })(
            "cwr",
            "e663ab94-8a97-4236-90ca-2ad1b864147a",
            "1.0.0",
            "us-east-1",
            {
                sessionSampleRate: 0.05,
                guestRoleArn: "arn:aws:iam::971536809886:role/RUM-Monitor-us-east-1-971536809886-1692815399861-Unauth",
                identityPoolId: "us-east-1:f715f9dd-f502-422b-bb4f-e1471234deb4",
                endpoint: "https://dataplane.rum.us-east-1.amazonaws.com",
                telemetries: [
                    [
                        'http',
                        {
                            urlsToExclude: "aemform.af.prefilldata.json,/content/forms/af,/libs/granite/csrf/token.json".length > 0 ? "aemform.af.prefilldata.json,/content/forms/af,/libs/granite/csrf/token.json".split(',').map(error => new RegExp(error)) : []
                        }
                    ],
                    [
                        "errors",
                        {
                            ignore: (errorEvent, errorsToIgnore = "") => {
                                if (errorEvent && errorsToIgnore.length > 0) {
                                    const regexpRules = errorsToIgnore.split(',').map(error => new RegExp(error));
                                    return regexpRules.find(rule => rule.test(errorEvent.message)) !== undefined;
                                } else {
                                    return false;
                                }
                            }
                        }
                    ],
                     "performance",
                ],
                allowCookies: false,
                enableXRay: true
            }
        );

        window.cep = window.cep || {};
        window.cep.reportRumError = (parameters) => {
            if (cwr !== undefined) {
                const record = {
                    type: 'com.amazon.rum.js_error_event',
                    data: { version: '1.0.0', type: 'Error', ...parameters }
                };

                cwr('recordEvent', record);
                console.error(record.data);
            } else {
                console.error("RUM Error when recording custom event - cwr is undefined.");
            }
        }

        document.addEventListener("securitypolicyviolation", (e) => {
            window.cep.reportRumError({
                message: "Security Policy Violation Error",
                stack: "Security Policy Violation Error. Blocked URI: " + e.blockedURI + ", Violated Directive: " + e.violatedDirective
            });
        });

    </script>


    
    
<link rel="canonical" href="https://www.coca-cola.com/ca/en/brands/powerade"/>


    

<script src="https://cdn.global.gcds.coke.com/naou/sdk/latest.js?v3" crossorigin="anonymous"></script>




    
        <!-- Google Tag Manager - Consent Mode V2 ---> 
<script> 
window.googleConsentModeV2= false; 
console.log('Consent Mode V2 ' + (window.googleConsentModeV2 ? 'enabled' : 'disabled')); 
</script> 
<!-- End Google Tag Manager - Consent Mode V2 --->
    
    <!-- Google Tag Manager --> 
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start': 
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0], 
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src= 
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f); 
})(window,document,'script','dataLayer','GTM-NBWPCZW');</script> 
<!-- End Google Tag Manager -->


<script type="text/javascript">
    const CepDataLayer = {
        push: (args) => {
            if (window.adobeDataLayer) {
                adobeDataLayer.push(args);
            }
            if (window.dataLayer) {
                dataLayer.push(args);
            }
        }
    }
</script>

<link rel="apple-touch-icon" sizes="180x180" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/favicons/apple-touch-icon.png"/>
<link rel="icon" type="image/png" sizes="32x32" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/favicons/favicon-32x32.png"/>
<link rel="icon" type="image/png" sizes="16x16" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/favicons/favicon-16x16.png"/>
<link rel="mask-icon" href="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/resources/favicons/safari-pinned-tab.svg" color="#000000"/>






    
    
    

    

    
    
    <link rel="stylesheet" href="/etc.clientlibs/onexp/clientlibs/clientlib-base.lc-123d3f370316a0c69df82281c7643d34-lc.min.css" type="text/css">


    
    
    <script async src="/etc.clientlibs/core/wcm/components/commons/datalayer/v2/clientlibs/core.wcm.components.commons.datalayer.v2.lc-1e0136bad0acfb78be509234578e44f9-lc.min.js"></script>


    
    <script async src="/etc.clientlibs/core/wcm/components/commons/datalayer/acdl/core.wcm.components.commons.datalayer.acdl.lc-bf921af342fd2c40139671dbf0920a1f-lc.min.js"></script>



    
</head>
<body class="page basicpage template-brand-landing-page" id="page-faf8750569" data-cmp-link-accessibility-enabled data-cmp-link-accessibility-text="opens in a new tab" data-cmp-data-layer-enabled>
<script>
	window.tccc = window.tccc || {};
	window.tccc.cloudsearch = "onexp_ca_en";
</script>
<script>
    window.adobeDataLayer = window.adobeDataLayer || [];
    adobeDataLayer.push({
        page: JSON.parse("{\x22page\u002Dfaf8750569\x22:{\x22@type\x22:\x22cep\/components\/page\x22,\x22repo:modifyDate\x22:\x222025\u002D02\u002D12T16:32:06Z\x22,\x22dc:title\x22:\x22POWERADE\x22,\x22dc:description\x22:\x22Discover Powerade sports drinks and stay hydrated during your workout. Made with vitamins and minerals to help you perform at your best.\x22,\x22xdm:template\x22:\x22\/conf\/onexp\/settings\/wcm\/templates\/brand\u002Dlanding\u002Dpage\x22,\x22xdm:language\x22:\x22en\u002DCA\x22,\x22xdm:tags\x22:[],\x22repo:path\x22:\x22\/content\/onexp\/ca\/en\/brands\/powerade.html\x22}}"),
        event: 'cmp:show',
        eventInfo: {
            path: 'page.page\u002Dfaf8750569'
        }
    });
</script>
<script>
    const siteSection = 'Brands - Powerade';
    if (siteSection !== '') {
        CepDataLayer.push({
            event: 'site_context',
            site_section: siteSection
        });
    }
</script>


    




    


    <!-- Google Tag Manager (noscript) --> 
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NBWPCZW" 
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript> 
<!-- End Google Tag Manager (noscript) -->



    <div id="single-sdk-config" data-cds-gam-url="https://frontend.naou.gcds.coke.com/v2" data-cds-enrichment-url="https://enrichment-apig.naou.gcds.coke.com/v2" data-client-ID="42f230df-e761-4719-bdd4-fc7b58ad1c35" data-login-Url="/ca/en/login" data-token-Exchange-Url="/ca/en/token-exchange" data-region="NAOU" data-override-Country="CA" data-override-Language="en" data-site-Section="Brands - Powerade"></div>


<div class="root container responsivegrid">

    
    
    
    
    <div id="container-ed6a99d439" class="cmp-container">

        

        
        <header class="experiencefragment">

    <div class="cmp-experiencefragment cmp-experiencefragment--header">


    
    
    
    
    <div id="container-45bdb2ec9a" class="cmp-container">

        

        
        <div class="text header__skip-to-content">
<div data-cmp-data-layer="{&#34;text-b8eb45ff17&#34;:{&#34;@type&#34;:&#34;cep/components/text&#34;,&#34;repo:modifyDate&#34;:&#34;2022-11-29T13:56:37Z&#34;,&#34;xdm:text&#34;:&#34;&lt;p>&lt;a href=\&#34;#maincontent\&#34;>Skip to content&lt;/a>&lt;/p>\r\n&#34;}}" id="text-b8eb45ff17" class="cmp-text">
    <p><a href="#maincontent">Skip to content</a></p>

</div>

    

</div>
<div class="header"><div class="header-wrapper">
    <div class="cmp-container">
        <div class="adaptiveImage image">
            <div><div data-asset-id="a52e32ef-3fd1-47a6-beb1-893a409c7d93" id="adaptiveImage-4c9b9db7bb" data-cmp-data-layer="{&#34;adaptiveImage-4c9b9db7bb&#34;:{&#34;@type&#34;:&#34;cep/components/adaptiveImage&#34;,&#34;repo:modifyDate&#34;:&#34;2023-11-21T13:08:34Z&#34;,&#34;xdm:linkURL&#34;:&#34;/ca/en.html&#34;,&#34;image&#34;:{&#34;repo:id&#34;:&#34;a52e32ef-3fd1-47a6-beb1-893a409c7d93&#34;,&#34;repo:modifyDate&#34;:&#34;2023-08-14T14:59:12Z&#34;,&#34;@type&#34;:&#34;image/png&#34;,&#34;repo:path&#34;:&#34;/content/dam/onexp/ca/en/header/canada-coca-cola-logo_black_600x96 2.png&#34;}}}" class="cmp-adaptive-image cmp-image" itemscope itemtype="http://schema.org/ImageObject">
    <a class="cmp-image__link" data-cmp-clickable href="/ca/en">
        
    <picture>
        
        
        <source width="600" height="96"/>
        <img src="/content/dam/onexp/ca/en/header/canada-coca-cola-logo_black_600x96%202.png" loading="lazy" class="cmp-image__image" itemprop="contentUrl" alt="Coca Cola Company Logo"/>
    </picture>


        
    </a>
    
    
</div>

    

</div>
        </div>
        <div class="header__navigation">
            <div class="cmp-container">
                <div class="text">
                    <div>
<div data-cmp-data-layer="{&#34;text-4b886ac611&#34;:{&#34;@type&#34;:&#34;cep/components/text&#34;,&#34;repo:modifyDate&#34;:&#34;2025-03-21T13:46:11Z&#34;,&#34;xdm:text&#34;:&#34;&lt;ul>\r\n&lt;li>&lt;a href=\&#34;/content/onexp/ca/en/brands.html\&#34;>Brands&lt;/a>&lt;/li>\r\n&lt;li>&lt;a href=\&#34;/content/onexp/ca/en/offerings.html\&#34;>Promos &amp;amp; Offers&lt;/a>&lt;/li>\r\n&lt;li>&lt;a href=\&#34;#\&#34;>Impact&lt;/a>&lt;ul>\r\n&lt;li>&lt;a href=\&#34;/content/onexp/ca/en/sustainability.html\&#34; style=\&#34;background-color: rgb(255,255,255);\&#34; _rte_style=\&#34;\tbackground-color: rgb(255,255,255);\r\n\&#34;>Sustainability&lt;/a>&lt;/li>\r\n&lt;li>&lt;a href=\&#34;/content/onexp/ca/en/social.html\&#34;>Social&lt;/a>&lt;/li>\r\n&lt;/ul>\r\n&lt;/li>\r\n&lt;li>&lt;a href=\&#34;https://shop-cocacola.com/en/\&#34; target=\&#34;_blank\&#34; rel=\&#34;noopener noreferrer\&#34;>Shop&lt;/a>&lt;/li>\r\n&lt;li>&lt;a href=\&#34;/content/onexp/ca/fr.html\&#34;>Français&lt;/a>&lt;/li>\r\n&lt;/ul>\r\n&#34;}}" id="text-4b886ac611" class="cmp-text">
    <ul><li><a href="/ca/en/brands">Brands</a></li><li><a href="/ca/en/offerings">Promos &amp; Offers</a></li><li><a href="#">Impact</a><ul><li><a href="/ca/en/sustainability" style="	background-color: rgb(255,255,255);
">Sustainability</a></li><li><a href="/ca/en/social">Social</a></li></ul>
</li><li><a href="https://shop-cocacola.com/en/" target="_blank" rel="noopener noreferrer">Shop</a></li><li><a href="/ca/fr">Français</a></li></ul>

</div>

    

</div>
                </div>
                
                    <div class="header__login-section--mobile">
                        <div class="cmp-container">
                            <div class="text header__login-experience-text">
                                <div class="cmp-text"><p>Want a personalized experience and access to exclusive content?</p>
</div>
                            </div>
                            <div class="button header__log-in-button">
                                <button class="cmp-button" aria-label="Log In">
                                    <span class="cmp-button__text">Log In</span>
                                </button>
                            </div>
                            <div class="button header__sign-up-button">
                                <button class="cmp-button" aria-label="Sign Up">
                                    <span class="cmp-button__text">Sign Up</span>
                                </button>
                            </div>
                            <div class="button header__manage-account-button">
                                <button class="cmp-button" aria-label="Manage Account">
                                    <span class="cmp-button__text">Manage Account</span>
                                </button>
                            </div>
                            
                            
                            <div class="button header__log-out-button">
                                <button class="cmp-button" aria-label="Log Out">
                                    <span class="cmp-button__text">Log Out</span>
                                </button>
                            </div>
                            <div class="text header__login-unavailable-text">
                                <div class="cmp-text"></div>
                            </div>
                        </div>
                    </div>
                
                <div class="header__mobile-account">
                    <div class="cmp-container">
                        <div class="text">
                            <div class="cmp-text">
                                <p><p>Select Location</p>
</p>
                            </div>
                            <div class="header__location-button">
                                <a class="cmp-button" href="/country-selector" aria-label="Canada">
                                    <span class="cmp-button__icon cmp-button__icon--location"></span>
                                    <span class="cmp-button__text">Canada</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="header__buttons-wrapper">
            <div class="cmp-container">
                
                    <div class="button header__account-button">
                        <button class="cmp-button" aria-label="Account">
                            <span class="cmp-button__icon cmp-button__icon--profile"></span>
                            <span class="cmp-button__text"> Account </span>
                        </button>
                    </div>
                
                <div class="header__login-section">
                    <div class="cmp-container">
                        <div class="button header__log-in-button">
                            <button class="cmp-button" aria-label="Log In">
                                <span class="cmp-button__text">Log In</span>
                            </button>
                        </div>
                        <div class="button header__sign-up-button">
                            <button class="cmp-button" aria-label="Sign Up">
                                <span class="cmp-button__text">Sign Up</span>
                            </button>
                        </div>
                        <div class="button header__manage-account-button">
                            <button class="cmp-button" aria-label="Manage Account">
                                <span class="cmp-button__text">Manage Account</span>
                            </button>
                        </div>
                        
                        
                        <div class="button header__log-out-button">
                            <button class="cmp-button" aria-label="Log Out">
                                <span class="cmp-button__text">Log Out</span>
                            </button>
                        </div>
                        <div class="text header__login-experience-text">
                            <div class="cmp-text"><p>Want a personalized experience and access to exclusive content?</p>
</div>
                        </div>
                        <div class="text header__login-unavailable-text">
                            <div class="cmp-text"></div>
                        </div>
                    </div>
                </div>
                
                <div class="button header__menu-button header__button--separator-hidden">
                    <div class="cmp-container">
                        <button class="cmp-button" aria-label="Menu">
                            <span class="cmp-button__icon cmp-button__icon--menu"></span>
                            <span class="cmp-button__text">Menu</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="maincontent"></div>
</div>

        
    </div>

</div>
    
    

    

</header>
<main class="container responsivegrid">

    
    
    
    
    <div id="container-c64f77551f" class="cmp-container">

        

        
        <div class="container responsivegrid secondary-header-logo container__top">

    
    
    
    
    <div id="container-f4f732a46b" class="cmp-container">

        

        
        <div class="adaptiveImage image no-renditions"><div data-asset-id="0a120b4a-3fc4-4082-ae54-5f91f4a1d2c8" id="adaptiveImage-b21e2f464e" data-cmp-data-layer="{&#34;adaptiveImage-b21e2f464e&#34;:{&#34;@type&#34;:&#34;cep/components/adaptiveImage&#34;,&#34;repo:modifyDate&#34;:&#34;2024-04-25T18:52:26Z&#34;,&#34;dc:title&#34;:&#34;Header logo&#34;,&#34;xdm:linkURL&#34;:&#34;/ca/en/brands/powerade.html&#34;,&#34;image&#34;:{&#34;repo:id&#34;:&#34;0a120b4a-3fc4-4082-ae54-5f91f4a1d2c8&#34;,&#34;repo:modifyDate&#34;:&#34;2024-01-06T21:07:40Z&#34;,&#34;@type&#34;:&#34;image/png&#34;,&#34;repo:path&#34;:&#34;/content/dam/onexp/ca/en/brands/powerade/en-powerade-header-tall.png&#34;,&#34;repo:mobile:path&#34;:&#34;/content/dam/onexp/ca/en/home-images/brands/powerade/ca_powerade_logo_500x180.png&#34;}}}" class="cmp-adaptive-image cmp-image" itemscope itemtype="http://schema.org/ImageObject">
    <a class="cmp-image__link" data-cmp-clickable href="/ca/en/brands/powerade">
        
    <picture>
        <source media="(min-width: 1200px)" width="967" height="256"/>
        <source width="500" height="180"/>
        
        <img src="/content/dam/onexp/ca/en/brands/powerade/en-powerade-header-tall.png" class="cmp-image__image" itemprop="contentUrl" alt="POWERADE logo" title="Header logo"/>
    </picture>


        
    </a>
    
    <meta itemprop="caption" content="Header logo"/>
</div>

    

</div>

        
    </div>

</div>
<div class="container responsivegrid secondary-header-with-navigation container__top-end">

    
    
    
    
    <div id="container-2fcc081096" class="cmp-container">

        

        
        <div class="text">
<div data-cmp-data-layer="{&#34;text-d19e30f036&#34;:{&#34;@type&#34;:&#34;cep/components/text&#34;,&#34;repo:modifyDate&#34;:&#34;2024-07-17T16:30:32Z&#34;,&#34;xdm:text&#34;:&#34;&lt;ul>\r\n&lt;li>&lt;a href=\&#34;/content/onexp/ca/en/brands/powerade.html\&#34;>Home&lt;/a>&lt;/li>\r\n&lt;li>&lt;a href=\&#34;/content/onexp/ca/en/brands/powerade/products.html\&#34;>Products&lt;/a>&lt;/li>\r\n&lt;/ul>\r\n&#34;}}" id="text-d19e30f036" class="cmp-text">
    <ul><li><a href="/ca/en/brands/powerade">Home</a></li><li><a href="/ca/en/brands/powerade/products">Products</a></li></ul>

</div>

    

</div>

        
    </div>

</div>
<div class="container responsivegrid container__content-start">

    
    
    
    
    <div id="container-98062742dd" class="cmp-container">

        

        
        <div class="container responsivegrid parallax color-variants--secondary">

    
    
    
    
    <div id="container-ef630e408d" class="cmp-container">

        
    <picture>
        <source srcset="/content/dam/onexp/ca/en/brands/powerade/powerade-hero-parallax-desktop-left.png/width100.png 100w,/content/dam/onexp/ca/en/brands/powerade/powerade-hero-parallax-desktop-left.png/width200.png 200w,/content/dam/onexp/ca/en/brands/powerade/powerade-hero-parallax-desktop-left.png/width380.png 380w,/content/dam/onexp/ca/en/brands/powerade/powerade-hero-parallax-desktop-left.png/width500.png 500w,/content/dam/onexp/ca/en/brands/powerade/powerade-hero-parallax-desktop-left.png/width767.png 767w,/content/dam/onexp/ca/en/brands/powerade/powerade-hero-parallax-desktop-left.png/width1024.png 1024w,/content/dam/onexp/ca/en/brands/powerade/powerade-hero-parallax-desktop-left.png/width1338.png 1338w,/content/dam/onexp/ca/en/brands/powerade/powerade-hero-parallax-desktop-left.png/width1960.png 1960w,/content/dam/onexp/ca/en/brands/powerade/powerade-hero-parallax-desktop-left.png/width2674.png 2674w,/content/dam/onexp/ca/en/brands/powerade/powerade-hero-parallax-desktop-left.png/width3840.png 3840w" media="(min-width: 1200px)" width="1960" height="2178"/>
        <source srcset="/content/dam/onexp/ca/en/brands/powerade/powerade-hero-parallax-mobile-left.png/width100.png 100w,/content/dam/onexp/ca/en/brands/powerade/powerade-hero-parallax-mobile-left.png/width200.png 200w,/content/dam/onexp/ca/en/brands/powerade/powerade-hero-parallax-mobile-left.png/width380.png 380w,/content/dam/onexp/ca/en/brands/powerade/powerade-hero-parallax-mobile-left.png/width500.png 500w,/content/dam/onexp/ca/en/brands/powerade/powerade-hero-parallax-mobile-left.png/width767.png 767w,/content/dam/onexp/ca/en/brands/powerade/powerade-hero-parallax-mobile-left.png/width1024.png 1024w,/content/dam/onexp/ca/en/brands/powerade/powerade-hero-parallax-mobile-left.png/width1338.png 1338w,/content/dam/onexp/ca/en/brands/powerade/powerade-hero-parallax-mobile-left.png/width1960.png 1960w,/content/dam/onexp/ca/en/brands/powerade/powerade-hero-parallax-mobile-left.png/width2674.png 2674w,/content/dam/onexp/ca/en/brands/powerade/powerade-hero-parallax-mobile-left.png/width3840.png 3840w" width="750" height="1200"/>
        
        <img src="/content/dam/onexp/ca/en/brands/powerade/powerade-hero-parallax-desktop-left.png" class="cmp-image__image" itemprop="contentUrl" alt/>
    </picture>


        
        <div class="teaser hero hero--normal"><div id="teaser-7ac908de88" class="cmp-teaser">
    
        <div class="cmp-teaser__content">
            
    

            
    

            
    

            
    

        </div>
        
        
    <div class="cmp-teaser__image"><div data-asset-id="dc7b4582-8852-4c5e-a228-16f6bae7f2fb" id="teaser-7ac908de88-image" data-cmp-data-layer="{&#34;teaser-7ac908de88-image&#34;:{&#34;@type&#34;:&#34;cep/components/adaptiveImage&#34;,&#34;repo:modifyDate&#34;:&#34;2025-02-12T16:32:06Z&#34;,&#34;dc:title&#34;:&#34;2025 POWERADE Canada Website Banner 6313&#34;,&#34;xdm:linkURL&#34;:&#34;/ca/en/brands/powerade.html&#34;,&#34;image&#34;:{&#34;repo:id&#34;:&#34;dc7b4582-8852-4c5e-a228-16f6bae7f2fb&#34;,&#34;repo:modifyDate&#34;:&#34;2025-02-12T16:32:03Z&#34;,&#34;@type&#34;:&#34;image/jpeg&#34;,&#34;repo:path&#34;:&#34;/content/dam/onexp/ca/en/brands/powerade/home/<USER>/content/dam/onexp/ca/en/brands/powerade/home/<USER>" class="cmp-adaptive-image cmp-image" itemscope itemtype="http://schema.org/ImageObject">
    <a class="cmp-image__link" data-cmp-clickable href="/ca/en/brands/powerade">
        
    <picture>
        <source srcset="/content/dam/onexp/ca/en/brands/powerade/home/<USER>/width100.jpg 100w,/content/dam/onexp/ca/en/brands/powerade/home/<USER>/width200.jpg 200w,/content/dam/onexp/ca/en/brands/powerade/home/<USER>/width380.jpg 380w,/content/dam/onexp/ca/en/brands/powerade/home/<USER>/width500.jpg 500w,/content/dam/onexp/ca/en/brands/powerade/home/<USER>/width767.jpg 767w,/content/dam/onexp/ca/en/brands/powerade/home/<USER>/width1024.jpg 1024w,/content/dam/onexp/ca/en/brands/powerade/home/<USER>/width1338.jpg 1338w,/content/dam/onexp/ca/en/brands/powerade/home/<USER>/width1960.jpg 1960w,/content/dam/onexp/ca/en/brands/powerade/home/<USER>/width2674.jpg 2674w,/content/dam/onexp/ca/en/brands/powerade/home/<USER>/width3840.jpg 3840w" media="(min-width: 1200px)" width="1280" height="604"/>
        <source srcset="/content/dam/onexp/ca/en/brands/powerade/home/<USER>/width100.jpg 100w,/content/dam/onexp/ca/en/brands/powerade/home/<USER>/width200.jpg 200w,/content/dam/onexp/ca/en/brands/powerade/home/<USER>/width380.jpg 380w,/content/dam/onexp/ca/en/brands/powerade/home/<USER>/width500.jpg 500w,/content/dam/onexp/ca/en/brands/powerade/home/<USER>/width767.jpg 767w,/content/dam/onexp/ca/en/brands/powerade/home/<USER>/width1024.jpg 1024w,/content/dam/onexp/ca/en/brands/powerade/home/<USER>/width1338.jpg 1338w,/content/dam/onexp/ca/en/brands/powerade/home/<USER>/width1960.jpg 1960w,/content/dam/onexp/ca/en/brands/powerade/home/<USER>/width2674.jpg 2674w,/content/dam/onexp/ca/en/brands/powerade/home/<USER>/width3840.jpg 3840w" width="342" height="564"/>
        
        <img src="/content/dam/onexp/ca/en/brands/powerade/home/<USER>" class="cmp-image__image" itemprop="contentUrl" alt="2025 POWERADE Canada Website Banner 6313" title="2025 POWERADE Canada Website Banner 6313"/>
    </picture>


        
    </a>
    
    <meta itemprop="caption" content="2025 POWERADE Canada Website Banner 6313"/>
</div>

    

</div>

        
    
</div>

    

</div>

        
    </div>

</div>

        
    </div>

</div>
<div class="container responsivegrid container__content">

    
    
    
    
    <div id="container-e7207b61c1" class="cmp-container">

        

        
        <div class="title">
<div data-cmp-data-layer="{&#34;title-1a5abf71c2&#34;:{&#34;@type&#34;:&#34;cep/components/title&#34;,&#34;repo:modifyDate&#34;:&#34;2023-10-05T21:08:52Z&#34;,&#34;dc:title&#34;:&#34;Products&#34;}}" id="title-1a5abf71c2" class="cmp-title">
    <h2 class="cmp-title__text">Products</h2>
</div>

    

</div>
<div class="container responsivegrid grid two-columns">

    
    
    
    
    <div id="container-6fdb8cbb8c" class="cmp-container">

        

        
        <div class="teaser content-card"><div id="teaser-40d54b13f5" class="cmp-teaser">
    
        <div class="cmp-teaser__content">
            
    

            
    <h3 class="cmp-teaser__title">
        POWERADE
    </h3>

            
    

            
    <div class="cmp-teaser__action-container">
        
    <a class="cmp-teaser__action-link" id="teaser-40d54b13f5-cta-f496ef4652" data-cmp-data-layer="{&#34;teaser-40d54b13f5-cta-f496ef4652&#34;:{&#34;@type&#34;:&#34;cep/components/teaser/cta&#34;,&#34;dc:title&#34;:&#34;Explore&#34;,&#34;xdm:linkURL&#34;:&#34;/ca/en/brands/powerade/products/powerade.html&#34;}}" data-cmp-clickable href="/ca/en/brands/powerade/products/powerade">Explore</a>

    </div>

        </div>
        
        
    <div class="cmp-teaser__image"><div id="teaser-40d54b13f5-image" data-cmp-data-layer="{&#34;teaser-40d54b13f5-image&#34;:{&#34;@type&#34;:&#34;cep/components/adaptiveImage&#34;,&#34;repo:modifyDate&#34;:&#34;2024-08-08T15:29:16Z&#34;,&#34;dc:title&#34;:&#34;Mixed Berry, card&#34;,&#34;xdm:linkURL&#34;:&#34;/ca/en/brands/powerade/products/powerade.html&#34;,&#34;image&#34;:{&#34;repo:id&#34;:&#34;3b565ea7-819f-4550-bc5a-147a0da961cc&#34;,&#34;repo:modifyDate&#34;:&#34;2023-10-10T14:16:06Z&#34;,&#34;@type&#34;:&#34;image/png&#34;,&#34;repo:path&#34;:&#34;/content/dam/onexp/ca/en/brands/powerade/products/powerade/mixed-berry/en-mixed-berry-710mL-swipe.png&#34;,&#34;repo:mobile:path&#34;:&#34;/content/dam/onexp/ca/en/brands/powerade/en-powerade-brands-logo.png&#34;}}}" class="cmp-adaptive-image cmp-image" itemscope itemtype="http://schema.org/ImageObject">
    
        
    <picture>
        
        
        <source srcset="/content/dam/onexp/ca/en/brands/powerade/products/powerade/mixed-berry/en-mixed-berry-710mL-swipe.png/width100.png 100w,/content/dam/onexp/ca/en/brands/powerade/products/powerade/mixed-berry/en-mixed-berry-710mL-swipe.png/width200.png 200w,/content/dam/onexp/ca/en/brands/powerade/products/powerade/mixed-berry/en-mixed-berry-710mL-swipe.png/width380.png 380w,/content/dam/onexp/ca/en/brands/powerade/products/powerade/mixed-berry/en-mixed-berry-710mL-swipe.png/width500.png 500w,/content/dam/onexp/ca/en/brands/powerade/products/powerade/mixed-berry/en-mixed-berry-710mL-swipe.png/width767.png 767w,/content/dam/onexp/ca/en/brands/powerade/products/powerade/mixed-berry/en-mixed-berry-710mL-swipe.png/width1024.png 1024w,/content/dam/onexp/ca/en/brands/powerade/products/powerade/mixed-berry/en-mixed-berry-710mL-swipe.png/width1338.png 1338w,/content/dam/onexp/ca/en/brands/powerade/products/powerade/mixed-berry/en-mixed-berry-710mL-swipe.png/width1960.png 1960w,/content/dam/onexp/ca/en/brands/powerade/products/powerade/mixed-berry/en-mixed-berry-710mL-swipe.png/width2674.png 2674w,/content/dam/onexp/ca/en/brands/powerade/products/powerade/mixed-berry/en-mixed-berry-710mL-swipe.png/width3840.png 3840w" width="1040" height="1040"/>
        <img src="/content/dam/onexp/ca/en/brands/powerade/products/powerade/mixed-berry/en-mixed-berry-710mL-swipe.png" loading="lazy" class="cmp-image__image" itemprop="contentUrl" alt="POWERADE Mixed Berry 710 mL bottle" title="Mixed Berry, card"/>
    </picture>


        
    
    
    <meta itemprop="caption" content="Mixed Berry, card"/>
</div>

    

</div>

        
    
</div>

    

</div>
<div class="teaser content-card"><div id="teaser-f081d1ac44" class="cmp-teaser">
    
        <div class="cmp-teaser__content">
            
    

            
    <h3 class="cmp-teaser__title">
        POWERADE ZERO
    </h3>

            
    

            
    <div class="cmp-teaser__action-container">
        
    <a class="cmp-teaser__action-link" id="teaser-f081d1ac44-cta-6316240ee0" data-cmp-data-layer="{&#34;teaser-f081d1ac44-cta-6316240ee0&#34;:{&#34;@type&#34;:&#34;cep/components/teaser/cta&#34;,&#34;dc:title&#34;:&#34;Explore&#34;,&#34;xdm:linkURL&#34;:&#34;/ca/en/brands/powerade/products/powerade-zero.html&#34;}}" data-cmp-clickable href="/ca/en/brands/powerade/products/powerade-zero">Explore</a>

    </div>

        </div>
        
        
    <div class="cmp-teaser__image"><div id="teaser-f081d1ac44-image" data-cmp-data-layer="{&#34;teaser-f081d1ac44-image&#34;:{&#34;@type&#34;:&#34;cep/components/adaptiveImage&#34;,&#34;repo:modifyDate&#34;:&#34;2024-04-30T13:59:54Z&#34;,&#34;dc:title&#34;:&#34;ZERO Blue Raspberry, card&#34;,&#34;xdm:linkURL&#34;:&#34;/ca/en/brands/powerade/products/powerade-zero.html&#34;,&#34;image&#34;:{&#34;repo:id&#34;:&#34;22a726a0-af46-4f04-b56d-ecd7a75df159&#34;,&#34;repo:modifyDate&#34;:&#34;2023-10-10T13:38:58Z&#34;,&#34;@type&#34;:&#34;image/png&#34;,&#34;repo:path&#34;:&#34;/content/dam/onexp/ca/en/brands/powerade/products/zero/blue-raspberry/en-zs-blue-raspberry-710mL-swipe.png&#34;,&#34;repo:mobile:path&#34;:&#34;/content/dam/onexp/ca/en/brands/powerade/en-powerade-brands-logo.png&#34;}}}" class="cmp-adaptive-image cmp-image" itemscope itemtype="http://schema.org/ImageObject">
    
        
    <picture>
        
        
        <source srcset="/content/dam/onexp/ca/en/brands/powerade/products/zero/blue-raspberry/en-zs-blue-raspberry-710mL-swipe.png/width100.png 100w,/content/dam/onexp/ca/en/brands/powerade/products/zero/blue-raspberry/en-zs-blue-raspberry-710mL-swipe.png/width200.png 200w,/content/dam/onexp/ca/en/brands/powerade/products/zero/blue-raspberry/en-zs-blue-raspberry-710mL-swipe.png/width380.png 380w,/content/dam/onexp/ca/en/brands/powerade/products/zero/blue-raspberry/en-zs-blue-raspberry-710mL-swipe.png/width500.png 500w,/content/dam/onexp/ca/en/brands/powerade/products/zero/blue-raspberry/en-zs-blue-raspberry-710mL-swipe.png/width767.png 767w,/content/dam/onexp/ca/en/brands/powerade/products/zero/blue-raspberry/en-zs-blue-raspberry-710mL-swipe.png/width1024.png 1024w,/content/dam/onexp/ca/en/brands/powerade/products/zero/blue-raspberry/en-zs-blue-raspberry-710mL-swipe.png/width1338.png 1338w,/content/dam/onexp/ca/en/brands/powerade/products/zero/blue-raspberry/en-zs-blue-raspberry-710mL-swipe.png/width1960.png 1960w,/content/dam/onexp/ca/en/brands/powerade/products/zero/blue-raspberry/en-zs-blue-raspberry-710mL-swipe.png/width2674.png 2674w,/content/dam/onexp/ca/en/brands/powerade/products/zero/blue-raspberry/en-zs-blue-raspberry-710mL-swipe.png/width3840.png 3840w" width="1040" height="1040"/>
        <img src="/content/dam/onexp/ca/en/brands/powerade/products/zero/blue-raspberry/en-zs-blue-raspberry-710mL-swipe.png" loading="lazy" class="cmp-image__image" itemprop="contentUrl" alt="POWERADE Zero Blue Raspberry 710 mL bottle" title="ZERO Blue Raspberry, card"/>
    </picture>


        
    
    
    <meta itemprop="caption" content="ZERO Blue Raspberry, card"/>
</div>

    

</div>

        
    
</div>

    

</div>

        
    </div>

</div>
<div class="container responsivegrid color-variants--primary">

    
    
    
    
    <div id="container-66e7b0a4d5" class="cmp-container">

        

        
        <div class="title">
<div data-cmp-data-layer="{&#34;title-65f817e8be&#34;:{&#34;@type&#34;:&#34;cep/components/title&#34;,&#34;repo:modifyDate&#34;:&#34;2023-10-05T13:08:17Z&#34;,&#34;dc:title&#34;:&#34;Property Partnerships&#34;}}" id="title-65f817e8be" class="cmp-title">
    <h2 class="cmp-title__text">Property Partnerships</h2>
</div>

    

</div>

        
    </div>

</div>
<div class="container responsivegrid grid two-columns color-variants--primary">

    
    
    
    
    <div id="container-103faebbbd" class="cmp-container">

        

        
        <div class="teaser content-card"><div id="teaser-f06a4d8af8" class="cmp-teaser">
    
        <div class="cmp-teaser__content">
            
    

            
    <h3 class="cmp-teaser__title">
        Olympics
    </h3>

            
    <div class="cmp-teaser__description"><p>Official Sports Drink of the Olympic Games</p>
</div>

            
    

        </div>
        
        
    <div class="cmp-teaser__image"><div id="teaser-f06a4d8af8-image" data-cmp-data-layer="{&#34;teaser-f06a4d8af8-image&#34;:{&#34;@type&#34;:&#34;cep/components/adaptiveImage&#34;,&#34;repo:modifyDate&#34;:&#34;2024-01-13T01:03:11Z&#34;,&#34;dc:title&#34;:&#34;Olympics, parter&#34;,&#34;xdm:linkURL&#34;:&#34;/ca/en/brands/powerade.html&#34;,&#34;image&#34;:{&#34;repo:id&#34;:&#34;131fccd9-98ef-4f48-9d90-f5efc93aef23&#34;,&#34;repo:modifyDate&#34;:&#34;2023-10-03T17:18:58Z&#34;,&#34;@type&#34;:&#34;image/jpeg&#34;,&#34;repo:path&#34;:&#34;/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-olympics.jpg&#34;,&#34;repo:mobile:path&#34;:&#34;/content/dam/onexp/ca/en/brands/powerade/en-powerade-brands-logo.png&#34;}}}" class="cmp-adaptive-image cmp-image" itemscope itemtype="http://schema.org/ImageObject">
    
        
    <picture>
        
        
        <source srcset="/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-olympics.jpg/width100.jpg 100w,/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-olympics.jpg/width200.jpg 200w,/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-olympics.jpg/width380.jpg 380w,/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-olympics.jpg/width500.jpg 500w,/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-olympics.jpg/width767.jpg 767w,/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-olympics.jpg/width1024.jpg 1024w,/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-olympics.jpg/width1338.jpg 1338w,/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-olympics.jpg/width1960.jpg 1960w,/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-olympics.jpg/width2674.jpg 2674w,/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-olympics.jpg/width3840.jpg 3840w" width="1040" height="1040"/>
        <img src="/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-olympics.jpg" loading="lazy" class="cmp-image__image" itemprop="contentUrl" alt="Olympics logo" title="Olympics, parter"/>
    </picture>


        
    
    
    <meta itemprop="caption" content="Olympics, parter"/>
</div>

    

</div>

        
    
</div>

    

</div>
<div class="teaser content-card"><div id="teaser-02e4695a97" class="cmp-teaser">
    
        <div class="cmp-teaser__content">
            
    

            
    <h3 class="cmp-teaser__title">
        Team Canada
    </h3>

            
    <div class="cmp-teaser__description"><p>Official Sports Drink of Team Canada</p>
</div>

            
    

        </div>
        
        
    <div class="cmp-teaser__image"><div id="teaser-02e4695a97-image" data-cmp-data-layer="{&#34;teaser-02e4695a97-image&#34;:{&#34;@type&#34;:&#34;cep/components/adaptiveImage&#34;,&#34;repo:modifyDate&#34;:&#34;2023-10-03T17:24:59Z&#34;,&#34;dc:title&#34;:&#34;Team Canada, partner&#34;,&#34;xdm:linkURL&#34;:&#34;/ca/en/brands/powerade.html&#34;,&#34;image&#34;:{&#34;repo:id&#34;:&#34;f5707fee-5705-46da-90ed-cd6d408177e7&#34;,&#34;repo:modifyDate&#34;:&#34;2023-10-03T17:19:04Z&#34;,&#34;@type&#34;:&#34;image/jpeg&#34;,&#34;repo:path&#34;:&#34;/content/dam/onexp/ca/en/brands/powerade/partners/en-parter-team-canada.jpg&#34;,&#34;repo:mobile:path&#34;:&#34;/content/dam/onexp/ca/en/brands/powerade/en-powerade-brands-logo.png&#34;}}}" class="cmp-adaptive-image cmp-image" itemscope itemtype="http://schema.org/ImageObject">
    
        
    <picture>
        
        
        <source srcset="/content/dam/onexp/ca/en/brands/powerade/partners/en-parter-team-canada.jpg/width100.jpg 100w,/content/dam/onexp/ca/en/brands/powerade/partners/en-parter-team-canada.jpg/width200.jpg 200w,/content/dam/onexp/ca/en/brands/powerade/partners/en-parter-team-canada.jpg/width380.jpg 380w,/content/dam/onexp/ca/en/brands/powerade/partners/en-parter-team-canada.jpg/width500.jpg 500w,/content/dam/onexp/ca/en/brands/powerade/partners/en-parter-team-canada.jpg/width767.jpg 767w,/content/dam/onexp/ca/en/brands/powerade/partners/en-parter-team-canada.jpg/width1024.jpg 1024w,/content/dam/onexp/ca/en/brands/powerade/partners/en-parter-team-canada.jpg/width1338.jpg 1338w,/content/dam/onexp/ca/en/brands/powerade/partners/en-parter-team-canada.jpg/width1960.jpg 1960w,/content/dam/onexp/ca/en/brands/powerade/partners/en-parter-team-canada.jpg/width2674.jpg 2674w,/content/dam/onexp/ca/en/brands/powerade/partners/en-parter-team-canada.jpg/width3840.jpg 3840w" width="1132" height="1132"/>
        <img src="/content/dam/onexp/ca/en/brands/powerade/partners/en-parter-team-canada.jpg" loading="lazy" class="cmp-image__image" itemprop="contentUrl" alt="Team Canada Olympics logo" title="Team Canada, partner"/>
    </picture>


        
    
    
    <meta itemprop="caption" content="Team Canada, partner"/>
</div>

    

</div>

        
    
</div>

    

</div>
<div class="teaser content-card"><div id="teaser-3f838e5160" class="cmp-teaser">
    
        <div class="cmp-teaser__content">
            
    

            
    <h3 class="cmp-teaser__title">
        FIFA
    </h3>

            
    <div class="cmp-teaser__description"><p>Official Partner of FIFA</p>
</div>

            
    

        </div>
        
        
    <div class="cmp-teaser__image"><div id="teaser-3f838e5160-image" data-cmp-data-layer="{&#34;teaser-3f838e5160-image&#34;:{&#34;@type&#34;:&#34;cep/components/adaptiveImage&#34;,&#34;repo:modifyDate&#34;:&#34;2023-10-03T17:24:49Z&#34;,&#34;dc:title&#34;:&#34;FIFA, partner&#34;,&#34;xdm:linkURL&#34;:&#34;/ca/en/brands/powerade.html&#34;,&#34;image&#34;:{&#34;repo:id&#34;:&#34;ba865b2d-888b-4f84-8f7b-cf8d732a5ba1&#34;,&#34;repo:modifyDate&#34;:&#34;2023-10-03T17:19:04Z&#34;,&#34;@type&#34;:&#34;image/jpeg&#34;,&#34;repo:path&#34;:&#34;/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-fifa.jpg&#34;,&#34;repo:mobile:path&#34;:&#34;/content/dam/onexp/ca/en/brands/powerade/en-powerade-brands-logo.png&#34;}}}" class="cmp-adaptive-image cmp-image" itemscope itemtype="http://schema.org/ImageObject">
    
        
    <picture>
        
        
        <source srcset="/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-fifa.jpg/width100.jpg 100w,/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-fifa.jpg/width200.jpg 200w,/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-fifa.jpg/width380.jpg 380w,/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-fifa.jpg/width500.jpg 500w,/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-fifa.jpg/width767.jpg 767w,/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-fifa.jpg/width1024.jpg 1024w,/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-fifa.jpg/width1338.jpg 1338w,/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-fifa.jpg/width1960.jpg 1960w,/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-fifa.jpg/width2674.jpg 2674w,/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-fifa.jpg/width3840.jpg 3840w" width="1040" height="1040"/>
        <img src="/content/dam/onexp/ca/en/brands/powerade/partners/en-partner-fifa.jpg" loading="lazy" class="cmp-image__image" itemprop="contentUrl" alt="FIFA logo" title="FIFA, partner"/>
    </picture>


        
    
    
    <meta itemprop="caption" content="FIFA, partner"/>
</div>

    

</div>

        
    
</div>

    

</div>
<div class="teaser content-card"><div id="teaser-27977cadc2" class="cmp-teaser">
    
        <div class="cmp-teaser__content">
            
    

            
    <h3 class="cmp-teaser__title">
        Major League Soccer
    </h3>

            
    <div class="cmp-teaser__description"><p>The Official sports drink of Major League Soccer</p>
</div>

            
    

        </div>
        
        
    <div class="cmp-teaser__image"><div id="teaser-27977cadc2-image" data-cmp-data-layer="{&#34;teaser-27977cadc2-image&#34;:{&#34;@type&#34;:&#34;cep/components/adaptiveImage&#34;,&#34;repo:modifyDate&#34;:&#34;2025-02-12T13:43:22Z&#34;,&#34;xdm:linkURL&#34;:&#34;/ca/en/brands/powerade.html&#34;,&#34;image&#34;:{&#34;repo:id&#34;:&#34;cc90ed23-3dd3-47ae-aa08-adf6363769c6&#34;,&#34;repo:modifyDate&#34;:&#34;2025-01-28T15:38:39Z&#34;,&#34;@type&#34;:&#34;image/png&#34;,&#34;repo:path&#34;:&#34;/content/dam/onexp/ca/en/brands/powerade/partners/mls-logo.png&#34;,&#34;repo:mobile:path&#34;:&#34;/content/dam/onexp/ca/en/brands/powerade/en-powerade-brands-logo.png&#34;}}}" class="cmp-adaptive-image cmp-image" itemscope itemtype="http://schema.org/ImageObject">
    
        
    <picture>
        
        
        <source srcset="/content/dam/onexp/ca/en/brands/powerade/partners/mls-logo.png/width100.png 100w,/content/dam/onexp/ca/en/brands/powerade/partners/mls-logo.png/width200.png 200w,/content/dam/onexp/ca/en/brands/powerade/partners/mls-logo.png/width380.png 380w,/content/dam/onexp/ca/en/brands/powerade/partners/mls-logo.png/width500.png 500w,/content/dam/onexp/ca/en/brands/powerade/partners/mls-logo.png/width767.png 767w,/content/dam/onexp/ca/en/brands/powerade/partners/mls-logo.png/width1024.png 1024w,/content/dam/onexp/ca/en/brands/powerade/partners/mls-logo.png/width1338.png 1338w,/content/dam/onexp/ca/en/brands/powerade/partners/mls-logo.png/width1960.png 1960w,/content/dam/onexp/ca/en/brands/powerade/partners/mls-logo.png/width2674.png 2674w,/content/dam/onexp/ca/en/brands/powerade/partners/mls-logo.png/width3840.png 3840w" width="1132" height="1132"/>
        <img src="/content/dam/onexp/ca/en/brands/powerade/partners/mls-logo.png" loading="lazy" class="cmp-image__image" itemprop="contentUrl" alt="Major League Soccer (MLS) logo"/>
    </picture>


        
    
    
    
</div>

    

</div>

        
    
</div>

    

</div>

        
    </div>

</div>

        
    </div>

</div>
<div class="container responsivegrid container__content-end">

    
    
    
    
    <div id="container-7f3006879e" class="cmp-container">

        

        
        <div class="experiencefragment color-variants--primary">

    <div class="cmp-experiencefragment cmp-experiencefragment--social-share-black">


    
    
    
    
    <div id="container-9b676e03b0" class="cmp-container">

        

        
        <div class="container responsivegrid action-card">

    
    
    
    
    <div id="container-8dc1f4ff29" class="cmp-container">

        

        
        <div class="text">
<div data-cmp-data-layer="{&#34;text-eab5742922&#34;:{&#34;@type&#34;:&#34;cep/components/text&#34;,&#34;repo:modifyDate&#34;:&#34;2023-10-05T19:49:06Z&#34;,&#34;xdm:text&#34;:&#34;&lt;h2>Follow POWERADE&lt;/h2>\r\n&#34;}}" id="text-eab5742922" class="cmp-text">
    <h2>Follow POWERADE</h2>

</div>

    

</div>
<div class="container responsivegrid">

    
    
    
    
    <div id="container-4020a3a2b1" class="cmp-container">

        

        
        <div class="button button--secondary button--icon">
<a id="button-da647a2384" class="cmp-button" aria-label="instagram" title="instagram" data-cmp-clickable data-cmp-data-layer="{&#34;button-da647a2384&#34;:{&#34;@type&#34;:&#34;cep/components/button&#34;,&#34;repo:modifyDate&#34;:&#34;2023-09-30T13:52:18Z&#34;,&#34;xdm:linkURL&#34;:&#34;https://www.instagram.com/powerade_ca/?hl=en&#34;}}" href="https://www.instagram.com/powerade_ca/?hl=en" target="_blank">
    
    <span class="cmp-button__icon cmp-button__icon--instagram" aria-hidden="true"></span>

    
</a>
</div>
<div class="button button--secondary button--icon">
<a id="button-77aa708515" class="cmp-button" aria-label="X" title="X" data-cmp-clickable data-cmp-data-layer="{&#34;button-77aa708515&#34;:{&#34;@type&#34;:&#34;cep/components/button&#34;,&#34;repo:modifyDate&#34;:&#34;2024-08-20T17:07:20Z&#34;,&#34;xdm:linkURL&#34;:&#34;https://www.tiktok.com/@powerade_ca&#34;}}" href="https://www.tiktok.com/@powerade_ca" target="_blank">
    
    <span class="cmp-button__icon cmp-button__icon--tiktok" aria-hidden="true"></span>

    
</a>
</div>
<div class="button button--secondary button--icon">
<a id="button-c5c1070aea" class="cmp-button" aria-label="facebook" title="facebook" data-cmp-clickable data-cmp-data-layer="{&#34;button-c5c1070aea&#34;:{&#34;@type&#34;:&#34;cep/components/button&#34;,&#34;repo:modifyDate&#34;:&#34;2023-09-30T13:50:15Z&#34;,&#34;xdm:linkURL&#34;:&#34;https://www.facebook.com/PoweradeCanada/&#34;}}" href="https://www.facebook.com/PoweradeCanada/" target="_blank">
    
    <span class="cmp-button__icon cmp-button__icon--facebook" aria-hidden="true"></span>

    
</a>
</div>
<div class="button button--secondary button--icon">
<a id="button-2770ce1039" class="cmp-button" aria-label="youtube" title="youtube" data-cmp-clickable data-cmp-data-layer="{&#34;button-2770ce1039&#34;:{&#34;@type&#34;:&#34;cep/components/button&#34;,&#34;repo:modifyDate&#34;:&#34;2023-09-30T13:51:42Z&#34;,&#34;xdm:linkURL&#34;:&#34;https://www.youtube.com/channel/UCeN8sAQ1OcmxT6Akmne2bhQ&#34;}}" href="https://www.youtube.com/channel/UCeN8sAQ1OcmxT6Akmne2bhQ" target="_blank">
    
    <span class="cmp-button__icon cmp-button__icon--youtube" aria-hidden="true"></span>

    
</a>
</div>

        
    </div>

</div>

        
    </div>

</div>

        
    </div>

</div>
    
    

    

</div>

        
    </div>

</div>

        
    </div>

</main>
<footer class="experiencefragment">

    <div class="cmp-experiencefragment cmp-experiencefragment--footer">


    
    
    
    
    <div id="container-fd1f2286ab" class="cmp-container">

        

        
        <div class="container responsivegrid footer-wrapper">

    
    
    
    
    <div id="container-9e79ae9201" class="cmp-container">

        

        
        <div class="container responsivegrid footer__content">

    
    
    
    
    <div id="container-da9be74b7a" class="cmp-container">

        

        
        <div class="adaptiveImage image footer__logo"><div data-asset-id="5398abf0-f155-4876-b2cb-86bc12d7226b" id="adaptiveImage-746da0aed6" data-cmp-data-layer="{&#34;adaptiveImage-746da0aed6&#34;:{&#34;@type&#34;:&#34;cep/components/adaptiveImage&#34;,&#34;repo:modifyDate&#34;:&#34;2023-08-14T15:01:09Z&#34;,&#34;xdm:linkURL&#34;:&#34;/ca/en.html&#34;,&#34;image&#34;:{&#34;repo:id&#34;:&#34;5398abf0-f155-4876-b2cb-86bc12d7226b&#34;,&#34;repo:modifyDate&#34;:&#34;2023-08-14T14:59:09Z&#34;,&#34;@type&#34;:&#34;image/png&#34;,&#34;repo:path&#34;:&#34;/content/dam/onexp/ca/en/header/canada-coca-cola-logo_white_600x96 2.png&#34;}}}" class="cmp-adaptive-image cmp-image" itemscope itemtype="http://schema.org/ImageObject">
    <a class="cmp-image__link" data-cmp-clickable href="/ca/en">
        
    <picture>
        
        
        <source width="600" height="96"/>
        <img src="/content/dam/onexp/ca/en/header/canada-coca-cola-logo_white_600x96%202.png" loading="lazy" class="cmp-image__image" itemprop="contentUrl" alt="The Coca Cola Company Logo"/>
    </picture>


        
    </a>
    
    
</div>

    

</div>
<div class="separator">
<div id="separator-2fda2f6067" class="cmp-separator">
    <hr class="cmp-separator__horizontal-rule"/>
</div></div>
<div class="container responsivegrid footer__navigation">

    
    
    
    
    <div id="container-7eb6a543b9" class="cmp-container">

        

        
        <div class="text footer__mobile-accordion">
<div data-cmp-data-layer="{&#34;text-9584a7bc3e&#34;:{&#34;@type&#34;:&#34;cep/components/text&#34;,&#34;repo:modifyDate&#34;:&#34;2024-05-21T18:15:11Z&#34;,&#34;xdm:text&#34;:&#34;&lt;h2>About us&lt;/h2>\r\n&lt;ul>\r\n&lt;li>&lt;a href=\&#34;https://www.coca-colacompany.com/about-us\&#34; target=\&#34;_blank\&#34;>Our Company&lt;/a>&lt;/li>\r\n&lt;li>&lt;a href=\&#34;/content/onexp/ca/en/media-center.html\&#34;>Media Centre&lt;/a>&lt;/li>\r\n&lt;li>&lt;a href=\&#34;/content/onexp/ca/en/about-us/history.html\&#34;>History&lt;/a>&lt;/li>\r\n&lt;li>&lt;a href=\&#34;https://www.coca-colacompany.com/careers\&#34; target=\&#34;_blank\&#34; rel=\&#34;noopener noreferrer\&#34;>Careers&lt;/a>&lt;/li>\r\n&lt;/ul>\r\n&#34;}}" id="text-9584a7bc3e" class="cmp-text">
    <h2>About us</h2>
<ul><li><a href="https://www.coca-colacompany.com/about-us" target="_blank" rel="noopener noreferrer">Our Company</a></li><li><a href="/ca/en/media-centre">Media Centre</a></li><li><a href="/ca/en/about-us/history">History</a></li><li><a href="https://www.coca-colacompany.com/careers" target="_blank" rel="noopener noreferrer">Careers</a></li></ul>

</div>

    

</div>
<div class="text footer__mobile-accordion">
<div data-cmp-data-layer="{&#34;text-584928fe89&#34;:{&#34;@type&#34;:&#34;cep/components/text&#34;,&#34;repo:modifyDate&#34;:&#34;2025-01-06T18:29:26Z&#34;,&#34;xdm:text&#34;:&#34;&lt;h2>Need help?&lt;/h2>\r\n&lt;ul>\r\n&lt;li>&lt;a href=\&#34;/content/onexp/ca/en/about-us/faq.html\&#34;>FAQ&lt;/a>&lt;/li>\r\n&lt;li>&lt;a href=\&#34;/content/onexp/ca/en/sitemap.html\&#34;>Sitemap&lt;/a>&lt;/li>\r\n&lt;li>&lt;a href=\&#34;/content/onexp/ca/en/about-us/contact-us.html\&#34;>Contact Us&lt;/a>&lt;/li>\r\n&lt;li>&lt;a href=\&#34;https://login.naou.coca-cola.com/\&#34;>Sign up or Login&lt;/a>&lt;/li>\r\n&lt;/ul>\r\n&#34;}}" id="text-584928fe89" class="cmp-text">
    <h2>Need help?</h2>
<ul><li><a href="/ca/en/about-us/faq">FAQ</a></li><li><a href="/ca/en/sitemap">Sitemap</a></li><li><a href="/ca/en/about-us/contact-us">Contact Us</a></li><li><a href="https://login.naou.coca-cola.com/">Sign up or Login</a></li></ul>

</div>

    

</div>
<div class="text footer__mobile-accordion">
<div data-cmp-data-layer="{&#34;text-4a55a4f7dc&#34;:{&#34;@type&#34;:&#34;cep/components/text&#34;,&#34;repo:modifyDate&#34;:&#34;2024-09-02T14:10:07Z&#34;,&#34;xdm:text&#34;:&#34;&lt;h2>Legal&lt;/h2>\r\n&lt;ul>\r\n&lt;li>&lt;a href=\&#34;/content/onexp/ca/en/legal/terms-of-use.html\&#34;>Terms of Service&lt;/a>&lt;/li>\r\n&lt;li>&lt;a href=\&#34;/content/onexp/ca/en/legal/privacy-policy.html\&#34;>Privacy Policy&lt;/a>&lt;/li>\r\n&lt;li>&lt;a href=\&#34;/content/onexp/ca/en/legal/accessibility-policy.html\&#34;>Accessibility Policy&lt;/a>&lt;/li>\r\n&lt;li>&lt;a href=\&#34;/content/onexp/ca/en/legal/cookies-policy.html\&#34;>Cookie Policy&lt;/a>&lt;/li>\r\n&lt;li>&lt;a href=\&#34;#js-open-onetrust-preference-center\&#34;>Cookie Settings&lt;/a>&lt;/li>\r\n&lt;li>&lt;a href=\&#34;/content/onexp/ca/en/legal/promotion-legal.html\&#34;>All Promotion Official Rules&lt;/a>&lt;/li>\r\n&lt;li>&lt;a href=\&#34;/content/dam/onexp/ca/en/legal/ccl-statement-for-fighting-against-forced-and-child-labour-in-supply-chains-act-2024.pdf\&#34; target=\&#34;_blank\&#34; rel=\&#34;noopener noreferrer\&#34;>Forced &amp;amp; Child Labour Statement&lt;/a>&lt;/li>\r\n&lt;li>&lt;a href=\&#34;https://privacyportal.onetrust.com/webform/************************************/12c8211c-2713-4e67-960a-e28a3dc1320c\&#34; target=\&#34;_blank\&#34; rel=\&#34;noopener noreferrer\&#34;>Do Not Sell or Share My Information&lt;/a>&lt;/li>\r\n&lt;/ul>\r\n&#34;}}" id="text-4a55a4f7dc" class="cmp-text">
    <h2>Legal</h2>
<ul><li><a href="/ca/en/legal/terms-of-service">Terms of Service</a></li><li><a href="/ca/en/legal/privacy-policy">Privacy Policy</a></li><li><a href="/ca/en/legal/accessibility-policy">Accessibility Policy</a></li><li><a href="/ca/en/legal/cookies-policy">Cookie Policy</a></li><li><a href="#js-open-onetrust-preference-center">Cookie Settings</a></li><li><a href="/ca/en/legal/promotion-legal">All Promotion Official Rules</a></li><li><a href="/content/dam/onexp/ca/en/legal/ccl-statement-for-fighting-against-forced-and-child-labour-in-supply-chains-act-2024.pdf" target="_blank" rel="noopener noreferrer">Forced &amp; Child Labour Statement</a></li><li><a href="https://privacyportal.onetrust.com/webform/************************************/12c8211c-2713-4e67-960a-e28a3dc1320c" target="_blank" rel="noopener noreferrer">Do Not Sell or Share My Information</a></li></ul>

</div>

    

</div>

        
    </div>

</div>
<div class="container responsivegrid footer__social-media">

    
    
    
    
    <div id="container-94211d3c0e" class="cmp-container">

        

        
        <div class="button footer__button--social-media">
<a id="button-541f2c50e1" class="cmp-button" aria-label="X" data-cmp-clickable data-cmp-data-layer="{&#34;button-541f2c50e1&#34;:{&#34;@type&#34;:&#34;cep/components/button&#34;,&#34;repo:modifyDate&#34;:&#34;2025-03-04T12:29:05Z&#34;,&#34;dc:title&#34;:&#34;X&#34;,&#34;xdm:linkURL&#34;:&#34;https://x.com/CocaColaCo_ca&#34;}}" href="https://x.com/CocaColaCo_ca" target="_blank">
    
    <span class="cmp-button__icon cmp-button__icon--x-social" aria-hidden="true"></span>

    <span class="cmp-button__text">X</span>
</a>
</div>
<div class="button footer__button--social-media">
<a id="button-39b0f508e9" class="cmp-button" aria-label="instagram" data-cmp-clickable data-cmp-data-layer="{&#34;button-39b0f508e9&#34;:{&#34;@type&#34;:&#34;cep/components/button&#34;,&#34;repo:modifyDate&#34;:&#34;2023-05-12T03:00:29Z&#34;,&#34;dc:title&#34;:&#34;Instagram&#34;,&#34;xdm:linkURL&#34;:&#34;https://www.instagram.com/cocacolaco_ca/&#34;}}" href="https://www.instagram.com/cocacolaco_ca/" target="_blank">
    
    <span class="cmp-button__icon cmp-button__icon--instagram" aria-hidden="true"></span>

    <span class="cmp-button__text">Instagram</span>
</a>
</div>
<div class="button footer__button--social-media">
<a id="button-6fdb79b1f2" class="cmp-button" aria-label="youtube" data-cmp-clickable data-cmp-data-layer="{&#34;button-6fdb79b1f2&#34;:{&#34;@type&#34;:&#34;cep/components/button&#34;,&#34;repo:modifyDate&#34;:&#34;2023-05-12T03:00:50Z&#34;,&#34;dc:title&#34;:&#34;Youtube&#34;,&#34;xdm:linkURL&#34;:&#34;https://www.youtube.com/playlist?list=PLXSGSlneuyeHRwu57JX4HLYanuwwumTtd&#34;}}" href="https://www.youtube.com/playlist?list=PLXSGSlneuyeHRwu57JX4HLYanuwwumTtd" target="_blank">
    
    <span class="cmp-button__icon cmp-button__icon--youtube" aria-hidden="true"></span>

    <span class="cmp-button__text">Youtube</span>
</a>
</div>
<div class="button footer__button--social-media">
<a id="button-11ea024a10" class="cmp-button" aria-label="facebook" data-cmp-clickable data-cmp-data-layer="{&#34;button-11ea024a10&#34;:{&#34;@type&#34;:&#34;cep/components/button&#34;,&#34;repo:modifyDate&#34;:&#34;2023-05-12T03:01:08Z&#34;,&#34;dc:title&#34;:&#34;Facebook&#34;,&#34;xdm:linkURL&#34;:&#34;https://www.facebook.com/TheCocaColaCoCanada/&#34;}}" href="https://www.facebook.com/TheCocaColaCoCanada/" target="_blank">
    
    <span class="cmp-button__icon cmp-button__icon--facebook" aria-hidden="true"></span>

    <span class="cmp-button__text">Facebook</span>
</a>
</div>

        
    </div>

</div>
<div class="separator">
<div id="separator-ff051ef45d" class="cmp-separator">
    <hr class="cmp-separator__horizontal-rule"/>
</div></div>
<div class="container responsivegrid footer__mobile-apps">

    
    
    
    
    <div id="container-9a3ff4eee2" class="cmp-container">

        

        
        
        
    </div>

</div>
<div class="text footer__bottom--privacy">
<div data-cmp-data-layer="{&#34;text-49eb8e03a4&#34;:{&#34;@type&#34;:&#34;cep/components/text&#34;,&#34;repo:modifyDate&#34;:&#34;2025-03-04T16:42:26Z&#34;,&#34;xdm:text&#34;:&#34;&lt;p>®&amp;nbsp;Coca-Cola Ltd., used under license.&lt;/p>\r\n&#34;}}" id="text-49eb8e03a4" class="cmp-text">
    <p>® Coca&#x2011;Cola Ltd., used under license.</p>

</div>

    

</div>
<div class="text">


    

</div>

        
    </div>

</div>

        
    </div>

</div>

        
    </div>

</div>
    
    

    

</footer>

        
    </div>

</div>





    <div id="date-format-config" data-date-format="MM/dd/yyyy"></div>


<div id="age-gate-config"></div>

<div id="theme-url" data-theme-url="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd"></div>





<div id="labels" data-labels="{&#34;prevLabel&#34;:&#34;Prev&#34;,&#34;loginButtonLabel&#34;:&#34;&#34;,&#34;unsupportedBrowserHeader&#34;:&#34;Browser Not Supported&#34;,&#34;carouselPaginationItemLabel&#34;:&#34;Go to slide&#34;,&#34;loginModalHeading&#34;:&#34;&#34;,&#34;liveBadge&#34;:&#34;Live&#34;,&#34;closeButton&#34;:&#34;Close&#34;,&#34;share&#34;:&#34;Share&#34;,&#34;placeholder&#34;:&#34;&#34;,&#34;notificationsPermissionsNotGrantedErrorHeader&#34;:&#34;&#34;,&#34;iOSInstructionsDescription&#34;:&#34;\u003col\u003e\n\u003cli\u003eClick on\u0026nbsp;{{ios_share}}\u003c/li\u003e\n\u003cli\u003eChoose \u0026quot;Add to Home Screen\u0026quot;\u003c/li\u003e\n\u003cli\u003eOpen the application from your homescreen\u003c/li\u003e\n\u003cli\u003eClick subscribe again\u003c/li\u003e\n\u003c/ol\u003e\n&#34;,&#34;signupButtonLabel&#34;:&#34;&#34;,&#34;loginModalCloseButtonLabel&#34;:&#34;&#34;,&#34;months&#34;:&#34;&#34;,&#34;notificationsUnknownErrorDescription&#34;:&#34;\u003cp\u003eWe couldn\u0027t subscribe you to push notifications. Please try again / check in another browser.\u003c/p\u003e\n&#34;,&#34;confirmationHeader&#34;:&#34;You\u0027re Subscribed!&#34;,&#34;unknownErrorHeader&#34;:&#34;Unexpected Error Occurred&#34;,&#34;iOSInstructionsImage&#34;:&#34;/content/dam/onexp/global/icons/coke-logo.svg&#34;,&#34;linkCopied&#34;:&#34;Link copied&#34;,&#34;notificationsPermissionsNotGrantedErrorDescription&#34;:&#34;&#34;,&#34;copyCodeLabel&#34;:&#34;Copy Code&#34;,&#34;loginModalDescription&#34;:&#34;&#34;,&#34;showMore&#34;:&#34;Show More&#34;,&#34;notificationsUnknownErrorHeader&#34;:&#34;Ups! Something went wrong&#34;,&#34;nextLabel&#34;:&#34;Next&#34;,&#34;days&#34;:&#34;&#34;,&#34;unknownErrorMsgLabel&#34;:&#34;Unexpected error occurred, try again later&#34;,&#34;showLess&#34;:&#34;Show Less&#34;,&#34;showModal&#34;:&#34;Show More&#34;,&#34;iOSInstructionsHeader&#34;:&#34;Install the app to receive notifications&#34;,&#34;unsupportedBrowserDescription&#34;:&#34;\u003cp\u003eThis browser doesn\u0027t support notifications. If you opened the page inside Social Media app, please open the page in a standard browser. Please also make sure that you\u0027re running at least iOS 16.4+ / latest Android version.\u003c/p\u003e\n&#34;,&#34;codeClickedLabel&#34;:&#34;Code copied&#34;,&#34;confirmationDescription&#34;:&#34;\u003cp\u003eYou have successfully subscribed to receive notifications.\u003c/p\u003e\n&#34;}"></div>

    
    
    <script src="/etc.clientlibs/core/wcm/components/commons/site/clientlibs/container.lc-0a6aff292f5cc42142779cde92054524-lc.min.js"></script>
<script src="/etc.clientlibs/onexp/clientlibs/clientlib-base.lc-c0088e3136a535681ba43f65b08abc2b-lc.min.js"></script>


    

    

    

    
    <script src="https://www.coca-cola.com/onexp-theme/dd60d3b2080b61c0561f32c6755e5615f30e9f711403664b91e8aa68280bf4dd/theme.js" async type="text/javascript"></script>
    
    


</body>
</html>
