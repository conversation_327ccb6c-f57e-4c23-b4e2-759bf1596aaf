"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alog<PERSON>eader,
  <PERSON>alogTitle,
  <PERSON>alogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { Plus, Pencil, Trash2, Eye } from "lucide-react"
import { Badge } from "@/components/ui/badge"

// Sample pages data
const initialPages = [
  {
    id: 1,
    title: "About Us",
    slug: "about-us",
    lastUpdated: "2023-05-10",
    status: true,
    type: "Standard",
  },
  {
    id: 2,
    title: "Contact Us",
    slug: "contact-us",
    lastUpdated: "2023-06-15",
    status: true,
    type: "Form",
  },
  {
    id: 3,
    title: "Privacy Policy",
    slug: "privacy-policy",
    lastUpdated: "2023-04-20",
    status: true,
    type: "Legal",
  },
  {
    id: 4,
    title: "Terms of Service",
    slug: "terms-of-service",
    lastUpdated: "2023-04-20",
    status: true,
    type: "Legal",
  },
  {
    id: 5,
    title: "FAQ",
    slug: "faq",
    lastUpdated: "2023-07-05",
    status: true,
    type: "Standard",
  },
  {
    id: 6,
    title: "Shipping Information",
    slug: "shipping-info",
    lastUpdated: "2023-06-30",
    status: false,
    type: "Standard",
  },
]

export default function AdminContentPages() {
  const [pages, setPages] = useState(initialPages)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [newPage, setNewPage] = useState({
    title: "",
    slug: "",
    content: "",
    status: true,
    type: "Standard",
  })

  const handleAddPage = () => {
    const page = {
      id: pages.length + 1,
      ...newPage,
      lastUpdated: new Date().toISOString().split("T")[0],
    }
    setPages([...pages, page])
    setNewPage({
      title: "",
      slug: "",
      content: "",
      status: true,
      type: "Standard",
    })
    setIsAddDialogOpen(false)
  }

  const togglePageStatus = (id: number) => {
    setPages(pages.map((page) => (page.id === id ? { ...page, status: !page.status } : page)))
  }

  const deletePage = (id: number) => {
    setPages(pages.filter((page) => page.id !== id))
  }

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const title = e.target.value
    const slug = title
      .toLowerCase()
      .replace(/\s+/g, "-")
      .replace(/[^a-z0-9-]/g, "")
    setNewPage({ ...newPage, title, slug })
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Page Management</CardTitle>
            <CardDescription>Manage static pages for your store</CardDescription>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Page
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[650px]">
              <DialogHeader>
                <DialogTitle>Add New Page</DialogTitle>
                <DialogDescription>Create a new static page for your store.</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="title" className="text-right">
                    Title
                  </Label>
                  <Input id="title" value={newPage.title} onChange={handleTitleChange} className="col-span-3" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="slug" className="text-right">
                    Slug
                  </Label>
                  <Input id="slug" value={newPage.slug} />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="content" className="text-right">
                    Content
                  </Label>
                  <Textarea
                    id="content"
                    value={newPage.content}
                    onChange={(e) => setNewPage({ ...newPage, content: e.target.value })}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="status" className="text-right">
                    Status
                  </Label>
                  <Switch
                    id="status"
                    checked={newPage.status}
                    onCheckedChange={(checked) => setNewPage({ ...newPage, status: checked })}
                    className="col-span-3"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button type="button" onClick={handleAddPage}>
                  Add Page
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Title</TableHead>
              <TableHead>Slug</TableHead>
              <TableHead>Last Updated</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Type</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {pages.map((page) => (
              <TableRow key={page.id}>
                <TableCell>{page.title}</TableCell>
                <TableCell>{page.slug}</TableCell>
                <TableCell>{page.lastUpdated}</TableCell>
                <TableCell>{page.status ? <Badge variant="outline">Published</Badge> : <Badge>Draft</Badge>}</TableCell>
                <TableCell>{page.type}</TableCell>
                <TableCell className="text-right">
                  <Button variant="ghost" size="sm">
                    <Eye className="mr-2 h-4 w-4" />
                    View
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Pencil className="mr-2 h-4 w-4" />
                    Edit
                  </Button>
                  <Button variant="ghost" size="sm" onClick={() => deletePage(page.id)}>
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </Button>
                  <Switch checked={page.status} onCheckedChange={() => togglePageStatus(page.id)} />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

