"use client"

import { useState, useEffect } from "react"
import { ProductCard } from "@/components/product-card"

// Mock product data for search - in a real app, this would come from an API
const allProducts = [
  {
    id: "cantu-shea-butter",
    title: "Cantu Shea Butter Tea Tree Oil Leave-In Conditioner",
    price: 140000,
    currency: "UGX",
    image: "/placeholder.svg?height=300&width=300",
    discount: 15,
    rating: 4.5,
    category: "Beauty & Health",
  },
  {
    id: "suave-essentials",
    title: "Suave Essentials Gentle Body Wash Ocean Breeze",
    price: 145000,
    currency: "UGX",
    image: "/placeholder.svg?height=300&width=300",
    discount: 20,
    rating: 4.2,
    category: "Beauty & Health",
  },
  {
    id: "aveeno-pure",
    title: "Aveeno Pure Flaming Body Wash with Oatmeal",
    price: 135000,
    currency: "UGX",
    image: "/placeholder.svg?height=300&width=300",
    discount: 10,
    rating: 4.7,
    category: "Beauty & Health",
  },
  {
    id: "herbal-essentials",
    title: "Herbal Essentials Hydrating Shampoo with Aloe Vera",
    price: 150000,
    currency: "UGX",
    image: "/placeholder.svg?height=300&width=300",
    discount: 25,
    rating: 4.3,
    category: "Beauty & Health",
  },
  {
    id: "neutrogena-revitalizing",
    title: "Neutrogena Revitalizing Body Wash with Vitamin E",
    price: 125000,
    currency: "UGX",
    image: "/placeholder.svg?height=300&width=300",
    discount: 15,
    rating: 4.6,
    category: "Beauty & Health",
  },
  {
    id: "oral-b-electric",
    title: "Oral-B Pro 1000 Electric Toothbrush",
    price: 350000,
    currency: "UGX",
    image: "/placeholder.svg?height=300&width=300",
    discount: 10,
    rating: 4.9,
    category: "Oral Hygiene",
  },
  {
    id: "colgate-total",
    title: "Colgate Total Whitening Toothpaste",
    price: 25000,
    currency: "UGX",
    image: "/placeholder.svg?height=300&width=300",
    discount: 0,
    rating: 4.7,
    category: "Oral Hygiene",
  },
  {
    id: "sensodyne",
    title: "Sensodyne Pronamel Gentle Whitening Toothpaste",
    price: 30000,
    currency: "UGX",
    image: "/placeholder.svg?height=300&width=300",
    discount: 5,
    rating: 4.8,
    category: "Oral Hygiene",
  },
  {
    id: "egyptian-pillow",
    title: "Egyptian Cotton King Size Pillow",
    price: 185000,
    currency: "UGX",
    image: "/placeholder.svg?height=300&width=300",
    discount: 0,
    rating: 4.8,
    category: "Beddings",
  },
  {
    id: "bamboo-duvet",
    title: "Bamboo Duvet Cover Queen Size",
    price: 450000,
    currency: "UGX",
    image: "/placeholder.svg?height=300&width=300",
    discount: 10,
    rating: 4.9,
    category: "Beddings",
  },
]

interface SearchResultsProps {
  query: string
}

export function SearchResults({ query }: SearchResultsProps) {
  const [results, setResults] = useState<typeof allProducts>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulate API search delay
    setIsLoading(true)
    const timer = setTimeout(() => {
      if (query) {
        const filtered = allProducts.filter(
          (product) =>
            product.title.toLowerCase().includes(query.toLowerCase()) ||
            product.category.toLowerCase().includes(query.toLowerCase()),
        )
        setResults(filtered)
      } else {
        setResults(allProducts)
      }
      setIsLoading(false)
    }, 500)

    return () => clearTimeout(timer)
  }, [query])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    )
  }

  if (results.length === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium mb-2">No results found</h3>
        <p className="text-muted-foreground">
          We couldn't find any products matching "{query}". Please try a different search term.
        </p>
      </div>
    )
  }

  return (
    <div>
      <p className="text-sm text-muted-foreground mb-6">
        Found {results.length} result{results.length !== 1 ? "s" : ""} for "{query}"
      </p>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {results.map((product) => (
          <ProductCard
            key={product.id}
            id={product.id}
            title={product.title}
            price={product.price}
            currency={product.currency}
            image={product.image}
            discount={product.discount}
            rating={product.rating}
            href={`/products/${product.id}`}
          />
        ))}
      </div>
    </div>
  )
}

