import type { ReactNode } from "react"
import { redirect } from "next/navigation"

import { AdminSidebar } from "@/components/admin/admin-sidebar"
import { AdminHeader } from "@/components/admin/admin-header"

// This would normally check for authentication and admin role
const checkAdminAuth = () => {
  // Mock authentication check - in a real app, this would verify the user is logged in and has admin role
  const isAuthenticated = true
  const isAdmin = true

  return { isAuthenticated, isAdmin }
}

export default function AdminLayout({ children }: { children: ReactNode }) {
  const { isAuthenticated, isAdmin } = checkAdminAuth()

  // Redirect if not authenticated or not an admin
  if (!isAuthenticated || !isAdmin) {
    redirect("/login")
  }

  return (
    <div className="min-h-screen bg-muted/30 flex">
      <AdminSidebar />
      <div className="flex-1 flex flex-col">
        <AdminHeader />
        <main className="flex-1 p-6">{children}</main>
      </div>
    </div>
  )
}

