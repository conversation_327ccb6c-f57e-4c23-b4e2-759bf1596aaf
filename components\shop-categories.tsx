import Link from "next/link"
import Image from "next/image"
import { ChevronRight } from "lucide-react"

import { Card, CardContent } from "@/components/ui/card"

export function ShopCategories() {
  const categories = [
    { id: "beauty", name: "Beauty & Health", image: "/placeholder.svg?height=200&width=200", count: 120 },
    { id: "oral", name: "Oral Hygiene", image: "/placeholder.svg?height=200&width=200", count: 45 },
    { id: "bedding", name: "Beddings", image: "/placeholder.svg?height=200&width=200", count: 78 },
    { id: "footwear", name: "Footwear", image: "/placeholder.svg?height=200&width=200", count: 96 },
    { id: "perfumes", name: "Perfumes", image: "/placeholder.svg?height=200&width=200", count: 64 },
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">Shop by Category</h2>
        <Link href="/categories" className="flex items-center text-sm font-medium text-primary">
          View All Categories
          <ChevronRight className="ml-1 h-4 w-4" />
        </Link>
      </div>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4">
        {categories.map((category) => (
          <Link key={category.id} href={`/categories/${category.id}`}>
            <Card className="overflow-hidden transition-all duration-300 hover:shadow-md group">
              <CardContent className="p-0">
                <div className="relative">
                  <div className="aspect-square overflow-hidden">
                    <Image
                      src={category.image || "/placeholder.svg"}
                      alt={category.name}
                      width={200}
                      height={200}
                      className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
                    />
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-t from-background/90 to-transparent flex items-end p-4">
                    <div>
                      <h3 className="font-medium text-base">{category.name}</h3>
                      <p className="text-xs text-muted-foreground">{category.count} products</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>
    </div>
  )
}

