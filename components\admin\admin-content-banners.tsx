"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Switch } from "@/components/ui/switch"
import { Plus, Pencil, Trash2, Eye } from "lucide-react"

// Sample banner data
const initialBanners = [
  {
    id: 1,
    title: "Summer Sale",
    image: "/placeholder.svg?height=200&width=800",
    location: "Home Page",
    startDate: "2023-06-01",
    endDate: "2023-08-31",
    status: true,
  },
  {
    id: 2,
    title: "New Arrivals",
    image: "/placeholder.svg?height=200&width=800",
    location: "Category Page",
    startDate: "2023-05-15",
    endDate: "2023-12-31",
    status: true,
  },
  {
    id: 3,
    title: "Holiday Special",
    image: "/placeholder.svg?height=200&width=800",
    location: "Shop Page",
    startDate: "2023-11-01",
    endDate: "2023-12-25",
    status: false,
  },
]

export default function AdminContentBanners() {
  const [banners, setBanners] = useState(initialBanners)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [newBanner, setNewBanner] = useState({
    title: "",
    location: "",
    startDate: "",
    endDate: "",
    status: true,
  })

  const handleAddBanner = () => {
    const banner = {
      id: banners.length + 1,
      ...newBanner,
      image: "/placeholder.svg?height=200&width=800",
    }
    setBanners([...banners, banner])
    setNewBanner({
      title: "",
      location: "",
      startDate: "",
      endDate: "",
      status: true,
    })
    setIsAddDialogOpen(false)
  }

  const toggleBannerStatus = (id: number) => {
    setBanners(banners.map((banner) => (banner.id === id ? { ...banner, status: !banner.status } : banner)))
  }

  const deleteBanner = (id: number) => {
    setBanners(banners.filter((banner) => banner.id !== id))
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Banner Management</CardTitle>
            <CardDescription>Manage promotional banners across your store</CardDescription>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Banner
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[550px]">
              <DialogHeader>
                <DialogTitle>Add New Banner</DialogTitle>
                <DialogDescription>Create a new promotional banner for your store.</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="title" className="text-right">
                    Title
                  </Label>
                  <Input
                    id="title"
                    value={newBanner.title}
                    onChange={(e) => setNewBanner({ ...newBanner, title: e.target.value })}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="location" className="text-right">
                    Location
                  </Label>
                  <Select
                    value={newBanner.location}
                    onValueChange={(value) => setNewBanner({ ...newBanner, location: value })}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select location" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Home Page">Home Page</SelectItem>
                      <SelectItem value="Shop Page">Shop Page</SelectItem>
                      <SelectItem value="Category Page">Category Page</SelectItem>
                      <SelectItem value="Product Page">Product Page</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="startDate" className="text-right">
                    Start Date
                  </Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={newBanner.startDate}
                    onChange={(e) => setNewBanner({ ...newBanner, startDate: e.target.value })}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="endDate" className="text-right">
                    End Date
                  </Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={newBanner.endDate}
                    onChange={(e) => setNewBanner({ ...newBanner, endDate: e.target.value })}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="banner-image" className="text-right">
                    Banner Image
                  </Label>
                  <Input id="banner-image" type="file" className="col-span-3" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="status" className="text-right">
                    Active
                  </Label>
                  <div className="col-span-3 flex items-center space-x-2">
                    <Switch
                      id="status"
                      checked={newBanner.status}
                      onCheckedChange={(checked) => setNewBanner({ ...newBanner, status: checked })}
                    />
                    <Label htmlFor="status">{newBanner.status ? "Active" : "Inactive"}</Label>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddBanner}>Save Banner</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Banner</TableHead>
              <TableHead>Title</TableHead>
              <TableHead>Location</TableHead>
              <TableHead>Duration</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {banners.map((banner) => (
              <TableRow key={banner.id}>
                <TableCell>
                  <img
                    src={banner.image || "/placeholder.svg"}
                    alt={banner.title}
                    className="h-12 w-24 rounded object-cover"
                  />
                </TableCell>
                <TableCell className="font-medium">{banner.title}</TableCell>
                <TableCell>{banner.location}</TableCell>
                <TableCell>
                  {banner.startDate} to {banner.endDate}
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <Switch checked={banner.status} onCheckedChange={() => toggleBannerStatus(banner.id)} />
                    <span>{banner.status ? "Active" : "Inactive"}</span>
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" size="icon">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon">
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon" onClick={() => deleteBanner(banner.id)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="text-sm text-muted-foreground">Showing {banners.length} banners</div>
      </CardFooter>
    </Card>
  )
}

