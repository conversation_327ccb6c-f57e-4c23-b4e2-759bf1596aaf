import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Plus } from "lucide-react"

export function AdminMarketingCoupons() {
  return (
    <div className="grid gap-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">Coupons</h2>
        <Button>
          <Plus className="mr-2 h-4 w-4" /> Create Coupon
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Active Coupons</CardTitle>
          <CardDescription>
            Manage your store's coupon codes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Code</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Discount</TableHead>
                <TableHead>Used</TableHead>
                <TableHead>Limit</TableHead>
                <TableHead>Expiry</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {[
                {
                  code: "SUMMER20",
                  description: "Summer sale discount",
                  discount: "20% off",
                  used: "45",
                  limit: "100",
                  expiry: "Aug 31, 2023",
                  status: "Active",
                },
                {
                  code: "WELCOME10",
                  description: "New customer discount",
                  discount: "$10 off",
                  used: "128",
                  limit: "500",
                  expiry: "Dec 31, 2023",
                  status: "Active",
                },
                {
                  code: "FREESHIP",
                  description: "Free shipping on orders over $50",
                  discount: "Free shipping",
                  used: "76",
                  limit: "200",
                  expiry: "Jul 15, 2023",
                  status: "Active",
                },
                {
                  code: "FLASH25",
                  description: "Weekend flash sale",
                  discount: "25% off",
                  used: "32",
                  limit: "50",
                  expiry: "Jun 30, 2023",
                  status: "Almost Full",
                },
                {
                  code: "LOYALTY15",
                  description: "Loyalty program members",
                  discount: "15% off",
                  used: "89",
                  limit: "No limit",
                  expiry: "Dec 31, 2023",
                  status: "Active",
                },
              ].map((coupon, i) => (
                <TableRow key={i}>
                  <TableCell className="font-medium">{coupon.code}</TableCell>
                  <TableCell>{coupon.description}</TableCell>
                  <TableCell>{coupon.discount}</TableCell>
                  <TableCell>{coupon.used}</TableCell>
                  <TableCell>{coupon.limit}</TableCell>
                  <TableCell>{coupon.expiry}</TableCell>
                  <TableCell>
                    <Badge 
                      variant={coupon.status === "Active" ? "default" : "secondary"}
                    >
                      {coupon.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className\

