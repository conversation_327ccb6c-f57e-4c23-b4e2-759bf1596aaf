<!DOCTYPE HTML><html lang="en"><head>
<meta charset="utf-8"><script type="text/javascript">(window.NREUM||(NREUM={})).init={privacy:{cookies_enabled:true},ajax:{deny_list:["bam.nr-data.net"]},distributed_tracing:{enabled:true}};(window.NREUM||(NREUM={})).loader_config={agentID:"**********",accountID:"3693860",trustKey:"3693860",xpid:"VwAOUl5VCBABVFhVAQUFUFMG",licenseKey:"NRJS-77b9f90983038b4dd22",applicationID:"**********"};;/*! For license information please see nr-loader-spa-1.288.1.min.js.LICENSE.txt */
(()=>{var e,t,r={8122:(e,t,r)=>{"use strict";r.d(t,{a:()=>i});var n=r(944);function i(e,t){try{if(!e||"object"!=typeof e)return(0,n.R)(3);if(!t||"object"!=typeof t)return(0,n.R)(4);const r=Object.create(Object.getPrototypeOf(t),Object.getOwnPropertyDescriptors(t)),o=0===Object.keys(r).length?e:r;for(let a in o)if(void 0!==e[a])try{if(null===e[a]){r[a]=null;continue}Array.isArray(e[a])&&Array.isArray(t[a])?r[a]=Array.from(new Set([...e[a],...t[a]])):"object"==typeof e[a]&&"object"==typeof t[a]?r[a]=i(e[a],t[a]):r[a]=e[a]}catch(e){(0,n.R)(1,e)}return r}catch(e){(0,n.R)(2,e)}}},2555:(e,t,r)=>{"use strict";r.d(t,{Vp:()=>c,fn:()=>s,x1:()=>u});var n=r(384),i=r(8122);const o={beacon:n.NT.beacon,errorBeacon:n.NT.errorBeacon,licenseKey:void 0,applicationID:void 0,sa:void 0,queueTime:void 0,applicationTime:void 0,ttGuid:void 0,user:void 0,account:void 0,product:void 0,extra:void 0,jsAttributes:{},userAttributes:void 0,atts:void 0,transactionName:void 0,tNamePlain:void 0},a={};function s(e){try{const t=c(e);return!!t.licenseKey&&!!t.errorBeacon&&!!t.applicationID}catch(e){return!1}}function c(e){if(!e)throw new Error("All info objects require an agent identifier!");if(!a[e])throw new Error("Info for ".concat(e," was never set"));return a[e]}function u(e,t){if(!e)throw new Error("All info objects require an agent identifier!");a[e]=(0,i.a)(t,o);const r=(0,n.nY)(e);r&&(r.info=a[e])}},9417:(e,t,r)=>{"use strict";r.d(t,{D0:()=>h,gD:()=>g,xN:()=>p});var n=r(3333);const i=e=>{if(!e||"string"!=typeof e)return!1;try{document.createDocumentFragment().querySelector(e)}catch{return!1}return!0};var o=r(2614),a=r(944),s=r(384),c=r(8122);const u="[data-nr-mask]",d=()=>{const e={feature_flags:[],experimental:{marks:!1,measures:!1,resources:!1},mask_selector:"*",block_selector:"[data-nr-block]",mask_input_options:{color:!1,date:!1,"datetime-local":!1,email:!1,month:!1,number:!1,range:!1,search:!1,tel:!1,text:!1,time:!1,url:!1,week:!1,textarea:!1,select:!1,password:!0}};return{ajax:{deny_list:void 0,block_internal:!0,enabled:!0,autoStart:!0},api:{allow_registered_children:!0,duplicate_registered_data:!1},distributed_tracing:{enabled:void 0,exclude_newrelic_header:void 0,cors_use_newrelic_header:void 0,cors_use_tracecontext_headers:void 0,allowed_origins:void 0},get feature_flags(){return e.feature_flags},set feature_flags(t){e.feature_flags=t},generic_events:{enabled:!0,autoStart:!0},harvest:{interval:30},jserrors:{enabled:!0,autoStart:!0},logging:{enabled:!0,autoStart:!0},metrics:{enabled:!0,autoStart:!0},obfuscate:void 0,page_action:{enabled:!0},page_view_event:{enabled:!0,autoStart:!0},page_view_timing:{enabled:!0,autoStart:!0},performance:{get capture_marks(){return e.feature_flags.includes(n.$v.MARKS)||e.experimental.marks},set capture_marks(t){e.experimental.marks=t},get capture_measures(){return e.feature_flags.includes(n.$v.MEASURES)||e.experimental.measures},set capture_measures(t){e.experimental.measures=t},capture_detail:!0,resources:{get enabled(){return e.feature_flags.includes(n.$v.RESOURCES)||e.experimental.resources},set enabled(t){e.experimental.resources=t},asset_types:[],first_party_domains:[],ignore_newrelic:!0}},privacy:{cookies_enabled:!0},proxy:{assets:void 0,beacon:void 0},session:{expiresMs:o.wk,inactiveMs:o.BB},session_replay:{autoStart:!0,enabled:!1,preload:!1,sampling_rate:10,error_sampling_rate:100,collect_fonts:!1,inline_images:!1,fix_stylesheets:!0,mask_all_inputs:!0,get mask_text_selector(){return e.mask_selector},set mask_text_selector(t){i(t)?e.mask_selector="".concat(t,",").concat(u):""===t||null===t?e.mask_selector=u:(0,a.R)(5,t)},get block_class(){return"nr-block"},get ignore_class(){return"nr-ignore"},get mask_text_class(){return"nr-mask"},get block_selector(){return e.block_selector},set block_selector(t){i(t)?e.block_selector+=",".concat(t):""!==t&&(0,a.R)(6,t)},get mask_input_options(){return e.mask_input_options},set mask_input_options(t){t&&"object"==typeof t?e.mask_input_options={...t,password:!0}:(0,a.R)(7,t)}},session_trace:{enabled:!0,autoStart:!0},soft_navigations:{enabled:!0,autoStart:!0},spa:{enabled:!0,autoStart:!0},ssl:void 0,user_actions:{enabled:!0,elementAttributes:["id","className","tagName","type"]}}},l={},f="All configuration objects require an agent identifier!";function h(e){if(!e)throw new Error(f);if(!l[e])throw new Error("Configuration for ".concat(e," was never set"));return l[e]}function p(e,t){if(!e)throw new Error(f);l[e]=(0,c.a)(t,d());const r=(0,s.nY)(e);r&&(r.init=l[e])}function g(e,t){if(!e)throw new Error(f);var r=h(e);if(r){for(var n=t.split("."),i=0;i<n.length-1;i++)if("object"!=typeof(r=r[n[i]]))return;r=r[n[n.length-1]]}return r}},5603:(e,t,r)=>{"use strict";r.d(t,{a:()=>c,o:()=>s});var n=r(384),i=r(8122);const o={accountID:void 0,trustKey:void 0,agentID:void 0,licenseKey:void 0,applicationID:void 0,xpid:void 0},a={};function s(e){if(!e)throw new Error("All loader-config objects require an agent identifier!");if(!a[e])throw new Error("LoaderConfig for ".concat(e," was never set"));return a[e]}function c(e,t){if(!e)throw new Error("All loader-config objects require an agent identifier!");a[e]=(0,i.a)(t,o);const r=(0,n.nY)(e);r&&(r.loader_config=a[e])}},3371:(e,t,r)=>{"use strict";r.d(t,{V:()=>f,f:()=>l});var n=r(8122),i=r(384),o=r(6154),a=r(9324);let s=0;const c={buildEnv:a.F3,distMethod:a.Xs,version:a.xv,originTime:o.WN},u={appMetadata:{},customTransaction:void 0,denyList:void 0,disabled:!1,entityManager:void 0,harvester:void 0,isolatedBacklog:!1,loaderType:void 0,maxBytes:3e4,obfuscator:void 0,onerror:void 0,ptid:void 0,releaseIds:{},session:void 0,timeKeeper:void 0},d={};function l(e){if(!e)throw new Error("All runtime objects require an agent identifier!");if(!d[e])throw new Error("Runtime for ".concat(e," was never set"));return d[e]}function f(e,t){if(!e)throw new Error("All runtime objects require an agent identifier!");d[e]={...(0,n.a)(t,u),...c},Object.hasOwnProperty.call(d[e],"harvestCount")||Object.defineProperty(d[e],"harvestCount",{get:()=>++s});const r=(0,i.nY)(e);r&&(r.runtime=d[e])}},9324:(e,t,r)=>{"use strict";r.d(t,{F3:()=>i,Xs:()=>o,Yq:()=>a,xv:()=>n});const n="1.288.1",i="PROD",o="CDN",a="^2.0.0-alpha.18"},6154:(e,t,r)=>{"use strict";r.d(t,{A4:()=>s,OF:()=>d,RI:()=>i,WN:()=>h,bv:()=>o,gm:()=>a,lR:()=>f,m:()=>u,mw:()=>c,sb:()=>l});var n=r(1863);const i="undefined"!=typeof window&&!!window.document,o="undefined"!=typeof WorkerGlobalScope&&("undefined"!=typeof self&&self instanceof WorkerGlobalScope&&self.navigator instanceof WorkerNavigator||"undefined"!=typeof globalThis&&globalThis instanceof WorkerGlobalScope&&globalThis.navigator instanceof WorkerNavigator),a=i?window:"undefined"!=typeof WorkerGlobalScope&&("undefined"!=typeof self&&self instanceof WorkerGlobalScope&&self||"undefined"!=typeof globalThis&&globalThis instanceof WorkerGlobalScope&&globalThis),s="complete"===a?.document?.readyState,c=Boolean("hidden"===a?.document?.visibilityState),u=""+a?.location,d=/iPad|iPhone|iPod/.test(a.navigator?.userAgent),l=d&&"undefined"==typeof SharedWorker,f=(()=>{const e=a.navigator?.userAgent?.match(/Firefox[/\s](\d+\.\d+)/);return Array.isArray(e)&&e.length>=2?+e[1]:0})(),h=Date.now()-(0,n.t)()},7295:(e,t,r)=>{"use strict";r.d(t,{Xv:()=>a,gX:()=>i,iW:()=>o});var n=[];function i(e){if(!e||o(e))return!1;if(0===n.length)return!0;for(var t=0;t<n.length;t++){var r=n[t];if("*"===r.hostname)return!1;if(s(r.hostname,e.hostname)&&c(r.pathname,e.pathname))return!1}return!0}function o(e){return void 0===e.hostname}function a(e){if(n=[],e&&e.length)for(var t=0;t<e.length;t++){let r=e[t];if(!r)continue;0===r.indexOf("http://")?r=r.substring(7):0===r.indexOf("https://")&&(r=r.substring(8));const i=r.indexOf("/");let o,a;i>0?(o=r.substring(0,i),a=r.substring(i)):(o=r,a="");let[s]=o.split(":");n.push({hostname:s,pathname:a})}}function s(e,t){return!(e.length>t.length)&&t.indexOf(e)===t.length-e.length}function c(e,t){return 0===e.indexOf("/")&&(e=e.substring(1)),0===t.indexOf("/")&&(t=t.substring(1)),""===e||e===t}},3241:(e,t,r)=>{"use strict";r.d(t,{W:()=>o});var n=r(6154);const i="newrelic";function o(e={}){try{n.gm.dispatchEvent(new CustomEvent(i,{detail:e}))}catch(e){}}},1687:(e,t,r)=>{"use strict";r.d(t,{Ak:()=>c,Ze:()=>l,x3:()=>u});var n=r(7836),i=r(3606),o=r(860),a=r(2646);const s={};function c(e,t){const r={staged:!1,priority:o.P3[t]||0};d(e),s[e].get(t)||s[e].set(t,r)}function u(e,t){e&&s[e]&&(s[e].get(t)&&s[e].delete(t),h(e,t,!1),s[e].size&&f(e))}function d(e){if(!e)throw new Error("agentIdentifier required");s[e]||(s[e]=new Map)}function l(e="",t="feature",r=!1){if(d(e),!e||!s[e].get(t)||r)return h(e,t);s[e].get(t).staged=!0,f(e)}function f(e){const t=Array.from(s[e]);t.every((([e,t])=>t.staged))&&(t.sort(((e,t)=>e[1].priority-t[1].priority)),t.forEach((([t])=>{s[e].delete(t),h(e,t)})))}function h(e,t,r=!0){const o=e?n.ee.get(e):n.ee,s=i.i.handlers;if(!o.aborted&&o.backlog&&s){if(r){const e=o.backlog[t],r=s[t];if(r){for(let t=0;e&&t<e.length;++t)p(e[t],r);Object.entries(r).forEach((([e,t])=>{Object.values(t||{}).forEach((t=>{t[0]?.on&&t[0]?.context()instanceof a.y&&t[0].on(e,t[1])}))}))}}o.isolatedBacklog||delete s[t],o.backlog[t]=null,o.emit("drain-"+t,[])}}function p(e,t){var r=e[1];Object.values(t[r]||{}).forEach((t=>{var r=e[0];if(t[0]===r){var n=t[1],i=e[3],o=e[2];n.apply(i,o)}}))}},7836:(e,t,r)=>{"use strict";r.d(t,{P:()=>c,ee:()=>u});var n=r(384),i=r(8990),o=r(3371),a=r(2646),s=r(5607);const c="nr@context:".concat(s.W),u=function e(t,r){var n={},s={},d={},l=!1;try{l=16===r.length&&(0,o.f)(r).isolatedBacklog}catch(e){}var f={on:p,addEventListener:p,removeEventListener:function(e,t){var r=n[e];if(!r)return;for(var i=0;i<r.length;i++)r[i]===t&&r.splice(i,1)},emit:function(e,r,n,i,o){!1!==o&&(o=!0);if(u.aborted&&!i)return;t&&o&&t.emit(e,r,n);for(var a=h(n),c=g(e),d=c.length,l=0;l<d;l++)c[l].apply(a,r);var p=v()[s[e]];p&&p.push([f,e,r,a]);return a},get:m,listeners:g,context:h,buffer:function(e,t){const r=v();if(t=t||"feature",f.aborted)return;Object.entries(e||{}).forEach((([e,n])=>{s[n]=t,t in r||(r[t]=[])}))},abort:function(){f._aborted=!0,Object.keys(f.backlog).forEach((e=>{delete f.backlog[e]}))},isBuffering:function(e){return!!v()[s[e]]},debugId:r,backlog:l?{}:t&&"object"==typeof t.backlog?t.backlog:{},isolatedBacklog:l};return Object.defineProperty(f,"aborted",{get:()=>{let e=f._aborted||!1;return e||(t&&(e=t.aborted),e)}}),f;function h(e){return e&&e instanceof a.y?e:e?(0,i.I)(e,c,(()=>new a.y(c))):new a.y(c)}function p(e,t){n[e]=g(e).concat(t)}function g(e){return n[e]||[]}function m(t){return d[t]=d[t]||e(f,t)}function v(){return f.backlog}}(void 0,"globalEE"),d=(0,n.Zm)();d.ee||(d.ee=u)},2646:(e,t,r)=>{"use strict";r.d(t,{y:()=>n});class n{constructor(e){this.contextId=e}}},9908:(e,t,r)=>{"use strict";r.d(t,{d:()=>n,p:()=>i});var n=r(7836).ee.get("handle");function i(e,t,r,i,o){o?(o.buffer([e],i),o.emit(e,t,r)):(n.buffer([e],i),n.emit(e,t,r))}},3606:(e,t,r)=>{"use strict";r.d(t,{i:()=>o});var n=r(9908);o.on=a;var i=o.handlers={};function o(e,t,r,o){a(o||n.d,i,e,t,r)}function a(e,t,r,i,o){o||(o="feature"),e||(e=n.d);var a=t[o]=t[o]||{};(a[r]=a[r]||[]).push([e,i])}},3878:(e,t,r)=>{"use strict";function n(e,t){return{capture:e,passive:!1,signal:t}}function i(e,t,r=!1,i){window.addEventListener(e,t,n(r,i))}function o(e,t,r=!1,i){document.addEventListener(e,t,n(r,i))}r.d(t,{DD:()=>o,jT:()=>n,sp:()=>i})},5607:(e,t,r)=>{"use strict";r.d(t,{W:()=>n});const n=(0,r(9566).bz)()},9566:(e,t,r)=>{"use strict";r.d(t,{LA:()=>s,ZF:()=>c,bz:()=>a,el:()=>u});var n=r(6154);const i="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx";function o(e,t){return e?15&e[t]:16*Math.random()|0}function a(){const e=n.gm?.crypto||n.gm?.msCrypto;let t,r=0;return e&&e.getRandomValues&&(t=e.getRandomValues(new Uint8Array(30))),i.split("").map((e=>"x"===e?o(t,r++).toString(16):"y"===e?(3&o()|8).toString(16):e)).join("")}function s(e){const t=n.gm?.crypto||n.gm?.msCrypto;let r,i=0;t&&t.getRandomValues&&(r=t.getRandomValues(new Uint8Array(e)));const a=[];for(var s=0;s<e;s++)a.push(o(r,i++).toString(16));return a.join("")}function c(){return s(16)}function u(){return s(32)}},2614:(e,t,r)=>{"use strict";r.d(t,{BB:()=>a,H3:()=>n,g:()=>u,iL:()=>c,tS:()=>s,uh:()=>i,wk:()=>o});const n="NRBA",i="SESSION",o=144e5,a=18e5,s={STARTED:"session-started",PAUSE:"session-pause",RESET:"session-reset",RESUME:"session-resume",UPDATE:"session-update"},c={SAME_TAB:"same-tab",CROSS_TAB:"cross-tab"},u={OFF:0,FULL:1,ERROR:2}},1863:(e,t,r)=>{"use strict";function n(){return Math.floor(performance.now())}r.d(t,{t:()=>n})},7485:(e,t,r)=>{"use strict";r.d(t,{D:()=>i});var n=r(6154);function i(e){if(0===(e||"").indexOf("data:"))return{protocol:"data"};try{const t=new URL(e,location.href),r={port:t.port,hostname:t.hostname,pathname:t.pathname,search:t.search,protocol:t.protocol.slice(0,t.protocol.indexOf(":")),sameOrigin:t.protocol===n.gm?.location?.protocol&&t.host===n.gm?.location?.host};return r.port&&""!==r.port||("http:"===t.protocol&&(r.port="80"),"https:"===t.protocol&&(r.port="443")),r.pathname&&""!==r.pathname?r.pathname.startsWith("/")||(r.pathname="/".concat(r.pathname)):r.pathname="/",r}catch(e){return{}}}},944:(e,t,r)=>{"use strict";function n(e,t){"function"==typeof console.debug&&console.debug("New Relic Warning: https://github.com/newrelic/newrelic-browser-agent/blob/main/docs/warning-codes.md#".concat(e),t)}r.d(t,{R:()=>n})},5701:(e,t,r)=>{"use strict";r.d(t,{B:()=>o,t:()=>a});var n=r(3241);const i=new Set,o={};function a(e,t){const r=t.agentIdentifier;o[r]??={},e&&"object"==typeof e&&(i.has(r)||(t.ee.emit("rumresp",[e]),o[r]=e,i.add(r),(0,n.W)({agentIdentifier:r,loaded:!0,drained:!0,type:"lifecycle",name:"load",feature:void 0,data:e})))}},8990:(e,t,r)=>{"use strict";r.d(t,{I:()=>i});var n=Object.prototype.hasOwnProperty;function i(e,t,r){if(n.call(e,t))return e[t];var i=r();if(Object.defineProperty&&Object.keys)try{return Object.defineProperty(e,t,{value:i,writable:!0,enumerable:!1}),i}catch(e){}return e[t]=i,i}},6389:(e,t,r)=>{"use strict";function n(e,t=500,r={}){const n=r?.leading||!1;let i;return(...r)=>{n&&void 0===i&&(e.apply(this,r),i=setTimeout((()=>{i=clearTimeout(i)}),t)),n||(clearTimeout(i),i=setTimeout((()=>{e.apply(this,r)}),t))}}function i(e){let t=!1;return(...r)=>{t||(t=!0,e.apply(this,r))}}r.d(t,{J:()=>i,s:()=>n})},3304:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(7836);const i=()=>{const e=new WeakSet;return(t,r)=>{if("object"==typeof r&&null!==r){if(e.has(r))return;e.add(r)}return r}};function o(e){try{return JSON.stringify(e,i())??""}catch(e){try{n.ee.emit("internal-error",[e])}catch(e){}return""}}},3496:(e,t,r)=>{"use strict";function n(e){return!e||!(!e.licenseKey||!e.applicationID)}function i(e,t){return!e||e.licenseKey===t.info.licenseKey&&e.applicationID===t.info.applicationID}r.d(t,{A:()=>i,I:()=>n})},5289:(e,t,r)=>{"use strict";r.d(t,{GG:()=>o,sB:()=>a});var n=r(3878);function i(){return"undefined"==typeof document||"complete"===document.readyState}function o(e,t){if(i())return e();(0,n.sp)("load",e,t)}function a(e){if(i())return e();(0,n.DD)("DOMContentLoaded",e)}},384:(e,t,r)=>{"use strict";r.d(t,{NT:()=>o,US:()=>d,Zm:()=>a,bQ:()=>c,dV:()=>s,nY:()=>u,pV:()=>l});var n=r(6154),i=r(1863);const o={beacon:"bam.nr-data.net",errorBeacon:"bam.nr-data.net"};function a(){return n.gm.NREUM||(n.gm.NREUM={}),void 0===n.gm.newrelic&&(n.gm.newrelic=n.gm.NREUM),n.gm.NREUM}function s(){let e=a();return e.o||(e.o={ST:n.gm.setTimeout,SI:n.gm.setImmediate,CT:n.gm.clearTimeout,XHR:n.gm.XMLHttpRequest,REQ:n.gm.Request,EV:n.gm.Event,PR:n.gm.Promise,MO:n.gm.MutationObserver,FETCH:n.gm.fetch,WS:n.gm.WebSocket}),e}function c(e,t){let r=a();r.initializedAgents??={},t.initializedAt={ms:(0,i.t)(),date:new Date},r.initializedAgents[e]=t}function u(e){let t=a();return t.initializedAgents?.[e]}function d(e,t){a()[e]=t}function l(){return function(){let e=a();const t=e.info||{};e.info={beacon:o.beacon,errorBeacon:o.errorBeacon,...t}}(),function(){let e=a();const t=e.init||{};e.init={...t}}(),s(),function(){let e=a();const t=e.loader_config||{};e.loader_config={...t}}(),a()}},2843:(e,t,r)=>{"use strict";r.d(t,{u:()=>i});var n=r(3878);function i(e,t=!1,r,i){(0,n.DD)("visibilitychange",(function(){if(t)return void("hidden"===document.visibilityState&&e());e(document.visibilityState)}),r,i)}},8139:(e,t,r)=>{"use strict";r.d(t,{u:()=>f});var n=r(7836),i=r(3434),o=r(8990),a=r(6154);const s={},c=a.gm.XMLHttpRequest,u="addEventListener",d="removeEventListener",l="nr@wrapped:".concat(n.P);function f(e){var t=function(e){return(e||n.ee).get("events")}(e);if(s[t.debugId]++)return t;s[t.debugId]=1;var r=(0,i.YM)(t,!0);function f(e){r.inPlace(e,[u,d],"-",p)}function p(e,t){return e[1]}return"getPrototypeOf"in Object&&(a.RI&&h(document,f),c&&h(c.prototype,f),h(a.gm,f)),t.on(u+"-start",(function(e,t){var n=e[1];if(null!==n&&("function"==typeof n||"object"==typeof n)){var i=(0,o.I)(n,l,(function(){var e={object:function(){if("function"!=typeof n.handleEvent)return;return n.handleEvent.apply(n,arguments)},function:n}[typeof n];return e?r(e,"fn-",null,e.name||"anonymous"):n}));this.wrapped=e[1]=i}})),t.on(d+"-start",(function(e){e[1]=this.wrapped||e[1]})),t}function h(e,t,...r){let n=e;for(;"object"==typeof n&&!Object.prototype.hasOwnProperty.call(n,u);)n=Object.getPrototypeOf(n);n&&t(n,...r)}},3434:(e,t,r)=>{"use strict";r.d(t,{Jt:()=>o,YM:()=>c});var n=r(7836),i=r(5607);const o="nr@original:".concat(i.W);var a=Object.prototype.hasOwnProperty,s=!1;function c(e,t){return e||(e=n.ee),r.inPlace=function(e,t,n,i,o){n||(n="");const a="-"===n.charAt(0);for(let s=0;s<t.length;s++){const c=t[s],u=e[c];d(u)||(e[c]=r(u,a?c+n:n,i,c,o))}},r.flag=o,r;function r(t,r,n,s,c){return d(t)?t:(r||(r=""),nrWrapper[o]=t,function(e,t,r){if(Object.defineProperty&&Object.keys)try{return Object.keys(e).forEach((function(r){Object.defineProperty(t,r,{get:function(){return e[r]},set:function(t){return e[r]=t,t}})})),t}catch(e){u([e],r)}for(var n in e)a.call(e,n)&&(t[n]=e[n])}(t,nrWrapper,e),nrWrapper);function nrWrapper(){var o,a,d,l;try{a=this,o=[...arguments],d="function"==typeof n?n(o,a):n||{}}catch(t){u([t,"",[o,a,s],d],e)}i(r+"start",[o,a,s],d,c);try{return l=t.apply(a,o)}catch(e){throw i(r+"err",[o,a,e],d,c),e}finally{i(r+"end",[o,a,l],d,c)}}}function i(r,n,i,o){if(!s||t){var a=s;s=!0;try{e.emit(r,n,i,t,o)}catch(t){u([t,r,n,i],e)}s=a}}}function u(e,t){t||(t=n.ee);try{t.emit("internal-error",e)}catch(e){}}function d(e){return!(e&&"function"==typeof e&&e.apply&&!e[o])}},9414:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var n=r(7836),i=r(2646),o=r(944),a=r(3434);const s=new Map;function c(e,t,r,c){if("object"!=typeof t||!t||"string"!=typeof r||!r||"function"!=typeof t[r])return(0,o.R)(29);const u=function(e){return(e||n.ee).get("logger")}(e),d=(0,a.YM)(u),l=new i.y(n.P);l.level=c.level,l.customAttributes=c.customAttributes;const f=t[r]?.[a.Jt]||t[r];return s.set(f,l),d.inPlace(t,[r],"wrap-logger-",(()=>s.get(f))),u}},9300:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});const n=r(860).K7.ajax},3333:(e,t,r)=>{"use strict";r.d(t,{$v:()=>u,TZ:()=>n,Zp:()=>i,kd:()=>c,mq:()=>s,nf:()=>a,qN:()=>o});const n=r(860).K7.genericEvents,i=["auxclick","click","copy","keydown","paste","scrollend"],o=["focus","blur"],a=4,s=1e3,c=["PageAction","UserAction","BrowserPerformance"],u={MARKS:"experimental.marks",MEASURES:"experimental.measures",RESOURCES:"experimental.resources"}},6774:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});const n=r(860).K7.jserrors},993:(e,t,r)=>{"use strict";r.d(t,{A$:()=>o,ET:()=>a,TZ:()=>s,p_:()=>i});var n=r(860);const i={ERROR:"ERROR",WARN:"WARN",INFO:"INFO",DEBUG:"DEBUG",TRACE:"TRACE"},o={OFF:0,ERROR:1,WARN:2,INFO:3,DEBUG:4,TRACE:5},a="log",s=n.K7.logging},3785:(e,t,r)=>{"use strict";r.d(t,{R:()=>c,b:()=>u});var n=r(9908),i=r(1863),o=r(860),a=r(8154),s=r(993);function c(e,t,r={},c=s.p_.INFO,u,d=(0,i.t)()){(0,n.p)(a.xV,["API/logging/".concat(c.toLowerCase(),"/called")],void 0,o.K7.metrics,e),(0,n.p)(s.ET,[d,t,r,c,u],void 0,o.K7.logging,e)}function u(e){return"string"==typeof e&&Object.values(s.p_).some((t=>t===e.toUpperCase().trim()))}},8154:(e,t,r)=>{"use strict";r.d(t,{z_:()=>o,XG:()=>s,TZ:()=>n,rs:()=>i,xV:()=>a});r(6154),r(9566),r(384);const n=r(860).K7.metrics,i="sm",o="cm",a="storeSupportabilityMetrics",s="storeEventMetrics"},6630:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});const n=r(860).K7.pageViewEvent},782:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});const n=r(860).K7.pageViewTiming},6344:(e,t,r)=>{"use strict";r.d(t,{BB:()=>d,G4:()=>o,Qb:()=>l,TZ:()=>i,Ug:()=>a,_s:()=>s,bc:()=>u,yP:()=>c});var n=r(2614);const i=r(860).K7.sessionReplay,o={RECORD:"recordReplay",PAUSE:"pauseReplay",REPLAY_RUNNING:"replayRunning",ERROR_DURING_REPLAY:"errorDuringReplay"},a=.12,s={DomContentLoaded:0,Load:1,FullSnapshot:2,IncrementalSnapshot:3,Meta:4,Custom:5},c={[n.g.ERROR]:15e3,[n.g.FULL]:3e5,[n.g.OFF]:0},u={RESET:{message:"Session was reset",sm:"Reset"},IMPORT:{message:"Recorder failed to import",sm:"Import"},TOO_MANY:{message:"429: Too Many Requests",sm:"Too-Many"},TOO_BIG:{message:"Payload was too large",sm:"Too-Big"},CROSS_TAB:{message:"Session Entity was set to OFF on another tab",sm:"Cross-Tab"},ENTITLEMENTS:{message:"Session Replay is not allowed and will not be started",sm:"Entitlement"}},d=5e3,l={API:"api"}},5270:(e,t,r)=>{"use strict";r.d(t,{Aw:()=>c,CT:()=>u,SR:()=>s,rF:()=>d});var n=r(384),i=r(9417),o=r(7767),a=r(6154);function s(e){return!!(0,n.dV)().o.MO&&(0,o.V)(e)&&!0===(0,i.gD)(e,"session_trace.enabled")}function c(e){return!0===(0,i.gD)(e,"session_replay.preload")&&s(e)}function u(e,t){const r=t.correctAbsoluteTimestamp(e);return{originalTimestamp:e,correctedTimestamp:r,timestampDiff:e-r,originTime:a.WN,correctedOriginTime:t.correctedOriginTime,originTimeDiff:Math.floor(a.WN-t.correctedOriginTime)}}function d(e,t){try{if("string"==typeof t?.type){if("password"===t.type.toLowerCase())return"*".repeat(e?.length||0);if(void 0!==t?.dataset?.nrUnmask||t?.classList?.contains("nr-unmask"))return e}}catch(e){}return"string"==typeof e?e.replace(/[\S]/g,"*"):"*".repeat(e?.length||0)}},3738:(e,t,r)=>{"use strict";r.d(t,{He:()=>i,Kp:()=>s,Lc:()=>u,Rz:()=>d,TZ:()=>n,bD:()=>o,d3:()=>a,jx:()=>l,uP:()=>c});const n=r(860).K7.sessionTrace,i="bstResource",o="resource",a="-start",s="-end",c="fn"+a,u="fn"+s,d="pushState",l=1e3},3962:(e,t,r)=>{"use strict";r.d(t,{AM:()=>o,O2:()=>c,Qu:()=>u,TZ:()=>s,ih:()=>d,pP:()=>a,tC:()=>i});var n=r(860);const i=["click","keydown","submit","popstate"],o="api",a="initialPageLoad",s=n.K7.softNav,c={INITIAL_PAGE_LOAD:"",ROUTE_CHANGE:1,UNSPECIFIED:2},u={INTERACTION:1,AJAX:2,CUSTOM_END:3,CUSTOM_TRACER:4},d={IP:"in progress",FIN:"finished",CAN:"cancelled"}},7378:(e,t,r)=>{"use strict";r.d(t,{$p:()=>x,BR:()=>b,Kp:()=>R,L3:()=>y,Lc:()=>c,NC:()=>o,SG:()=>d,TZ:()=>i,U6:()=>p,UT:()=>m,d3:()=>w,dT:()=>f,e5:()=>E,gx:()=>v,l9:()=>l,oW:()=>h,op:()=>g,rw:()=>u,tH:()=>T,uP:()=>s,wW:()=>A,xq:()=>a});var n=r(384);const i=r(860).K7.spa,o=["click","submit","keypress","keydown","keyup","change"],a=999,s="fn-start",c="fn-end",u="cb-start",d="api-ixn-",l="remaining",f="interaction",h="spaNode",p="jsonpNode",g="fetch-start",m="fetch-done",v="fetch-body-",b="jsonp-end",y=(0,n.dV)().o.ST,w="-start",R="-end",x="-body",A="cb"+R,E="jsTime",T="fetch"},4234:(e,t,r)=>{"use strict";r.d(t,{W:()=>o});var n=r(7836),i=r(1687);class o{constructor(e,t){this.agentIdentifier=e,this.ee=n.ee.get(e),this.featureName=t,this.blocked=!1}deregisterDrain(){(0,i.x3)(this.agentIdentifier,this.featureName)}}},7767:(e,t,r)=>{"use strict";r.d(t,{V:()=>o});var n=r(9417),i=r(6154);const o=e=>i.RI&&!0===(0,n.gD)(e,"privacy.cookies_enabled")},2110:(e,t,r)=>{"use strict";r.d(t,{j:()=>j});var n=r(860),i=r(9908),o=r(1687),a=r(5289),s=r(6154),c=r(944),u=r(8154),d=r(384),l=r(6344);const f=["setErrorHandler","finished","addToTrace","addRelease","recordCustomEvent","addPageAction","setCurrentRouteName","setPageViewName","setCustomAttribute","interaction","noticeError","setUserId","setApplicationVersion","start",l.G4.RECORD,l.G4.PAUSE,"log","wrapLogger","register"],h=["setErrorHandler","finished","addToTrace","addRelease"];var p=r(1863),g=r(2614),m=r(993),v=r(3785),b=r(9414),y=r(3496);var w=r(3241),R=r(5701);function x(){const e=(0,d.pV)();f.forEach((t=>{e[t]=(...r)=>function(t,...r){let n=[];return Object.values(e.initializedAgents).forEach((e=>{e&&e.runtime?e.exposed&&e[t]&&"micro-agent"!==e.runtime.loaderType&&n.push(e[t](...r)):(0,c.R)(38,t)})),n[0]}(t,...r)}))}const A={};function E(e,t){t||(0,o.Ak)(e.agentIdentifier,"api");const d=e.ee.get("tracer");A[e.agentIdentifier]=g.g.OFF,e.ee.on(l.G4.REPLAY_RUNNING,(t=>{A[e.agentIdentifier]=t}));const f="api-",x=f+"ixn-",E={addPageAction:function(e,t,r,i=(0,p.t)()){N(f,"addPageAction",!0,n.K7.genericEvents,i)(e,t,r)},log:function(t,{customAttributes:r={},level:o=m.p_.INFO}={},a,s=(0,p.t)()){(0,i.p)(u.xV,["API/log/called"],void 0,n.K7.metrics,e.ee),(0,v.R)(e.ee,t,r,o,a,s)},noticeError:function(t,r,o,a=(0,p.t)()){"string"==typeof t&&(t=new Error(t)),(0,i.p)(u.xV,["API/noticeError/called"],void 0,n.K7.metrics,e.ee),(0,i.p)("err",[t,a,!1,r,!!A[e.agentIdentifier],void 0,o],void 0,n.K7.jserrors,e.ee)}};function T(t,r,n,i){const o=e.info;return null===r?delete o.jsAttributes[t]:e.info={...e.info,jsAttributes:{...o.jsAttributes,[t]:r}},N(f,n,!0,i||null===r?"session":void 0)(t,r)}function S(){}e.register=function(t){return(0,i.p)(u.xV,["API/register/called"],void 0,n.K7.metrics,e.ee),function(e,t,r){const o={};let a,s;(0,c.R)(54,"newrelic.register"),e.init.api.allow_registered_children||(a=()=>(0,c.R)(55)),r&&(0,y.I)(r)||(a=()=>(0,c.R)(48,r));const d={addPageAction:(e,n={})=>{l(t.addPageAction,[e,{...o,...n}],r)},log:(e,n={})=>{l(t.log,[e,{...n,customAttributes:{...o,...n.customAttributes||{}}}],r)},noticeError:(e,n={})=>{l(t.noticeError,[e,{...o,...n}],r)},setApplicationVersion:e=>{o["application.version"]=e},setCustomAttribute:(e,t)=>{o[e]=t},setUserId:e=>{o["enduser.id"]=e},metadata:{customAttributes:o,target:r,get connected(){return s||Promise.reject(new Error("Failed to connect"))}}};a?a():s=new Promise(((t,n)=>{try{const i=e.runtime?.entityManager;let a=!!i?.get().entityGuid,s=i?.getEntityGuidFor(r.licenseKey,r.applicationID),c=!!s;if(a&&c)r.entityGuid=s,t(d);else{const u=setTimeout((()=>n(new Error("Failed to connect - Timeout"))),15e3);function l(n){(0,y.A)(n,e)?a||=!0:r.licenseKey===n.licenseKey&&r.applicationID===n.applicationID&&(c=!0,r.entityGuid=n.entityGuid),a&&c&&(clearTimeout(u),e.ee.removeEventListener("entity-added",l),t(d))}e.ee.emit("api-send-rum",[o,r]),e.ee.on("entity-added",l)}}catch(f){n(f)}}));const l=async(t,r,o)=>{if(a)return a();const d=(0,p.t)();(0,i.p)(u.xV,["API/register/".concat(t.name,"/called")],void 0,n.K7.metrics,e.ee);try{await s;const n=e.init.api.duplicate_registered_data;(!0===n||Array.isArray(n)&&n.includes(o.entityGuid))&&t(...r,void 0,d),t(...r,o.entityGuid,d)}catch(e){(0,c.R)(50,e)}};return d}(e,E,t)},e.log=function(e,t){E.log(e,t)},e.wrapLogger=(t,r,{customAttributes:o={},level:a=m.p_.INFO}={})=>{(0,i.p)(u.xV,["API/wrapLogger/called"],void 0,n.K7.metrics,e.ee),(0,b.J)(e.ee,t,r,{customAttributes:o,level:a})},h.forEach((t=>{e[t]=N(f,t,!0,"api")})),e.addPageAction=function(e,t){E.addPageAction(e,t)},e.recordCustomEvent=N(f,"recordCustomEvent",!0,n.K7.genericEvents),e.setPageViewName=function(t,r){if("string"==typeof t)return"/"!==t.charAt(0)&&(t="/"+t),e.runtime.customTransaction=(r||"http://custom.transaction")+t,N(f,"setPageViewName",!0)()},e.setCustomAttribute=function(e,t,r=!1){if("string"==typeof e){if(["string","number","boolean"].includes(typeof t)||null===t)return T(e,t,"setCustomAttribute",r);(0,c.R)(40,typeof t)}else(0,c.R)(39,typeof e)},e.setUserId=function(e){if("string"==typeof e||null===e)return T("enduser.id",e,"setUserId",!0);(0,c.R)(41,typeof e)},e.setApplicationVersion=function(e){if("string"==typeof e||null===e)return T("application.version",e,"setApplicationVersion",!1);(0,c.R)(42,typeof e)},e.start=()=>{try{(0,i.p)(u.xV,["API/start/called"],void 0,n.K7.metrics,e.ee),e.ee.emit("manual-start-all")}catch(e){(0,c.R)(23,e)}},e[l.G4.RECORD]=function(){(0,i.p)(u.xV,["API/recordReplay/called"],void 0,n.K7.metrics,e.ee),(0,i.p)(l.G4.RECORD,[],void 0,n.K7.sessionReplay,e.ee)},e[l.G4.PAUSE]=function(){(0,i.p)(u.xV,["API/pauseReplay/called"],void 0,n.K7.metrics,e.ee),(0,i.p)(l.G4.PAUSE,[],void 0,n.K7.sessionReplay,e.ee)},e.interaction=function(e){return(new S).get("object"==typeof e?e:{})};const I=S.prototype={createTracer:function(t,r){var o={},a=this,s="function"==typeof r;return(0,i.p)(u.xV,["API/createTracer/called"],void 0,n.K7.metrics,e.ee),e.runSoftNavOverSpa||(0,i.p)(x+"tracer",[(0,p.t)(),t,o],a,n.K7.spa,e.ee),function(){if(d.emit((s?"":"no-")+"fn-start",[(0,p.t)(),a,s],o),s)try{return r.apply(this,arguments)}catch(e){const t="string"==typeof e?new Error(e):e;throw d.emit("fn-err",[arguments,this,t],o),t}finally{d.emit("fn-end",[(0,p.t)()],o)}}}};function N(t,r,o,a,s=(0,p.t)()){return function(){return(0,i.p)(u.xV,["API/"+r+"/called"],void 0,n.K7.metrics,e.ee),(0,w.W)({agentIdentifier:e.agentIdentifier,drained:!!R.B?.[e.agentIdentifier],type:"data",name:"api",feature:t+r,data:{notSpa:o,bufferGroup:a}}),a&&(0,i.p)(t+r,[s,...arguments],o?null:this,a,e.ee),o?void 0:this}}function _(){r.e(478).then(r.bind(r,8778)).then((({setAsyncAPI:t})=>{t(e),(0,o.Ze)(e.agentIdentifier,"api")})).catch((t=>{(0,c.R)(27,t),e.ee.abort()}))}return["actionText","setName","setAttribute","save","ignore","onEnd","getContext","end","get"].forEach((t=>{I[t]=function(){return N.apply(this,[x,t,void 0,e.runSoftNavOverSpa?n.K7.softNav:n.K7.spa]).apply(this,arguments)}})),e.setCurrentRouteName=function(){return e.runSoftNavOverSpa?N(x,"routeName",void 0,n.K7.softNav)(...arguments):N(f,"routeName",!0,n.K7.spa)(...arguments)},e.noticeError=function(e,t){E.noticeError(e,t)},s.RI?(0,a.GG)((()=>_()),!0):_(),!0}var T=r(2555),S=r(9417),I=r(5603),N=r(3371);const _=e=>{const t=e.startsWith("http");e+="/",r.p=t?e:"https://"+e};var O=r(7836);const P=new Set;function j(e,t={},r,n){let{init:i,info:o,loader_config:a,runtime:c={},exposed:u=!0}=t;c.loaderType=r;const l=(0,d.pV)();o||(i=l.init,o=l.info,a=l.loader_config),(0,S.xN)(e.agentIdentifier,i||{}),(0,I.a)(e.agentIdentifier,a||{}),o.jsAttributes??={},s.bv&&(o.jsAttributes.isWorker=!0),(0,T.x1)(e.agentIdentifier,o);const f=e.init,h=[o.beacon,o.errorBeacon];P.has(e.agentIdentifier)||(f.proxy.assets&&(_(f.proxy.assets),h.push(f.proxy.assets)),f.proxy.beacon&&h.push(f.proxy.beacon),x(),(0,d.US)("activatedFeatures",R.B),e.runSoftNavOverSpa&&=!0===f.soft_navigations.enabled&&f.feature_flags.includes("soft_nav")),c.denyList=[...f.ajax.deny_list||[],...f.ajax.block_internal?h:[]],c.ptid=e.agentIdentifier,(0,N.V)(e.agentIdentifier,c),P.has(e.agentIdentifier)||(e.ee=O.ee.get(e.agentIdentifier),e.exposed=u,E(e,n),(0,w.W)({agentIdentifier:e.agentIdentifier,drained:!!R.B?.[e.agentIdentifier],type:"lifecycle",name:"initialize",feature:void 0,data:e.config})),P.add(e.agentIdentifier)}},8374:(e,t,r)=>{r.nc=(()=>{try{return document?.currentScript?.nonce}catch(e){}return""})()},860:(e,t,r)=>{"use strict";r.d(t,{$J:()=>d,K7:()=>c,P3:()=>u,XX:()=>i,Yy:()=>s,df:()=>o,qY:()=>n,v4:()=>a});const n="events",i="jserrors",o="browser/blobs",a="rum",s="browser/logs",c={ajax:"ajax",genericEvents:"generic_events",jserrors:i,logging:"logging",metrics:"metrics",pageAction:"page_action",pageViewEvent:"page_view_event",pageViewTiming:"page_view_timing",sessionReplay:"session_replay",sessionTrace:"session_trace",softNav:"soft_navigations",spa:"spa"},u={[c.pageViewEvent]:1,[c.pageViewTiming]:2,[c.metrics]:3,[c.jserrors]:4,[c.spa]:5,[c.ajax]:6,[c.sessionTrace]:7,[c.softNav]:8,[c.sessionReplay]:9,[c.logging]:10,[c.genericEvents]:11},d={[c.pageViewEvent]:a,[c.pageViewTiming]:n,[c.ajax]:n,[c.spa]:n,[c.softNav]:n,[c.metrics]:i,[c.jserrors]:i,[c.sessionTrace]:o,[c.sessionReplay]:o,[c.logging]:s,[c.genericEvents]:"ins"}}},n={};function i(e){var t=n[e];if(void 0!==t)return t.exports;var o=n[e]={exports:{}};return r[e](o,o.exports,i),o.exports}i.m=r,i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.f={},i.e=e=>Promise.all(Object.keys(i.f).reduce(((t,r)=>(i.f[r](e,t),t)),[])),i.u=e=>({212:"nr-spa-compressor",249:"nr-spa-recorder",478:"nr-spa"}[e]+"-1.288.1.min.js"),i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),e={},t="NRBA-1.288.1.PROD:",i.l=(r,n,o,a)=>{if(e[r])e[r].push(n);else{var s,c;if(void 0!==o)for(var u=document.getElementsByTagName("script"),d=0;d<u.length;d++){var l=u[d];if(l.getAttribute("src")==r||l.getAttribute("data-webpack")==t+o){s=l;break}}if(!s){c=!0;var f={478:"sha512-A197RDwDZ/NC6lb/PMIOFBrBanO57cULL4UpUepScgzjZPIPBBvfu4q0KzMhC7C3O5hKrc4kmK4aLpo+cShQzw==",249:"sha512-vLsg6+YBYhjurdM2WzfhBAhBVePWtoDu80qykIlpvNN2ZI/vQW6RtaLjxO4qURR+uoqAzj3rmr/WXrut/dMk6w==",212:"sha512-zp1Z+7gPpQlCybgSJryGY0yfN1jgDOdumlzqhYMlkNM6WECh2j0PyauwuXIRk8U4vpuwRq7tFWKekZv0Lednvw=="};(s=document.createElement("script")).charset="utf-8",s.timeout=120,i.nc&&s.setAttribute("nonce",i.nc),s.setAttribute("data-webpack",t+o),s.src=r,0!==s.src.indexOf(window.location.origin+"/")&&(s.crossOrigin="anonymous"),f[a]&&(s.integrity=f[a])}e[r]=[n];var h=(t,n)=>{s.onerror=s.onload=null,clearTimeout(p);var i=e[r];if(delete e[r],s.parentNode&&s.parentNode.removeChild(s),i&&i.forEach((e=>e(n))),t)return t(n)},p=setTimeout(h.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=h.bind(null,s.onerror),s.onload=h.bind(null,s.onload),c&&document.head.appendChild(s)}},i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.p="https://js-agent.newrelic.com/",(()=>{var e={38:0,788:0};i.f.j=(t,r)=>{var n=i.o(e,t)?e[t]:void 0;if(0!==n)if(n)r.push(n[2]);else{var o=new Promise(((r,i)=>n=e[t]=[r,i]));r.push(n[2]=o);var a=i.p+i.u(t),s=new Error;i.l(a,(r=>{if(i.o(e,t)&&(0!==(n=e[t])&&(e[t]=void 0),n)){var o=r&&("load"===r.type?"missing":r.type),a=r&&r.target&&r.target.src;s.message="Loading chunk "+t+" failed.\n("+o+": "+a+")",s.name="ChunkLoadError",s.type=o,s.request=a,n[1](s)}}),"chunk-"+t,t)}};var t=(t,r)=>{var n,o,[a,s,c]=r,u=0;if(a.some((t=>0!==e[t]))){for(n in s)i.o(s,n)&&(i.m[n]=s[n]);if(c)c(i)}for(t&&t(r);u<a.length;u++)o=a[u],i.o(e,o)&&e[o]&&e[o][0](),e[o]=0},r=self["webpackChunk:NRBA-1.288.1.PROD"]=self["webpackChunk:NRBA-1.288.1.PROD"]||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),(()=>{"use strict";i(8374);var e=i(944),t=i(6344),r=i(9566);class n{agentIdentifier;constructor(){this.agentIdentifier=(0,r.LA)(16)}#e(t,...r){if(this[t]!==n.prototype[t])return this[t](...r);(0,e.R)(35,t)}addPageAction(e,t){return this.#e("addPageAction",e,t)}register(e){return this.#e("register",e)}recordCustomEvent(e,t){return this.#e("recordCustomEvent",e,t)}setPageViewName(e,t){return this.#e("setPageViewName",e,t)}setCustomAttribute(e,t,r){return this.#e("setCustomAttribute",e,t,r)}noticeError(e,t){return this.#e("noticeError",e,t)}setUserId(e){return this.#e("setUserId",e)}setApplicationVersion(e){return this.#e("setApplicationVersion",e)}setErrorHandler(e){return this.#e("setErrorHandler",e)}addRelease(e,t){return this.#e("addRelease",e,t)}log(e,t){return this.#e("log",e,t)}}class o extends n{#e(t,...r){if(this[t]!==o.prototype[t]&&this[t]!==n.prototype[t])return this[t](...r);(0,e.R)(35,t)}start(){return this.#e("start")}finished(e){return this.#e("finished",e)}recordReplay(){return this.#e(t.G4.RECORD)}pauseReplay(){return this.#e(t.G4.PAUSE)}addToTrace(e){return this.#e("addToTrace",e)}setCurrentRouteName(e){return this.#e("setCurrentRouteName",e)}interaction(){return this.#e("interaction")}wrapLogger(e,t,r){return this.#e("wrapLogger",e,t,r)}}var a=i(860),s=i(9417);const c=Object.values(a.K7);function u(e){const t={};return c.forEach((r=>{t[r]=function(e,t){return!0===(0,s.gD)(t,"".concat(e,".enabled"))}(r,e)})),t}var d=i(2110);var l=i(9908),f=i(1687),h=i(4234),p=i(5289),g=i(6154),m=i(5270),v=i(7767),b=i(6389);class y extends h.W{constructor(e,t,r=!0){super(e.agentIdentifier,t),this.auto=r,this.abortHandler=void 0,this.featAggregate=void 0,this.onAggregateImported=void 0,!1===e.init[this.featureName].autoStart&&(this.auto=!1),this.auto?(0,f.Ak)(e.agentIdentifier,t):this.ee.on("manual-start-all",(0,b.J)((()=>{(0,f.Ak)(e.agentIdentifier,this.featureName),this.auto=!0,this.importAggregator(e)})))}importAggregator(t,r={}){if(this.featAggregate||!this.auto)return;let n;this.onAggregateImported=new Promise((e=>{n=e}));const o=async()=>{let o;try{if((0,v.V)(this.agentIdentifier)){const{setupAgentSession:e}=await i.e(478).then(i.bind(i,6526));o=e(t)}}catch(t){(0,e.R)(20,t),this.ee.emit("internal-error",[t]),this.featureName===a.K7.sessionReplay&&this.abortHandler?.()}try{if(!this.#t(this.featureName,o))return(0,f.Ze)(this.agentIdentifier,this.featureName),void n(!1);const{lazyFeatureLoader:e}=await i.e(478).then(i.bind(i,6103)),{Aggregate:a}=await e(this.featureName,"aggregate");this.featAggregate=new a(t,r),t.runtime.harvester.initializedAggregates.push(this.featAggregate),n(!0)}catch(t){(0,e.R)(34,t),this.abortHandler?.(),(0,f.Ze)(this.agentIdentifier,this.featureName,!0),n(!1),this.ee&&this.ee.abort()}};g.RI?(0,p.GG)((()=>o()),!0):o()}#t(e,t){switch(e){case a.K7.sessionReplay:return(0,m.SR)(this.agentIdentifier)&&!!t;case a.K7.sessionTrace:return!!t;default:return!0}}}var w=i(6630);class R extends y{static featureName=w.T;constructor(e,t=!0){super(e,w.T,t),this.ee.on("api-send-rum",((e,t)=>(0,l.p)("send-rum",[e,t],void 0,this.featureName,this.ee))),this.importAggregator(e)}}var x=i(384);var A=i(2843),E=i(3878),T=i(782),S=i(1863);class I extends y{static featureName=T.T;constructor(e,t=!0){super(e,T.T,t),g.RI&&((0,A.u)((()=>(0,l.p)("docHidden",[(0,S.t)()],void 0,T.T,this.ee)),!0),(0,E.sp)("pagehide",(()=>(0,l.p)("winPagehide",[(0,S.t)()],void 0,T.T,this.ee))),this.importAggregator(e))}}var N=i(8154);class _ extends y{static featureName=N.TZ;constructor(e,t=!0){super(e,N.TZ,t),g.RI&&document.addEventListener("securitypolicyviolation",(e=>{(0,l.p)(N.xV,["Generic/CSPViolation/Detected"],void 0,this.featureName,this.ee)})),this.importAggregator(e)}}var O=i(6774),P=i(3304);class j{constructor(e,t,r,n,i){this.name="UncaughtError",this.message="string"==typeof e?e:(0,P.A)(e),this.sourceURL=t,this.line=r,this.column=n,this.__newrelic=i}}function C(e){return D(e)?e:new j(void 0!==e?.message?e.message:e,e?.filename||e?.sourceURL,e?.lineno||e?.line,e?.colno||e?.col,e?.__newrelic)}function k(e){const t="Unhandled Promise Rejection: ";if(!e?.reason)return;if(D(e.reason)){try{e.reason.message.startsWith(t)||(e.reason.message=t+e.reason.message)}catch(e){}return C(e.reason)}const r=C(e.reason);return(r.message||"").startsWith(t)||(r.message=t+r.message),r}function L(e){if(e.error instanceof SyntaxError&&!/:\d+$/.test(e.error.stack?.trim())){const t=new j(e.message,e.filename,e.lineno,e.colno,e.error.__newrelic);return t.name=SyntaxError.name,t}return D(e.error)?e.error:C(e)}function D(e){return e instanceof Error&&!!e.stack}class M extends y{static featureName=O.T;#r=!1;constructor(e,r=!0){super(e,O.T,r);try{this.removeOnAbort=new AbortController}catch(e){}this.ee.on("internal-error",((e,t)=>{this.abortHandler&&(0,l.p)("ierr",[C(e),(0,S.t)(),!0,{},this.#r,t],void 0,this.featureName,this.ee)})),this.ee.on(t.G4.REPLAY_RUNNING,(e=>{this.#r=e})),g.gm.addEventListener("unhandledrejection",(e=>{this.abortHandler&&(0,l.p)("err",[k(e),(0,S.t)(),!1,{unhandledPromiseRejection:1},this.#r],void 0,this.featureName,this.ee)}),(0,E.jT)(!1,this.removeOnAbort?.signal)),g.gm.addEventListener("error",(e=>{this.abortHandler&&(0,l.p)("err",[L(e),(0,S.t)(),!1,{},this.#r],void 0,this.featureName,this.ee)}),(0,E.jT)(!1,this.removeOnAbort?.signal)),this.abortHandler=this.#n,this.importAggregator(e)}#n(){this.removeOnAbort?.abort(),this.abortHandler=void 0}}var H=i(8990);let K=1;function U(e){const t=typeof e;return!e||"object"!==t&&"function"!==t?-1:e===g.gm?0:(0,H.I)(e,"nr@id",(function(){return K++}))}function G(e){if("string"==typeof e&&e.length)return e.length;if("object"==typeof e){if("undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer&&e.byteLength)return e.byteLength;if("undefined"!=typeof Blob&&e instanceof Blob&&e.size)return e.size;if(!("undefined"!=typeof FormData&&e instanceof FormData))try{return(0,P.A)(e).length}catch(e){return}}}var V=i(8139),F=i(7836),B=i(3434);const W={},z=["open","send"];function q(t){var r=t||F.ee;const n=function(e){return(e||F.ee).get("xhr")}(r);if(void 0===g.gm.XMLHttpRequest)return n;if(W[n.debugId]++)return n;W[n.debugId]=1,(0,V.u)(r);var i=(0,B.YM)(n),o=g.gm.XMLHttpRequest,a=g.gm.MutationObserver,s=g.gm.Promise,c=g.gm.setInterval,u="readystatechange",d=["onload","onerror","onabort","onloadstart","onloadend","onprogress","ontimeout"],l=[],f=g.gm.XMLHttpRequest=function(t){const r=new o(t),a=n.context(r);try{n.emit("new-xhr",[r],a),r.addEventListener(u,(s=a,function(){var e=this;e.readyState>3&&!s.resolved&&(s.resolved=!0,n.emit("xhr-resolved",[],e)),i.inPlace(e,d,"fn-",y)}),(0,E.jT)(!1))}catch(t){(0,e.R)(15,t);try{n.emit("internal-error",[t])}catch(e){}}var s;return r};function h(e,t){i.inPlace(t,["onreadystatechange"],"fn-",y)}if(function(e,t){for(var r in e)t[r]=e[r]}(o,f),f.prototype=o.prototype,i.inPlace(f.prototype,z,"-xhr-",y),n.on("send-xhr-start",(function(e,t){h(e,t),function(e){l.push(e),a&&(p?p.then(b):c?c(b):(m=-m,v.data=m))}(t)})),n.on("open-xhr-start",h),a){var p=s&&s.resolve();if(!c&&!s){var m=1,v=document.createTextNode(m);new a(b).observe(v,{characterData:!0})}}else r.on("fn-end",(function(e){e[0]&&e[0].type===u||b()}));function b(){for(var e=0;e<l.length;e++)h(0,l[e]);l.length&&(l=[])}function y(e,t){return t}return n}var Z="fetch-",Y=Z+"body-",J=["arrayBuffer","blob","json","text","formData"],X=g.gm.Request,Q=g.gm.Response,ee="prototype";const te={};function re(e){const t=function(e){return(e||F.ee).get("fetch")}(e);if(!(X&&Q&&g.gm.fetch))return t;if(te[t.debugId]++)return t;function r(e,r,n){var i=e[r];"function"==typeof i&&(e[r]=function(){var e,r=[...arguments],o={};t.emit(n+"before-start",[r],o),o[F.P]&&o[F.P].dt&&(e=o[F.P].dt);var a=i.apply(this,r);return t.emit(n+"start",[r,e],a),a.then((function(e){return t.emit(n+"end",[null,e],a),e}),(function(e){throw t.emit(n+"end",[e],a),e}))})}return te[t.debugId]=1,J.forEach((e=>{r(X[ee],e,Y),r(Q[ee],e,Y)})),r(g.gm,"fetch",Z),t.on(Z+"end",(function(e,r){var n=this;if(r){var i=r.headers.get("content-length");null!==i&&(n.rxSize=i),t.emit(Z+"done",[null,r],n)}else t.emit(Z+"done",[e],n)})),t}var ne=i(7485),ie=i(5603);class oe{constructor(e){this.agentIdentifier=e}generateTracePayload(e){if(!this.shouldGenerateTrace(e))return null;var t=(0,ie.o)(this.agentIdentifier);if(!t)return null;var n=(t.accountID||"").toString()||null,i=(t.agentID||"").toString()||null,o=(t.trustKey||"").toString()||null;if(!n||!i)return null;var a=(0,r.ZF)(),s=(0,r.el)(),c=Date.now(),u={spanId:a,traceId:s,timestamp:c};return(e.sameOrigin||this.isAllowedOrigin(e)&&this.useTraceContextHeadersForCors())&&(u.traceContextParentHeader=this.generateTraceContextParentHeader(a,s),u.traceContextStateHeader=this.generateTraceContextStateHeader(a,c,n,i,o)),(e.sameOrigin&&!this.excludeNewrelicHeader()||!e.sameOrigin&&this.isAllowedOrigin(e)&&this.useNewrelicHeaderForCors())&&(u.newrelicHeader=this.generateTraceHeader(a,s,c,n,i,o)),u}generateTraceContextParentHeader(e,t){return"00-"+t+"-"+e+"-01"}generateTraceContextStateHeader(e,t,r,n,i){return i+"@nr=0-1-"+r+"-"+n+"-"+e+"----"+t}generateTraceHeader(e,t,r,n,i,o){if(!("function"==typeof g.gm?.btoa))return null;var a={v:[0,1],d:{ty:"Browser",ac:n,ap:i,id:e,tr:t,ti:r}};return o&&n!==o&&(a.d.tk=o),btoa((0,P.A)(a))}shouldGenerateTrace(e){return this.isDtEnabled()&&this.isAllowedOrigin(e)}isAllowedOrigin(e){var t=!1,r={};if((0,s.gD)(this.agentIdentifier,"distributed_tracing")&&(r=(0,s.D0)(this.agentIdentifier).distributed_tracing),e.sameOrigin)t=!0;else if(r.allowed_origins instanceof Array)for(var n=0;n<r.allowed_origins.length;n++){var i=(0,ne.D)(r.allowed_origins[n]);if(e.hostname===i.hostname&&e.protocol===i.protocol&&e.port===i.port){t=!0;break}}return t}isDtEnabled(){var e=(0,s.gD)(this.agentIdentifier,"distributed_tracing");return!!e&&!!e.enabled}excludeNewrelicHeader(){var e=(0,s.gD)(this.agentIdentifier,"distributed_tracing");return!!e&&!!e.exclude_newrelic_header}useNewrelicHeaderForCors(){var e=(0,s.gD)(this.agentIdentifier,"distributed_tracing");return!!e&&!1!==e.cors_use_newrelic_header}useTraceContextHeadersForCors(){var e=(0,s.gD)(this.agentIdentifier,"distributed_tracing");return!!e&&!!e.cors_use_tracecontext_headers}}var ae=i(9300),se=i(7295),ce=["load","error","abort","timeout"],ue=ce.length,de=(0,x.dV)().o.REQ,le=(0,x.dV)().o.XHR;const fe="X-NewRelic-App-Data";class he extends y{static featureName=ae.T;constructor(e,t=!0){super(e,ae.T,t),this.dt=new oe(e.agentIdentifier),this.handler=(e,t,r,n)=>(0,l.p)(e,t,r,n,this.ee);try{const e={xmlhttprequest:"xhr",fetch:"fetch",beacon:"beacon"};g.gm?.performance?.getEntriesByType("resource").forEach((t=>{if(t.initiatorType in e&&0!==t.responseStatus){const r={status:t.responseStatus},n={rxSize:t.transferSize,duration:Math.floor(t.duration),cbTime:0};pe(r,t.name),this.handler("xhr",[r,n,t.startTime,t.responseEnd,e[t.initiatorType]],void 0,a.K7.ajax)}}))}catch(e){}re(this.ee),q(this.ee),function(e,t,r,n){function i(e){var t=this;t.totalCbs=0,t.called=0,t.cbTime=0,t.end=x,t.ended=!1,t.xhrGuids={},t.lastSize=null,t.loadCaptureCalled=!1,t.params=this.params||{},t.metrics=this.metrics||{},e.addEventListener("load",(function(r){A(t,e)}),(0,E.jT)(!1)),g.lR||e.addEventListener("progress",(function(e){t.lastSize=e.loaded}),(0,E.jT)(!1))}function o(e){this.params={method:e[0]},pe(this,e[1]),this.metrics={}}function s(t,r){e.loader_config.xpid&&this.sameOrigin&&r.setRequestHeader("X-NewRelic-ID",e.loader_config.xpid);var i=n.generateTracePayload(this.parsedOrigin);if(i){var o=!1;i.newrelicHeader&&(r.setRequestHeader("newrelic",i.newrelicHeader),o=!0),i.traceContextParentHeader&&(r.setRequestHeader("traceparent",i.traceContextParentHeader),i.traceContextStateHeader&&r.setRequestHeader("tracestate",i.traceContextStateHeader),o=!0),o&&(this.dt=i)}}function c(e,r){var n=this.metrics,i=e[0],o=this;if(n&&i){var a=G(i);a&&(n.txSize=a)}this.startTime=(0,S.t)(),this.body=i,this.listener=function(e){try{"abort"!==e.type||o.loadCaptureCalled||(o.params.aborted=!0),("load"!==e.type||o.called===o.totalCbs&&(o.onloadCalled||"function"!=typeof r.onload)&&"function"==typeof o.end)&&o.end(r)}catch(e){try{t.emit("internal-error",[e])}catch(e){}}};for(var s=0;s<ue;s++)r.addEventListener(ce[s],this.listener,(0,E.jT)(!1))}function u(e,t,r){this.cbTime+=e,t?this.onloadCalled=!0:this.called+=1,this.called!==this.totalCbs||!this.onloadCalled&&"function"==typeof r.onload||"function"!=typeof this.end||this.end(r)}function d(e,t){var r=""+U(e)+!!t;this.xhrGuids&&!this.xhrGuids[r]&&(this.xhrGuids[r]=!0,this.totalCbs+=1)}function f(e,t){var r=""+U(e)+!!t;this.xhrGuids&&this.xhrGuids[r]&&(delete this.xhrGuids[r],this.totalCbs-=1)}function h(){this.endTime=(0,S.t)()}function p(e,r){r instanceof le&&"load"===e[0]&&t.emit("xhr-load-added",[e[1],e[2]],r)}function m(e,r){r instanceof le&&"load"===e[0]&&t.emit("xhr-load-removed",[e[1],e[2]],r)}function v(e,t,r){t instanceof le&&("onload"===r&&(this.onload=!0),("load"===(e[0]&&e[0].type)||this.onload)&&(this.xhrCbStart=(0,S.t)()))}function b(e,r){this.xhrCbStart&&t.emit("xhr-cb-time",[(0,S.t)()-this.xhrCbStart,this.onload,r],r)}function y(e){var t,r=e[1]||{};if("string"==typeof e[0]?0===(t=e[0]).length&&g.RI&&(t=""+g.gm.location.href):e[0]&&e[0].url?t=e[0].url:g.gm?.URL&&e[0]&&e[0]instanceof URL?t=e[0].href:"function"==typeof e[0].toString&&(t=e[0].toString()),"string"==typeof t&&0!==t.length){t&&(this.parsedOrigin=(0,ne.D)(t),this.sameOrigin=this.parsedOrigin.sameOrigin);var i=n.generateTracePayload(this.parsedOrigin);if(i&&(i.newrelicHeader||i.traceContextParentHeader))if(e[0]&&e[0].headers)s(e[0].headers,i)&&(this.dt=i);else{var o={};for(var a in r)o[a]=r[a];o.headers=new Headers(r.headers||{}),s(o.headers,i)&&(this.dt=i),e.length>1?e[1]=o:e.push(o)}}function s(e,t){var r=!1;return t.newrelicHeader&&(e.set("newrelic",t.newrelicHeader),r=!0),t.traceContextParentHeader&&(e.set("traceparent",t.traceContextParentHeader),t.traceContextStateHeader&&e.set("tracestate",t.traceContextStateHeader),r=!0),r}}function w(e,t){this.params={},this.metrics={},this.startTime=(0,S.t)(),this.dt=t,e.length>=1&&(this.target=e[0]),e.length>=2&&(this.opts=e[1]);var r,n=this.opts||{},i=this.target;"string"==typeof i?r=i:"object"==typeof i&&i instanceof de?r=i.url:g.gm?.URL&&"object"==typeof i&&i instanceof URL&&(r=i.href),pe(this,r);var o=(""+(i&&i instanceof de&&i.method||n.method||"GET")).toUpperCase();this.params.method=o,this.body=n.body,this.txSize=G(n.body)||0}function R(e,t){if(this.endTime=(0,S.t)(),this.params||(this.params={}),(0,se.iW)(this.params))return;let n;this.params.status=t?t.status:0,"string"==typeof this.rxSize&&this.rxSize.length>0&&(n=+this.rxSize);const i={txSize:this.txSize,rxSize:n,duration:(0,S.t)()-this.startTime};r("xhr",[this.params,i,this.startTime,this.endTime,"fetch"],this,a.K7.ajax)}function x(e){const t=this.params,n=this.metrics;if(!this.ended){this.ended=!0;for(let t=0;t<ue;t++)e.removeEventListener(ce[t],this.listener,!1);t.aborted||(0,se.iW)(t)||(n.duration=(0,S.t)()-this.startTime,this.loadCaptureCalled||4!==e.readyState?null==t.status&&(t.status=0):A(this,e),n.cbTime=this.cbTime,r("xhr",[t,n,this.startTime,this.endTime,"xhr"],this,a.K7.ajax))}}function A(e,r){e.params.status=r.status;var n=function(e,t){var r=e.responseType;return"json"===r&&null!==t?t:"arraybuffer"===r||"blob"===r||"json"===r?G(e.response):"text"===r||""===r||void 0===r?G(e.responseText):void 0}(r,e.lastSize);if(n&&(e.metrics.rxSize=n),e.sameOrigin&&r.getAllResponseHeaders().indexOf(fe)>=0){var i=r.getResponseHeader(fe);i&&((0,l.p)(N.rs,["Ajax/CrossApplicationTracing/Header/Seen"],void 0,a.K7.metrics,t),e.params.cat=i.split(", ").pop())}e.loadCaptureCalled=!0}t.on("new-xhr",i),t.on("open-xhr-start",o),t.on("open-xhr-end",s),t.on("send-xhr-start",c),t.on("xhr-cb-time",u),t.on("xhr-load-added",d),t.on("xhr-load-removed",f),t.on("xhr-resolved",h),t.on("addEventListener-end",p),t.on("removeEventListener-end",m),t.on("fn-end",b),t.on("fetch-before-start",y),t.on("fetch-start",w),t.on("fn-start",v),t.on("fetch-done",R)}(e,this.ee,this.handler,this.dt),this.importAggregator(e)}}function pe(e,t){var r=(0,ne.D)(t),n=e.params||e;n.hostname=r.hostname,n.port=r.port,n.protocol=r.protocol,n.host=r.hostname+":"+r.port,n.pathname=r.pathname,e.parsedOrigin=r,e.sameOrigin=r.sameOrigin}const ge={},me=["pushState","replaceState"];function ve(e){const t=function(e){return(e||F.ee).get("history")}(e);return!g.RI||ge[t.debugId]++||(ge[t.debugId]=1,(0,B.YM)(t).inPlace(window.history,me,"-")),t}var be=i(3738);const{He:ye,bD:we,d3:Re,Kp:xe,TZ:Ae,Lc:Ee,uP:Te,Rz:Se}=be;class Ie extends y{static featureName=Ae;constructor(e,t=!0){super(e,Ae,t);if(!(0,v.V)(this.agentIdentifier))return void this.deregisterDrain();const r=this.ee;let n;ve(r),this.eventsEE=(0,V.u)(r),this.eventsEE.on(Te,(function(e,t){this.bstStart=(0,S.t)()})),this.eventsEE.on(Ee,(function(e,t){(0,l.p)("bst",[e[0],t,this.bstStart,(0,S.t)()],void 0,a.K7.sessionTrace,r)})),r.on(Se+Re,(function(e){this.time=(0,S.t)(),this.startPath=location.pathname+location.hash})),r.on(Se+xe,(function(e){(0,l.p)("bstHist",[location.pathname+location.hash,this.startPath,this.time],void 0,a.K7.sessionTrace,r)}));try{n=new PerformanceObserver((e=>{const t=e.getEntries();(0,l.p)(ye,[t],void 0,a.K7.sessionTrace,r)})),n.observe({type:we,buffered:!0})}catch(e){}this.importAggregator(e,{resourceObserver:n})}}var Ne=i(2614);class _e extends y{static featureName=t.TZ;#i;#o;constructor(e,r=!0){let n;super(e,t.TZ,r),this.replayRunning=!1,this.#o=e;try{n=JSON.parse(localStorage.getItem("".concat(Ne.H3,"_").concat(Ne.uh)))}catch(e){}(0,m.SR)(e.agentIdentifier)&&this.ee.on(t.G4.RECORD,(()=>this.#a())),this.#s(n)?(this.#i=n?.sessionReplayMode,this.#c()):this.importAggregator(e),this.ee.on("err",(e=>{this.replayRunning&&(this.errorNoticed=!0,(0,l.p)(t.G4.ERROR_DURING_REPLAY,[e],void 0,this.featureName,this.ee))})),this.ee.on(t.G4.REPLAY_RUNNING,(e=>{this.replayRunning=e}))}#s(e){return e&&(e.sessionReplayMode===Ne.g.FULL||e.sessionReplayMode===Ne.g.ERROR)||(0,m.Aw)(this.agentIdentifier)}#u=!1;async#c(e){if(!this.#u){this.#u=!0;try{const{Recorder:t}=await Promise.all([i.e(478),i.e(249)]).then(i.bind(i,8589));this.recorder??=new t({mode:this.#i,agentIdentifier:this.agentIdentifier,trigger:e,ee:this.ee,agentRef:this.#o}),this.recorder.startRecording(),this.abortHandler=this.recorder.stopRecording}catch(e){}this.importAggregator(this.#o,{recorder:this.recorder,errorNoticed:this.errorNoticed})}}#a(){this.featAggregate?this.featAggregate.mode!==Ne.g.FULL&&this.featAggregate.initializeRecording(Ne.g.FULL,!0):(this.#i=Ne.g.FULL,this.#c(t.Qb.API),this.recorder&&this.recorder.parent.mode!==Ne.g.FULL&&(this.recorder.parent.mode=Ne.g.FULL,this.recorder.stopRecording(),this.recorder.startRecording(),this.abortHandler=this.recorder.stopRecording))}}var Oe=i(3962);class Pe extends y{static featureName=Oe.TZ;constructor(e,t=!0){if(super(e,Oe.TZ,t),!g.RI||!(0,x.dV)().o.MO)return;const r=ve(this.ee);Oe.tC.forEach((e=>{(0,E.sp)(e,(e=>{a(e)}),!0)}));const n=()=>(0,l.p)("newURL",[(0,S.t)(),""+window.location],void 0,this.featureName,this.ee);r.on("pushState-end",n),r.on("replaceState-end",n);try{this.removeOnAbort=new AbortController}catch(e){}(0,E.sp)("popstate",(e=>(0,l.p)("newURL",[e.timeStamp,""+window.location],void 0,this.featureName,this.ee)),!0,this.removeOnAbort?.signal);let i=!1;const o=new((0,x.dV)().o.MO)(((e,t)=>{i||(i=!0,requestAnimationFrame((()=>{(0,l.p)("newDom",[(0,S.t)()],void 0,this.featureName,this.ee),i=!1})))})),a=(0,b.s)((e=>{(0,l.p)("newUIEvent",[e],void 0,this.featureName,this.ee),o.observe(document.body,{attributes:!0,childList:!0,subtree:!0,characterData:!0})}),100,{leading:!0});this.abortHandler=function(){this.removeOnAbort?.abort(),o.disconnect(),this.abortHandler=void 0},this.importAggregator(e,{domObserver:o})}}var je=i(7378);const Ce={},ke=["appendChild","insertBefore","replaceChild"];function Le(e){const t=function(e){return(e||F.ee).get("jsonp")}(e);if(!g.RI||Ce[t.debugId])return t;Ce[t.debugId]=!0;var r=(0,B.YM)(t),n=/[?&](?:callback|cb)=([^&#]+)/,i=/(.*)\.([^.]+)/,o=/^(\w+)(\.|$)(.*)$/;function a(e,t){if(!e)return t;const r=e.match(o),n=r[1];return a(r[3],t[n])}return r.inPlace(Node.prototype,ke,"dom-"),t.on("dom-start",(function(e){!function(e){if(!e||"string"!=typeof e.nodeName||"script"!==e.nodeName.toLowerCase())return;if("function"!=typeof e.addEventListener)return;var o=(s=e.src,c=s.match(n),c?c[1]:null);var s,c;if(!o)return;var u=function(e){var t=e.match(i);if(t&&t.length>=3)return{key:t[2],parent:a(t[1],window)};return{key:e,parent:window}}(o);if("function"!=typeof u.parent[u.key])return;var d={};function l(){t.emit("jsonp-end",[],d),e.removeEventListener("load",l,(0,E.jT)(!1)),e.removeEventListener("error",f,(0,E.jT)(!1))}function f(){t.emit("jsonp-error",[],d),t.emit("jsonp-end",[],d),e.removeEventListener("load",l,(0,E.jT)(!1)),e.removeEventListener("error",f,(0,E.jT)(!1))}r.inPlace(u.parent,[u.key],"cb-",d),e.addEventListener("load",l,(0,E.jT)(!1)),e.addEventListener("error",f,(0,E.jT)(!1)),t.emit("new-jsonp",[e.src],d)}(e[0])})),t}const De={};function Me(e){const t=function(e){return(e||F.ee).get("promise")}(e);if(De[t.debugId])return t;De[t.debugId]=!0;var r=t.context,n=(0,B.YM)(t),i=g.gm.Promise;return i&&function(){function e(r){var o=t.context(),a=n(r,"executor-",o,null,!1);const s=Reflect.construct(i,[a],e);return t.context(s).getCtx=function(){return o},s}g.gm.Promise=e,Object.defineProperty(e,"name",{value:"Promise"}),e.toString=function(){return i.toString()},Object.setPrototypeOf(e,i),["all","race"].forEach((function(r){const n=i[r];e[r]=function(e){let i=!1;[...e||[]].forEach((e=>{this.resolve(e).then(a("all"===r),a(!1))}));const o=n.apply(this,arguments);return o;function a(e){return function(){t.emit("propagate",[null,!i],o,!1,!1),i=i||!e}}}})),["resolve","reject"].forEach((function(r){const n=i[r];e[r]=function(e){const r=n.apply(this,arguments);return e!==r&&t.emit("propagate",[e,!0],r,!1,!1),r}})),e.prototype=i.prototype;const o=i.prototype.then;i.prototype.then=function(...e){var i=this,a=r(i);a.promise=i,e[0]=n(e[0],"cb-",a,null,!1),e[1]=n(e[1],"cb-",a,null,!1);const s=o.apply(this,e);return a.nextPromise=s,t.emit("propagate",[i,!0],s,!1,!1),s},i.prototype.then[B.Jt]=o,t.on("executor-start",(function(e){e[0]=n(e[0],"resolve-",this,null,!1),e[1]=n(e[1],"resolve-",this,null,!1)})),t.on("executor-err",(function(e,t,r){e[1](r)})),t.on("cb-end",(function(e,r,n){t.emit("propagate",[n,!0],this.nextPromise,!1,!1)})),t.on("propagate",(function(e,r,n){this.getCtx&&!r||(this.getCtx=function(){if(e instanceof Promise)var r=t.context(e);return r&&r.getCtx?r.getCtx():this})}))}(),t}const He={},Ke="setTimeout",Ue="setInterval",Ge="clearTimeout",Ve="-start",Fe=[Ke,"setImmediate",Ue,Ge,"clearImmediate"];function Be(e){const t=function(e){return(e||F.ee).get("timer")}(e);if(He[t.debugId]++)return t;He[t.debugId]=1;var r=(0,B.YM)(t);return r.inPlace(g.gm,Fe.slice(0,2),Ke+"-"),r.inPlace(g.gm,Fe.slice(2,3),Ue+"-"),r.inPlace(g.gm,Fe.slice(3),Ge+"-"),t.on(Ue+Ve,(function(e,t,n){e[0]=r(e[0],"fn-",null,n)})),t.on(Ke+Ve,(function(e,t,n){this.method=n,this.timerDuration=isNaN(e[1])?0:+e[1],e[0]=r(e[0],"fn-",this,n)})),t}const We={};function ze(e){const t=function(e){return(e||F.ee).get("mutation")}(e);if(!g.RI||We[t.debugId])return t;We[t.debugId]=!0;var r=(0,B.YM)(t),n=g.gm.MutationObserver;return n&&(window.MutationObserver=function(e){return this instanceof n?new n(r(e,"fn-")):n.apply(this,arguments)},MutationObserver.prototype=n.prototype),t}const{TZ:qe,d3:Ze,Kp:Ye,$p:Je,wW:Xe,e5:$e,tH:Qe,uP:et,rw:tt,Lc:rt}=je;class nt extends y{static featureName=qe;constructor(e,t=!0){if(super(e,qe,t),!g.RI)return;try{this.removeOnAbort=new AbortController}catch(e){}let r,n=0;const i=this.ee.get("tracer"),o=Le(this.ee),a=Me(this.ee),s=Be(this.ee),c=q(this.ee),u=this.ee.get("events"),d=re(this.ee),f=ve(this.ee),h=ze(this.ee);function p(e,t){f.emit("newURL",[""+window.location,t])}function m(){n++,r=window.location.hash,this[et]=(0,S.t)()}function v(){n--,window.location.hash!==r&&p(0,!0);var e=(0,S.t)();this[$e]=~~this[$e]+e-this[et],this[rt]=e}function b(e,t){e.on(t,(function(){this[t]=(0,S.t)()}))}this.ee.on(et,m),a.on(tt,m),o.on(tt,m),this.ee.on(rt,v),a.on(Xe,v),o.on(Xe,v),this.ee.on("fn-err",((...t)=>{t[2]?.__newrelic?.[e.agentIdentifier]||(0,l.p)("function-err",[...t],void 0,this.featureName,this.ee)})),this.ee.buffer([et,rt,"xhr-resolved"],this.featureName),u.buffer([et],this.featureName),s.buffer(["setTimeout"+Ye,"clearTimeout"+Ze,et],this.featureName),c.buffer([et,"new-xhr","send-xhr"+Ze],this.featureName),d.buffer([Qe+Ze,Qe+"-done",Qe+Je+Ze,Qe+Je+Ye],this.featureName),f.buffer(["newURL"],this.featureName),h.buffer([et],this.featureName),a.buffer(["propagate",tt,Xe,"executor-err","resolve"+Ze],this.featureName),i.buffer([et,"no-"+et],this.featureName),o.buffer(["new-jsonp","cb-start","jsonp-error","jsonp-end"],this.featureName),b(d,Qe+Ze),b(d,Qe+"-done"),b(o,"new-jsonp"),b(o,"jsonp-end"),b(o,"cb-start"),f.on("pushState-end",p),f.on("replaceState-end",p),window.addEventListener("hashchange",p,(0,E.jT)(!0,this.removeOnAbort?.signal)),window.addEventListener("load",p,(0,E.jT)(!0,this.removeOnAbort?.signal)),window.addEventListener("popstate",(function(){p(0,n>1)}),(0,E.jT)(!0,this.removeOnAbort?.signal)),this.abortHandler=this.#n,this.importAggregator(e)}#n(){this.removeOnAbort?.abort(),this.abortHandler=void 0}}var it=i(3333);class ot extends y{static featureName=it.TZ;constructor(e,t=!0){super(e,it.TZ,t);const r=[e.init.page_action.enabled,e.init.performance.capture_marks,e.init.performance.capture_measures,e.init.user_actions.enabled,e.init.performance.resources.enabled];if(g.RI&&(e.init.user_actions.enabled&&(it.Zp.forEach((e=>(0,E.sp)(e,(e=>(0,l.p)("ua",[e],void 0,this.featureName,this.ee)),!0))),it.qN.forEach((e=>{const t=(0,b.s)((e=>{(0,l.p)("ua",[e],void 0,this.featureName,this.ee)}),500,{leading:!0});(0,E.sp)(e,t)}))),e.init.performance.resources.enabled&&g.gm.PerformanceObserver?.supportedEntryTypes.includes("resource"))){new PerformanceObserver((e=>{e.getEntries().forEach((e=>{(0,l.p)("browserPerformance.resource",[e],void 0,this.featureName,this.ee)}))})).observe({type:"resource",buffered:!0})}r.some((e=>e))?this.importAggregator(e):this.deregisterDrain()}}var at=i(993),st=i(3785),ct=i(9414);class ut extends y{static featureName=at.TZ;constructor(e,t=!0){super(e,at.TZ,t);const r=this.ee;(0,ct.J)(r,g.gm.console,"log",{level:"info"}),(0,ct.J)(r,g.gm.console,"error",{level:"error"}),(0,ct.J)(r,g.gm.console,"warn",{level:"warn"}),(0,ct.J)(r,g.gm.console,"info",{level:"info"}),(0,ct.J)(r,g.gm.console,"debug",{level:"debug"}),(0,ct.J)(r,g.gm.console,"trace",{level:"trace"}),this.ee.on("wrap-logger-end",(function([e]){const{level:t,customAttributes:n}=this;(0,st.R)(r,e,n,t)})),this.importAggregator(e)}}new class extends o{constructor(t){super(),g.gm?(this.features={},(0,x.bQ)(this.agentIdentifier,this),this.desiredFeatures=new Set(t.features||[]),this.desiredFeatures.add(R),this.runSoftNavOverSpa=[...this.desiredFeatures].some((e=>e.featureName===a.K7.softNav)),(0,d.j)(this,t,t.loaderType||"agent"),this.run()):(0,e.R)(21)}get config(){return{info:this.info,init:this.init,loader_config:this.loader_config,runtime:this.runtime}}get api(){return this}run(){try{const t=u(this.agentIdentifier),r=[...this.desiredFeatures];r.sort(((e,t)=>a.P3[e.featureName]-a.P3[t.featureName])),r.forEach((r=>{if(!t[r.featureName]&&r.featureName!==a.K7.pageViewEvent)return;if(this.runSoftNavOverSpa&&r.featureName===a.K7.spa)return;if(!this.runSoftNavOverSpa&&r.featureName===a.K7.softNav)return;const n=function(e){switch(e){case a.K7.ajax:return[a.K7.jserrors];case a.K7.sessionTrace:return[a.K7.ajax,a.K7.pageViewEvent];case a.K7.sessionReplay:return[a.K7.sessionTrace];case a.K7.pageViewTiming:return[a.K7.pageViewEvent];default:return[]}}(r.featureName).filter((e=>!(e in this.features)));n.length>0&&(0,e.R)(36,{targetFeature:r.featureName,missingDependencies:n}),this.features[r.featureName]=new r(this)}))}catch(t){(0,e.R)(22,t);for(const e in this.features)this.features[e].abortHandler?.();const r=(0,x.Zm)();delete r.initializedAgents[this.agentIdentifier]?.features,delete this.sharedAggregator;return r.ee.get(this.agentIdentifier).abort(),!1}}}({features:[he,R,I,Ie,_e,_,M,ot,ut,Pe,nt],loaderType:"spa"})})()})();</script><link rel="canonical" href="https://www.blackrockgalleries.com/404"/>
<title>Black Rock Galleries | We're sorry...</title>
<meta name="description" content="We're sorry..."/>
<meta name="keywords" content="We're sorry..."/>
<meta content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no" name="viewport" /><link href="css/style.css" media="screen" rel="stylesheet" type="text/css" /><link href="css/responsive.css" media="screen" rel="stylesheet" type="text/css" /><link rel="stylesheet" href="css/sidebar.css" /><script src="js/jquery.min.js"></script><script src="js/sidebar.js"></script><script  src="js/custom.js"></script><script src="js/unslider.min.js" ></script><link rel="stylesheet" href="css/uniform.default.css" type="text/css" media="screen">
<script src="js/jquery.uniform.js"  ></script><script  >
	$(function () {
	var $min, $remove, $apply, $uniformed;
	$selects = $("input[type=checkbox]").click(function () {
		if (typeof console !== 'undefined' && typeof console.log !== 'undefined') {
			console.log($(this).attr('id') + " clicked");
		}
	});
	$uniformed = $(".qus-inner").find("input[type=checkbox], input[type=radio], select, input[type=file], a.uniformTest").not(".qus-inner");
	$uniformed.uniform();
});
</script>
<!--[if IE]>
  <script src="http://html5shiv.googlecode.com/svn/trunk/html5.js"></script>
<![endif]-->
<link rel="apple-touch-icon" sizes="57x57" href="favicon_icon/apple-icon-57x57.png"><link rel="apple-touch-icon" sizes="60x60" href="favicon_icon/apple-icon-60x60.png"><link rel="apple-touch-icon" sizes="72x72" href="favicon_icon/apple-icon-72x72.png"><link rel="apple-touch-icon" sizes="76x76" href="favicon_icon/apple-icon-76x76.png"><link rel="apple-touch-icon" sizes="114x114" href="favicon_icon/apple-icon-114x114.png"><link rel="apple-touch-icon" sizes="120x120" href="favicon_icon/apple-icon-120x120.png"><link rel="apple-touch-icon" sizes="144x144" href="favicon_icon/apple-icon-144x144.png"><link rel="apple-touch-icon" sizes="152x152" href="favicon_icon/apple-icon-152x152.png"><link rel="apple-touch-icon" sizes="180x180" href="favicon_icon/apple-icon-180x180.png"><link rel="icon" type="image/png" sizes="192x192"  href="favicon_icon/android-icon-192x192.png"><link rel="icon" type="image/png" sizes="32x32" href="favicon_icon/favicon-32x32.png"><link rel="icon" type="image/png" sizes="96x96" href="favicon_icon/favicon-96x96.png"><link rel="icon" type="image/png" sizes="16x16" href="favicon_icon/favicon-16x16.png"><link rel="manifest" href="favicon_icon/manifest.json"><meta name="msapplication-TileColor" content="#ffffff"><meta name="msapplication-TileImage" content="favicon_icon/ms-icon-144x144.png"><meta name="theme-color" content="#ffffff">


<style>body{ background:#454f52; }</style><style>.footer-1{font-size:15px!important;}.eximg1{margin-top:7px!important;}.exspam1{color:#FFFFFF!important;font-size:13px!important;text-transform:none!important;}.exlink1{color:#FFFFFF!important;}#slider1 li { cursor:pointer!important;} .exp1{background:none!important;padding-bottom:0px!important;margin-bottom:0px!important;}.exli1{padding:0px!important;}.current-section ul li .text { border-top:0px solid #ffffff!important;padding:0 0 15px 0!important; }.exp2{text-transform:none!important;}.eximg2{margin-bottom:0px!important;}.exalign1{text-align:center!important;}.exbtn1{margin-bottom:10px!important;}.exh41{font-size:22px!important;}.exdisp1{display:none;}.exmodle1{min-height:300px!important;}.h2color{ color:#454f52!important;font-size:1px!important; }.spamcolor{ color:#FF0000!important; }.fulliwdth{ float:none!important;width:100%!important; }.fltlft{ float:left!important; }.fltrght{ float:right!important; }.disnone{ display:none; }.linethrw{ text-decoration:line-through!important; }.excoll1{padding:0!important;background:none!important;}excoll2{padding:0px!important;padding-bottom:20px!important;}.bordr0{ border:0!important; }.hight70{ height:70px!important; }.paddng25{ padding:0 25px!important; }.h1color{ color:#ffffff!important;font-size:1px!important; }.pdtl1{ width:100%!important;border:1px solid #CCCCCC!important; }.pdtl2{ border:1px solid #CCCCCC!important; }.pdtl3{ padding-bottom:10px!important; }.pdtl4{ color:#444444!important;text-transform:uppercase!important; }.pdtl5{ font-size:14px!important;color:#979797!important;font-family:"source_sans_probold"; }.pdtl6{ color:#979797!important; }.pdtl7{ color:#009dcd!important; }.pdtl8{position:relative!important; }.aucdtl1{width:100%!important; }.aucdtl2{color:#444444!important; }.aucdtl3{width:100%!important;float:left!important;position:relative!important; }.aucdtl4{text-decoration:none!important; }.aucdtl5{height:85px!important; }.aucdtl6{min-height:75px!important; }.aucdtl7{text-align:left!important; }
.top-blt .icon-contact-img{margin:0;padding:0 15px;position:relative;}
.top-blt .icon-contact-img img{max-width:100%;margin:0;vertical-align:middle;}
.notif-box {background: #f00;font-size: 12px;height: 16px;line-height: 16px;width: 16px;text-align: center;border-radius: 60px;font-weight: bold;position: absolute;top: 7px;right: 8px;}
.topbltstrong{padding-left:15px;margin:0;}
.aucbold{width: 100%;float: left;padding: 0 0 20px 0;margin: 0 0 20px 0;color: #444;border-bottom: 1px solid #bae5f2;font-size: 18px;text-transform: uppercase;font-family: source_sans_probold;font-weight: bold;}
.pastauch4 {text-transform: uppercase;font-family: 'source_sans_probold';font-size: 18px;color: #444444;margin-bottom: 5px;font-weight: bold;height: 40px;overflow: hidden;text-align: center;display:inline-block;}
.blogh4 {font-size: 28px!important;line-height: 30px!important;margin: 15px 0 0!important;font-weight: bold!important;text-align: center!important;color: #444444!important;padding: 0 0 8px!important;}
.ftrHclass{font-family: source_sans_probold;color: #fff;font-size: 18px;margin-bottom: 15px;text-transform: uppercase;font-weight: 400;}
.ftrimgwidth{max-width:150px!important;}.anlogowidth{width:160px!important;} @media only screen and (max-width: 480px) { .footer-11 { display:inline;clear:both;float:left; } .footer-1 { display:none; } .anlogowidth{width:150px!important;} }
@media only screen and (max-width: 480px) { .ftrlocspace{padding-top:20px;} }
.imgborder{ border:0!important; }
.width100{ width:100%; }.width50{ width:50%; }.width20{ width:20px; }.width80{ width:80px; }.width300{ width:300px; }.width38{ width:38px; }
.tdbold{ font-weight:bold!important; }.tdfont14{ font-size:14px; }.tdfont18{ font-size:18px; }
.imgalign{ vertical-align:middle!important; }.imgaligntop{ vertical-align:top!important; }
.msgalign{ text-align:center!important; }
.msgalignleft{ text-align:left!important; }
.tdaligntop{ vertical-align:top!important;text-align:left!important; }
.tdalignright{ vertical-align:top!important;text-align:right!important; }
.carttxt{ text-align:center!important;font-weight:bold;vertical-align:middle!important;height:170px;padding-top:80px; }
.listh1cls{font-family:'source_sans_proregular';font-size: 14px;display: inline;}
</style><!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-J9KC8GX3Y8"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'G-J9KC8GX3Y8');
</script><style>
#mainwrapper2 {display: flex;flex-direction: column;height: 100vh;margin: 0;}
#footerwrapper {margin-top: auto;}
#middlerwrapper {padding-bottom: 18px;margin-bottom: 0;}
#middlerwrapper {background: #f4f4f4;}
.jsc-sidebar{display:none;}
@media only screen and (max-width: 991px) { .jsc-sidebar{display:block;} }
@media print {
  a[href]:after {
    content: none !important;
  }
}
</style>
<!-- Facebook Pixel Code -->
<meta name="facebook-domain-verification" content="0pqgff9n1585iqjqxpb8nupcbriq3n" />
<script>
!function(f,b,e,v,n,t,s)
{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};
if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];
s.parentNode.insertBefore(t,s)}(window, document,'script','https://connect.facebook.net/en_US/fbevents.js');
fbq('init', '491635321175422');
fbq('track', 'PageView');
</script>
<noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=491635321175422&ev=PageView&noscript=1"/></noscript>
<!-- End Facebook Pixel Code -->
<!-- Meta Pixel Code -->
<script>
!function(f,b,e,v,n,t,s)
{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};
if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];
s.parentNode.insertBefore(t,s)}(window, document,'script',
'https://connect.facebook.net/en_US/fbevents.js');
fbq('init', '813756370514821');
fbq('track', 'PageView');
</script>
<noscript><img height="1" width="1" style="display:none" src="https://www.facebook.com/tr?id=813756370514821&ev=PageView&noscript=1"/></noscript>
<!-- End Meta Pixel Code -->
<style>
.termscss1{ margin: 0px; font-size: 11px; line-height: normal; font-family: Helvetica; -webkit-text-stroke-color: rgb(0, 0, 0); -webkit-text-stroke-width: initial; }
.termscss2{ margin: 0px; font-size: 11px; line-height: normal; font-family: Helvetica; -webkit-text-stroke-color: rgb(0, 0, 0); -webkit-text-stroke-width: initial; min-height: 13px; }
.termscss3{font-kerning: none; }
.termscss4{text-decoration: underline ; font-kerning: none; }
</style></head>
<body><div class="mainwrapper jsc-sidebar-content jsc-sidebar-pulled">
  <div id="main">
    <div id="mobile-menu"><a href="#" class="jsc-sidebar-trigger" id="menu-btn" title="Black Rock Galleries" >&nbsp;</a></div>    <div id="mainwrapper2">
      <div id="haederwrapper"><div class="top-blt"><div class="wrapper"><div class="top-contact"><a  href="contact">Contact</a>: <a  href="tel:**************">************</a></div><a  href="tel:**************" class="top-contact-mobile">************</a><ul><li><a  href="login"><img src="images/login.png" class="imgborder imgalign" title="Black Rock Galleries" alt="Black Rock Galleries"><span>Login</span></a></li><li><a  href="signup"><img src="images/signup.png" class="imgborder imgalign" title="Black Rock Galleries" alt="Black Rock Galleries"><span>SIGN UP</span></a></li><li class="last"><a  href="cart"><img class="topbltstrong imgborder imgalign" src="images/cart.png" title="Black Rock Galleries" alt="Black Rock Galleries"><span>0 items - $0.00</span></a></li> </ul></div></div><div class="header"><div class="wrapper"><div class="head-left"><div class="my-brand"> <a  href="https://www.blackrockgalleries.com/" title="Black Rock Galleries"><img src="images/logo.png" class="imgborder" title="Black Rock Galleries" alt="Black Rock Galleries"/></a> </div></div><div class="head-right"><div class="main-navigation"><ul><li><a  href="about-us" title="Black Rock Galleries" >About</a></li><li><a  href="online-auctions" title="Black Rock Galleries" >Auctions</a><ul class="sub-navigations"><li><a  href="online-auctions">Current Auctions</a></li><li><a  href="upcoming-collections">Upcoming Auctions</a></li><li><a  href="past-online-auctions">Past Auctions</a></li><li><a  href="calendar">Auction Calendar</a></li></ul></li><li><a  href="our-services" title="Black Rock Galleries" >Services</a><ul class="sub-navigations sn-50"><li class="sn-left"><a  href="auctions">Online Auctions</a></li><li class="sn-left"><a  href="sell-consign">Outright Purchase</a></li><li class="sn-left"><a  href="estate-sales">Estate Sales</a></li><li class="sn-left"><a  href="clean-outs">Clean Outs</a></li><li class="sn-left"><a  href="appraisals">Appraisals</a></li><li class="sn-left"><a  href="other-services">Other</a></li>
</ul></li><li><a  href="shop-now" title="Black Rock Galleries" >Store</a><ul class="sub-navigations sn-25"><li class="sn-left"><a  href="categories/Art">Art</a></li>
<li class="sn-left"><a  href="categories/Furniture">Furniture</a></li>
<li class="sn-left"><a  href="categories/Objects">Objects</a></li>
<li class="sn-left"><a  href="categories/Tabletop">Tabletop</a></li>
<li class="sn-left"><a  href="categories/Lighting">Lighting</a></li>
<li class="sn-left"><a  href="categories/Carpets">Carpets</a></li>
<li class="sn-left"><a  href="categories/Mirrors">Mirrors</a></li>
<li class="sn-left"><a  href="categories/Fireplace">Fireplace</a></li>
<li class="sn-left"><a  href="categories/Garden">Garden</a></li>
<li class="sn-left"><a  href="categories/Etc-">Etc...</a></li>
<li class="sn-left"><a  href="categories/Jewelry">Jewelry</a></li>
<li class="sn-left"><a  href="categories/Musical-Instruments">Musical Instruments</a></li>
<li class="sn-left"><a  href="categories/Other">Motor Vehicles</a></li>
<li class="sn-left"><a  href="shop-now" style="color:#009dcd;font-weight:bold;">SALE</a></li></ul></li><li><a  href="blog" title="Black Rock Galleries" >Blog</a></li><li><a  href="contact" title="Black Rock Galleries" >Contact Us</a></li></ul></div>
<div class="top-search"><form name="Topsearchfrm" method="get" enctype="multipart/form-data" action="search_mid.php"><input name="keyword" id="keyword" type="text" class="searchbox" placeholder="Search..."><input onclick="document.Topsearchfrm.submit();" type="button" class="searchbtn" value="Search"></form></div>
</div></div></div></div><script>function AddtoCartAjax(AddtoCartAjax_ID,itemid,price,quantity,colorr,sizee)
{
	var http7333 = false;
	if(navigator.appName == "Microsoft Internet Explorer") { http7333 = new ActiveXObject("Microsoft.XMLHTTP");} else { http7333 = new XMLHttpRequest();}
	http7333.abort();
	http7333.open("GET","ajax_validation.php?Type=AddtoCartAjax&itemid="+itemid+"&price="+price+"&quantity="+quantity+"&colorr="+colorr+"&sizee="+sizee, true);
	http7333.onreadystatechange=function()
	{
		  if(http7333.readyState == 4) 
		  {
			  //alert(http7333.responseText);return false;
			  //var aa,bb; aa=http7333.responseText.split("OOOPPPOOO"); var totitm=aa[0]; 
			  window.location.href='cart';return false;
		  } 
	}
	http7333.send(null);
}</script>
<script type="application/ld+json">
{
  "@context": {
    "@vocab": "http://schema.org/"
  },
  "@graph": [
    {
      "@id": "https://www.blackrockgalleries.com/",
      "@type": "Organization",
      "name": "Black Rock Galleries",
      "alternateName": "BRG",
	  "url" : "https://www.blackrockgalleries.com/",
	  "logo" : "https://www.blackrockgalleries.com/images/logo.png",
      "sameAs" : [ 
	 "https://www.facebook.com/blackrockgalleries",
  	 "https://twitter.com/BRGalleries",
         "https://www.instagram.com/blackrockgalleries/",
         "https://www.pinterest.com/brgalleries/" 
		] 
    },
    {
      "@type": "LocalBusiness",
      "parentOrganization": {
          "name" : "Black Rock Galleries"
      },
     "name" : "BRG - BRIDGEPORT HQ",
     "image" : "https://www.blackrockgalleries.com/images/logo.png",
     "priceRange" : "US$",
      "address": {
          "@type" : "PostalAddress",
          "streetAddress": "1720 Fairfield Avenue",
          "addressLocality": "Bridgeport",
          "addressRegion": "CT",
          "postalCode": "06605",
          "telephone" : "************"
          },
      "openingHours": [ "Mo-Sa 09:00-17:00"],
      "hasmap" : "https://www.google.com/maps?cid=6991964160784671497"
    },
    {
      "@type": "LocalBusiness",
      "parentOrganization": {
          "name" : "Black Rock Galleries"
      },
     "name" : "BRG - GREENWICH",
     "image" : "https://www.blackrockgalleries.com/images/logo.png",
     "priceRange" : "US$",
      "address": {
          "@type" : "PostalAddress",
          "streetAddress": "179 Hamilton Ave",
          "addressLocality": "Greenwich",
          "addressRegion": "CT",
          "postalCode": "06830",
          "telephone" : "************"
          },
      "openingHours": [ "Wednesday 10:00-16:00"],
      "hasmap" : "https://www.google.com/maps?cid=9254252607234140375"
    },
	{
      "@type": "LocalBusiness",
      "parentOrganization": {
          "name" : "Black Rock Galleries"
      },
     "name" : "BRG - BOSTON",
     "image" : "https://www.blackrockgalleries.com/images/logo.png",
     "priceRange" : "US$",
      "address": {
          "@type" : "PostalAddress",
          "streetAddress": "318 Bear Hill Road, Suite B-1 (basement level, rear of building)",
          "addressLocality": "Waltham",
          "addressRegion": "MA",
          "postalCode": "02451",
          "telephone" : "************"
          },
      "openingHours": [ "Wednesday 10:00-16:00"],
      "hasmap" : "https://www.google.com/maps?cid=3989129340440246843"
    },
 	{
      "@type": "LocalBusiness",
      "parentOrganization": {
          "name" : "Black Rock Galleries"
      },
     "name" : "BRG - RALEIGH",
     "image" : "https://www.blackrockgalleries.com/images/logo.png",
     "priceRange" : "US$",
      "address": {
          "@type" : "PostalAddress",
          "streetAddress": "215 James Jackson Avenue",
          "addressLocality": "Cary",
          "addressRegion": "NC",
          "postalCode": "02451",
          "telephone" : "27513"
          },
      "openingHours": [ "Wednesday 10:00-17:00"],
      "hasmap" : "https://www.google.com/maps?cid=4498528596784872668"
    }
  ]
}
</script>
      <div id="middlerwrapper"><div class="wrapper">
	  	<div class="about-who-main" style="margin-top:30px;">
        <div class="about-who-img-detail" style="width:100%;float:none;">
          <h1 class="about-who-title" style="text-align:center;">We're sorry...</h1>
          <p align="center" style="font-size:20px;text-align:center">The page you are looking for cannot be found.</p>
	<p style="min-height:250px;vertical-align:top;text-align:center"><a href="https://www.blackrockgalleries.com"  ><strong>Return to the homepage</strong></a></p>
        </div>
        <div class="clear"></div><div class="clear">&nbsp;</div><div class="clear">&nbsp;</div><div class="clear">&nbsp;</div><div class="clear">&nbsp;</div>
      </div>
	  
	  </div>
	  </div>
      <style>.anlogowidth{width:auto!important;height: 48px!important;margin-top:1px;}</style>
<div style="display:none">. Black Rock Galleries provides high end estate liquidation services through online estate auctions and a 50,000 sq ft designer showroom. estate liquidator, estate sale, online auction, antiques, fine art, liquidation, clean outs, auction, consignment, interior design, fine jewelry, collectibles, appraisals, estate appraisals, estate services, decor, furniture, furniture consignment, antique center, consignment shop. We offer appraisal services and specialize in fine art, jewelry, antiques and decorative arts. Sell with us. Black Rock Galleries is the tri-state's largest estate liquidator serving New England and the greater New York City area. We are the trusted source for expert appraisals, auction services, estate sales, and consignments. Black Rock Galleries is the east coast's largest and most trusted estate liquidator, with locations in Connecticut, greater Boston, MA and Raleigh, NC, Fairfield, Southport, Apex, TX. We are the best source for expert appraisals, auction services, estate sales and consignments. We have served thousands of families and estates with compassion, professionalism and expertise. BRG works with attorneys, executors, realtors and families to bring the greatest return on personal and estate property in the current market. Our unique combination of services make us the easy choice to get the job done in a timely and stress-free manner. We are with you from the initial consultation to the final clean out. Our house-built online auction software platform offers clients a dynamic approach to liquidating their personal property. This venue is most successful in bringing the best return with the least amount of disruption for our clients. See our services page for a complete description of this offering. </div>
<div id="footerwrapper"><div class="wrapper"><div class="footer-1" style="font-family:'loraregular';font-size:16px!important;" ><a  href="https://www.blackrockgalleries.com/"><img src="images/footer-logo.png" class="imgborder" title="Black Rock Galleries" alt="Black Rock Galleries" style="margin-bottom:10px;"></a><br />Authorized dealer on<a rel="nofollow" href="https://www.1stdibs.com/" target="_blank"><img src="image1.PNG" class="eximg1 ftrimgwidth imgborder" width="150" title="Black Rock Galleries" alt="Black Rock Galleries"></a></div><div class="footer-2"><h2 class="ftrHclass">HOME</h2> <ul><li><a  href="online-auctions" title="Black Rock Galleries" >Buy</a></li><li><a  href="our-services" title="Black Rock Galleries" >Sell</a></li><li><a  href="auctions" title="Black Rock Galleries" >Auctions</a></li><li><a  href="calendar" title="Black Rock Galleries" >Calendar</a></li><li><a  href="sitemap" title="Black Rock Galleries" >Sitemap</a></li></ul></div><div class="footer-2"><h3 class="ftrHclass">COMPANY</h3><ul><li><a  href="faqs" title="Black Rock Galleries" >FAQ</a></li><li><a  href="our-services" title="Black Rock Galleries" >Other Services</a></li><li><a  href="press" title="Black Rock Galleries" >Press</a></li><li><a  href="partners" title="Black Rock Galleries" >Partners</a></li><li><a  href="shipping" title="Black Rock Galleries" >Shipping</a></li><li><a  href="terms-conditions" title="Black Rock Galleries" >Terms & Conditions</a></li></ul></div><div class="footer-2"><h4 class="ftrHclass">CONNECT</h4><ul><li><a rel="nofollow"  href="https://www.facebook.com/blackrockgalleries" target="_blank" >Facebook</a></li><li><a rel="nofollow"  href="https://twitter.com/BRGalleries" target="_blank" >Twitter</a></li><li><a rel="nofollow"  href="http://www.pinterest.com/brgalleries" target="_blank" >Pinterest</a></li><li><a rel="nofollow" href="https://www.instagram.com/blackrockgalleries/" target="_blank" title="Black Rock Galleries" >Instagram</a></li><li><a  href="contact" title="Black Rock Galleries" >Contact Us</a></li></ul></div>
<div class="footer-3">
<h5>JOIN OUR MAILING LIST</h5><input maxlength="50" autocomplete="off" id="hom_email" name="hom_email" type="text" value="" placeholder="Email" class="textbox"><input onclick="return checkvalhom();return false;" type="button" class="join-btn" value="Join"><span class="exspam1" id="NEWLATr"></span>
<span class="copyrightx" style="font-family:'loraregular';font-size:16px!important;"><a rel="nofollow" href="https://www.auctionninja.com" target="_blank" class="exlink1" style="font-family:'loraregular';font-size:16px!important;">Powered by AuctionNinja</a><span> <a rel="nofollow" href="https://www.auctionninja.com" target="_blank"><img src="https://www.blackrockgalleries.com/AN_Standard_Logo.png"  title="Black Rock Galleries" alt="Black Rock Galleries" class="anlogowidth imgborder imgalign" /></a></span></span> </div><div class="footer-11 exlink1" style="font-family:'loraregular';font-size:16px!important;" >Authorized dealer on<a rel="nofollow" href="https://www.1stdibs.com/" target="_blank"><img src="image1.PNG" style="padding-left:20px;" class="eximg1 ftrimgwidth imgborder imgalign" width="150" title="Black Rock Galleries" alt="Black Rock Galleries"></a></div>

<div style="clear:both;text-align:center;color:#FFFFFF;font-size:17px;font-family:source_sans_probold;" class="ftrlocspace"><h6 class="ftrHclass">|| Our Locations ||</h2></div>
<div style="clear:both;text-align:center;color:#FFFFFF;font-size:17px;">Bridgeport, CT - <a href="https://www.google.com/maps?ll=41.171766,-73.215777&z=17&t=m&hl=en-US&gl=IN&mapclient=embed&daddr=1720+Fairfield+Ave+Bridgeport,+CT+06605+USA@41.1699084,-73.2148654" target="_blank" style="color:#FFFFFF;">Get Directions</a> &nbsp;|&nbsp; Greenwich, CT - <a href="https://www.google.com/maps/place/Black+Rock+Galleries/@41.0179193,-73.6391571,15z/data=!4m2!3m1!1s0x0:0x806db5b713ec68d7?sa=X&ved=1t:2428&ictx=111" target="_blank" style="color:#FFFFFF;">Get Directions</a> &nbsp;|&nbsp; Waltham, MA - <a href="https://www.google.com/maps/place/Black+Rock+Galleries/@35.7167661,-78.822146,15z/data=!4m6!3m5!1s0x89acf3f1b75ba2df:0x3e6dfbecaf4c7cdc!8m2!3d35.7167661!4d-78.822146!16s%2Fg%2F11fmrqzh9s?entry=ttu" target="_blank" style="color:#FFFFFF;">Get Directions</a> &nbsp;|&nbsp; Apex, NC - <a href="https://www.google.com/maps/place/Black+Rock+Galleries/@35.7167661,-78.822146,15z/data=!4m6!3m5!1s0x89acf3f1b75ba2df:0x3e6dfbecaf4c7cdc!8m2!3d35.7167661!4d-78.822146!16s%2Fg%2F11fmrqzh9s?entry=ttu" target="_blank" style="color:#FFFFFF;">Get Directions</a> &nbsp;|&nbsp; Fairfield, CT - <a href="https://www.google.com/maps/place/Black+Rock+Galleries/@41.1511801,-73.2413688,15z/data=!4m2!3m1!1s0x0:0x6108725bf5d52309?sa=X&ved=1t:2428&ictx=111" target="_blank" style="color:#FFFFFF;">Get Directions</a> &nbsp;|&nbsp; Southport, CT - <a href="https://www.google.com/maps/place/Black+Rock+Galleries/@41.1360553,-73.2840285,15z/data=!4m6!3m5!1s0x89e81bada269d29b:0xf2396ace1f38ef89!8m2!3d41.1360553!4d-73.2840285!16s%2Fg%2F11td3vhtj7?entry=ttu" style="color:#FFFFFF;">Get Directions</a></div>

</div></div><script>function checkvalhom()
{
	if(document.getElementById("hom_email").value=="" || document.getElementById("hom_email").value=="Email Address"){alert("Please enter your email address.");document.getElementById("hom_email").focus();return false;}
	else if(!(/[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}/igm.test(document.getElementById("hom_email").value))){alert("Please enter a proper email address.");document.getElementById("hom_email").focus();return false;}
	else{LoadAllColorsHOM();	return false;}
}
function LoadAllColorsHOM()
{
   email=document.getElementById("hom_email").value;
   var http3_Ftr = false; if(navigator.appName == "Microsoft Internet Explorer") { http3_Ftr = new ActiveXObject("Microsoft.XMLHTTP");} else { http3_Ftr = new XMLHttpRequest();}
	http3_Ftr.abort();
	http3_Ftr.open("GET","ajax_newlater.php?email="+email, true);
	http3_Ftr.onreadystatechange=function()
	{
	  if(http3_Ftr.readyState == 4)
	  {  
		  document.getElementById("NEWLATr").innerHTML=http3_Ftr.responseText;document.getElementById("hom_email").value="";return false;
	  } 
	}
	http3_Ftr.send(null);
}</script>    </div>
  </div>
</div>
<nav class="sidebar jsc-sidebar" id="jsi-nav" data-sidebar-options=""><ul class="sidebar-list"><li><a  href="https://www.blackrockgalleries.com/" title="Black Rock Galleries" >Home</a></li><li><a  href="about-us" title="Black Rock Galleries" >About</a></li><li><a  href="online-auctions" title="Black Rock Galleries" >Auctions</a></li><li><a  href="our-services" title="Black Rock Galleries" >Services</a></li><li><a  href="appraisals" title="Black Rock Galleries" >Appraisals</a></li><li><a  href="shop-now" title="Black Rock Galleries" >Store</a></li><li><a  href="blog" title="Black Rock Galleries" >Blog</a></li><li><a  href="contact" title="Black Rock Galleries" >Contact Us</a></li></ul></nav>
<script>$('#jsi-nav').sidebar({
	trigger: '.jsc-sidebar-trigger',
	scrollbarDisplay: true,
	pullCb: function () { console.log('pull'); },
	pushCb: function () { console.log('push'); }
});
$('#api-push').on('click', function (e) {
	e.preventDefault(); $('#jsi-nav').data('sidebar').push();
});
$('#api-pull').on('click', function (e) {
	e.preventDefault(); $('#jsi-nav').data('sidebar').pull();
});</script><script type="text/javascript" id="hs-script-loader" async defer src="//js.hs-scripts.com/8637804.js"></script>
<script>
(function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
   (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
})(window,document,'script','//www.google-analytics.com/analytics.js','ga');
ga('create', '***********-1', 'auto');
</script><script type="text/javascript">window.NREUM||(NREUM={});NREUM.info={"beacon":"bam.nr-data.net","licenseKey":"NRJS-77b9f90983038b4dd22","applicationID":"**********","transactionName":"NlUHYxRTWUIFUxAPWA8fMEUPHQMBUEAFAVJPQA1H","queueTime":0,"applicationTime":1,"atts":"GhIEFVxJSkw=","errorBeacon":"bam.nr-data.net","agent":""}</script></body>
</html>