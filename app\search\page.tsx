import { Suspense } from "react"
import { Filter } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { ProductFilters } from "@/components/product-filters"
import { ProductSort } from "@/components/product-sort"
import { Pagination } from "@/components/pagination"
import { SearchResults } from "@/components/search-results"

export default function SearchPage({
  searchParams,
}: {
  searchParams: { q: string }
}) {
  const query = searchParams.q || ""

  return (
    <div className="flex flex-col min-h-screen">
      <main className="flex-1">
        <div className="container py-8">
          <h1 className="text-2xl font-bold tracking-tight mb-4">Search Results for "{query}"</h1>

          <div className="bg-muted/40 rounded-lg p-4 mb-8">
            <div className="flex items-center gap-2">
              <div className="relative flex-1">
                <form action="/search" method="GET" className="flex gap-2">
                  <input
                    type="search"
                    name="q"
                    placeholder="Search products..."
                    defaultValue={query}
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  />
                  <Button type="submit">Search</Button>
                </form>
              </div>
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="outline" size="icon" className="flex md:hidden">
                    <Filter className="h-4 w-4" />
                    <span className="sr-only">Filter</span>
                  </Button>
                </SheetTrigger>
                <SheetContent side="left" className="w-[300px] sm:w-[400px]">
                  <div className="px-1">
                    <h2 className="text-lg font-semibold mb-4">Filters</h2>
                    <ProductFilters className="mt-4" />
                  </div>
                </SheetContent>
              </Sheet>
              <ProductSort />
            </div>
          </div>

          <div className="flex-1">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="hidden md:block">
                <div className="sticky top-20">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="font-semibold text-lg">Filters</h2>
                    <Button variant="ghost" size="sm" className="h-8 text-xs">
                      Reset All
                    </Button>
                  </div>
                  <ProductFilters />
                </div>
              </div>
              <div className="md:col-span-3">
                <Suspense fallback={<div>Loading search results...</div>}>
                  <SearchResults query={query} />
                </Suspense>
                <Pagination className="mt-12" />
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}

