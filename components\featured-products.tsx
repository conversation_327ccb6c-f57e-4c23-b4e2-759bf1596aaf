import { ChevronRight } from "lucide-react"
import Link from "next/link"

import { ProductCard } from "@/components/product-card"

export function FeaturedProducts() {
  const products = [
    {
      id: 1,
      title: "Dove Nourishing Body Wash with Deep Moisture",
      price: 110000,
      currency: "UGX",
      image: "/placeholder.svg?height=300&width=300",
      discount: 15,
      rating: 4.8,
      href: "/products/dove-body-wash",
      badge: "Best Seller",
    },
    {
      id: 2,
      title: "Suave Essentials Gentle Body Wash Ocean Breeze",
      price: 145000,
      currency: "UGX",
      image: "/placeholder.svg?height=300&width=300",
      discount: 0,
      rating: 4.2,
      href: "/products/suave-essentials",
      badge: "New",
    },
    {
      id: 3,
      title: "Cantu Shea Butter Tea Tree Oil Leave-In Conditioner",
      price: 140000,
      currency: "UGX",
      image: "/placeholder.svg?height=300&width=300",
      discount: 20,
      rating: 4.5,
      href: "/products/cantu-body-wash",
      badge: "Hot",
    },
    {
      id: 4,
      title: "Wild Cherry Blossom Body Wash with Vitamin E",
      price: 160000,
      currency: "UGX",
      image: "/placeholder.svg?height=300&width=300",
      discount: 0,
      rating: 4.7,
      href: "/products/wild-cherry",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">Featured Products</h2>
        <Link href="/featured" className="flex items-center text-sm font-medium text-primary">
          View All
          <ChevronRight className="ml-1 h-4 w-4" />
        </Link>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
        {products.map((product) => (
          <ProductCard
            key={product.id}
            title={product.title}
            price={product.price}
            currency={product.currency}
            image={product.image}
            discount={product.discount}
            rating={product.rating}
            href={product.href}
            badge={product.badge}
          />
        ))}
      </div>
    </div>
  )
}

